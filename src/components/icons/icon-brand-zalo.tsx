import React from 'react';

interface IconBrandZaloProps {
  size?: number;
  className?: string;
}

export const IconBrandZalo: React.FC<IconBrandZaloProps> = ({ size = 24, className = "" }) => {
  return (
    <svg 
      width={size} 
      height={size} 
      viewBox="0 0 24 24" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path 
        d="M12 2C6.477 2 2 6.477 2 12C2 17.523 6.477 22 12 22C17.523 22 22 17.523 22 12C22 6.477 17.523 2 12 2Z" 
        fill="currentColor" 
      />
      <path 
        d="M17.5 14.5L15.5 10.5H13.5L15.5 14.5H13.5L11.5 10.5H9.5L11.5 14.5H9.5L7.5 10.5H5.5L7.5 14.5H6.5V16.5H18.5V14.5H17.5Z" 
        fill="white" 
      />
    </svg>
  );
};

export default IconBrandZalo;
