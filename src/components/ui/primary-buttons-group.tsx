import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

interface PrimaryButtonConfig {
  label: string
  icon?: React.ComponentType<{ className?: string }>
  onClick: () => void
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive'
  className?: string
}

interface PrimaryButtonsGroupProps {
  buttons: PrimaryButtonConfig[]
  className?: string
  buttonClassName?: string
}

export function PrimaryButtonsGroup({
  buttons,
  className,
  buttonClassName,
}: PrimaryButtonsGroupProps) {
  return (
    <div className={cn('flex items-center gap-2', className)}>
      {buttons.map((button, index) => {
        const Icon = button.icon
        return (
          <Button
            key={index}
            variant={button.variant || 'default'}
            onClick={button.onClick}
            className={cn('gap-2', buttonClassName, button.className)}
          >
            {Icon && <Icon className="h-4 w-4" />}
            <span>{button.label}</span>
          </Button>
        )
      })}
    </div>
  )
}
