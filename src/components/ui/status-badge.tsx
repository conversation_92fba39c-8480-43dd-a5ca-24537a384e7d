"use client"

import { cn } from '@/lib/utils';

type StatusBadgeProps = {
  status: string;
  variant?: 'done' | 'inProgress' | 'default' | 'cancelled' | 'todo' | 'warning' | 'success' | 'destructive';
  className?: string;
};

const StatusBadge = ({
  status,
  variant = 'default',
  className,
}: StatusBadgeProps) => {
  // Hàm chuyển đổi tên trạng thái
  const getStatusName = (status: string) => {
    const statusNames: Record<string, string> = {
      'new': 'Mới',
      'contacting': '<PERSON><PERSON> liên hệ',
      'qualified': '<PERSON><PERSON> điều kiện',
      'success': 'Thành công',
      'failed': 'Th<PERSON>t bại',
      // Tương thích ngược
      'contacted': '<PERSON><PERSON> liên hệ',
      'nurture': '<PERSON>ang nuôi dưỡng',
      'unqualified': '<PERSON>hông phù hợp'
    };
    return statusNames[status.toLowerCase()] || status;
  };

  const getStatusIcon = () => {
    switch (variant) {
      case 'done':
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="mr-1 h-3.5 w-3.5 text-green-600 dark:text-green-400"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round">
            <circle cx="12" cy="12" r="10" />
            <path d="m9 12 2 2 4-4" />
          </svg>
        );
      case 'inProgress':
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="mr-1 h-3.5 w-3.5 text-blue-600 dark:text-blue-400"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round">
            <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z" />
            <path d="M12 6v6l4 2" />
          </svg>
        );
      case 'warning':
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="mr-1 h-3.5 w-3.5 text-amber-600 dark:text-amber-400"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round">
            <circle cx="12" cy="12" r="10" />
            <path d="M12 8v4M12 16h.01" />
          </svg>
        );
      case 'success':
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="mr-1 h-3.5 w-3.5 text-emerald-600 dark:text-emerald-400"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round">
            <circle cx="12" cy="12" r="10" />
            <path d="m9 12 2 2 4-4" />
          </svg>
        );
      case 'destructive':
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="mr-1 h-3.5 w-3.5 text-red-600 dark:text-red-400"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round">
            <circle cx="12" cy="12" r="10" />
            <path d="M15 9l-6 6M9 9l6 6" />
          </svg>
        );
      case 'cancelled':
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="mr-1 h-3.5 w-3.5 text-red-600 dark:text-red-400"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round">
            <circle cx="12" cy="12" r="10" />
            <path d="M15 9l-6 6M9 9l6 6" />
          </svg>
        );
      case 'todo':
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="mr-1 h-3.5 w-3.5 text-blue-600 dark:text-blue-400"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round">
            <circle cx="12" cy="12" r="10" />
            <path d="M12 8v4M12 16h.01" />
          </svg>
        );
      default:
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="mr-1 h-3.5 w-3.5 text-orange-600 dark:text-orange-400"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round">
            <circle cx="12" cy="12" r="10" />
            <path d="M12 16h.01" />
          </svg>
        );
    }
  };

  const getColorClass = () => {
    switch (variant) {
      case 'done':
        return 'text-green-700 dark:text-green-300';
      case 'cancelled':
        return 'text-red-700 dark:text-red-300';
      case 'todo':
        return 'text-blue-700 dark:text-blue-300';
      case 'inProgress':
        return 'text-blue-700 dark:text-blue-300';
      case 'warning':
        return 'text-amber-700 dark:text-amber-300';
      case 'success':
        return 'text-emerald-700 dark:text-emerald-300';
      case 'destructive':
        return 'text-red-700 dark:text-red-300';
      default:
        return 'text-muted-foreground';
    }
  };

  return (
    <div
      className={cn(
        'flex h-7 items-center justify-start rounded-full border border-border bg-background py-0 px-2 text-[11px] font-medium',
        getColorClass(),
        className
      )}
    >
      {getStatusIcon()}
      <span className="truncate">{getStatusName(status)}</span>
    </div>
  );
};

export { StatusBadge };