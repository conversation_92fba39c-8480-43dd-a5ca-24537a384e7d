import { createContext, useContext, forwardRef } from 'react'
import { cn } from '@/lib/utils'

type StepperState = 'completed' | 'active' | 'inactive'

interface StepperContextValue {
  currentStep: number
  orientation?: 'horizontal' | 'vertical'
}

const StepperContext = createContext<StepperContextValue | null>(null)

interface StepperProps extends React.HTMLAttributes<HTMLDivElement> {
  currentStep?: number
  orientation?: 'horizontal' | 'vertical'
}

const Stepper = forwardRef<HTMLDivElement, StepperProps>(
  ({ className, currentStep = 1, orientation = 'horizontal', ...props }, ref) => {
    return (
      <StepperContext.Provider value={{ currentStep, orientation }}>
        <div
          ref={ref}
          className={cn(
            'flex',
            orientation === 'horizontal' ? 'w-full items-start gap-2' : 'flex-col items-start gap-4',
            className
          )}
          {...props}
        />
      </StepperContext.Provider>
    )
  }
)
Stepper.displayName = 'Stepper'

interface StepperItemProps extends React.HTMLAttributes<HTMLDivElement> {
  step: number
  children: (props: { state: StepperState }) => React.ReactNode
}

const StepperItem = forwardRef<HTMLDivElement, StepperItemProps>(
  ({ className, step, children, ...props }, ref) => {
    const context = useContext(StepperContext)
    if (!context) throw new Error('StepperItem must be used within Stepper')

    const { currentStep } = context
    
    let state: StepperState = 'inactive'
    if (step < currentStep) {
      state = 'completed'
    } else if (step === currentStep) {
      state = 'active'
    }

    return (
      <div
        ref={ref}
        className={cn('relative flex w-full flex-col items-center justify-center group', className)}
        data-state={state}
        {...props}
      >
        {children({ state })}
      </div>
    )
  }
)
StepperItem.displayName = 'StepperItem'

interface StepperSeparatorProps extends React.HTMLAttributes<HTMLDivElement> {}

const StepperSeparator = forwardRef<HTMLDivElement, StepperSeparatorProps>(
  ({ className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          'absolute left-[calc(50%+20px)] right-[calc(-50%+10px)] top-5 block h-0.5 shrink-0 rounded-full bg-border group-data-[state=completed]:bg-primary',
          className
        )}
        {...props}
      />
    )
  }
)
StepperSeparator.displayName = 'StepperSeparator'

const StepperTrigger = forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn('', className)} {...props} />
))
StepperTrigger.displayName = 'StepperTrigger'

const StepperTitle = forwardRef<
  HTMLHeadingElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn('text-sm font-semibold transition lg:text-base', className)}
    {...props}
  />
))
StepperTitle.displayName = 'StepperTitle'

const StepperDescription = forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn('text-xs text-muted-foreground transition md:text-sm', className)}
    {...props}
  />
))
StepperDescription.displayName = 'StepperDescription'

export {
  Stepper,
  StepperItem,
  StepperSeparator,
  StepperTrigger,
  StepperTitle,
  StepperDescription,
}
