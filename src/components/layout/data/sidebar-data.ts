import {
  IconChecklist,
  IconLayoutDashboard,
  IconMessages,
  IconAddressBook,
  IconUserCircle,
  IconBuildingStore,
  IconShoppingCart,
  IconMailbox,
  IconMail,
  IconBox,
} from '@tabler/icons-react'
import { AudioWaveform, Command, GalleryVerticalEnd } from 'lucide-react'
import { type SidebarData } from '../types'

export const sidebarData: SidebarData = {
  user: {
    name: 'satnaing',
    email: '<EMAIL>',
    avatar: '/avatars/shadcn.jpg',
  },
  teams: [
    {
      name: 'Shadcn Admin',
      logo: Command,
      plan: 'Vite + ShadcnUI',
    },
    {
      name: 'Acme Inc',
      logo: GalleryVerticalEnd,
      plan: 'Enterprise',
    },
    {
      name: 'Acme Corp.',
      logo: AudioWaveform,
      plan: 'Startup',
    },
  ],
  navGroups: [
    {
      title: 'General',
      items: [
        {
          title: 'Dashboard',
          url: '/',
          icon: IconLayoutDashboard,
        },
        {
          title: 'Chat',
          url: '/chats/chat',
          icon: IconMessages,
          badge: '3',
        },
        {
          title: 'Task',
          url: '/tasks',
          icon: IconChecklist,
        },
      ],
    },
    {
      title: 'Sales',
      items: [
        {
          title: 'Leads',
          url: '/leads',
          icon: IconAddressBook,
        },
        {
          title: 'Deals',
          url: '/deals',
          icon: IconBuildingStore,
        },
        {
          title: 'Accounts',
          url: '/accounts',
          icon: IconUserCircle,
        },
      ],
    },
    {
      title: 'Marketing',
      items: [
        {
          title: 'Campaigns',
          url: '/campaigns',
          icon: IconMailbox,
        },
        {
          title: 'Email Templates',
          url: '/email-templates',
          icon: IconMail,
        },
      ],
    },
    {
      title: 'Commerce',
      items: [
        {
          title: 'Orders',
          url: '/orders',
          icon: IconShoppingCart,
        },
        {
          title: 'Products',
          url: '/products',
          icon: IconBox,
        },
      ],
    },
  ],
}
