import { ReactNode } from 'react'
import { Link, useLocation } from '@tanstack/react-router'
import { ChevronRight } from 'lucide-react'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from '@/components/ui/sidebar'
import { Badge } from '../ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu'
import { NavCollapsible, NavLink, type NavGroup } from './types'

export function NavGroup({ title, items }: NavGroup) {
  const { state } = useSidebar()
  const href = useLocation({ select: (location) => location.href })
  return (
    <SidebarGroup>
      <SidebarGroupLabel>{title}</SidebarGroupLabel>
      <SidebarMenu>
        {items.map((item) => {
          const key = `${item.title}-${item.url}`

          if (!item.items)
            return <SidebarMenuLink key={key} item={item} href={href} />

          if (state === 'collapsed')
            return (
              <SidebarMenuCollapsedDropdown key={key} item={item} href={href} />
            )

          return <SidebarMenuCollapsible key={key} item={item} href={href} />
        })}
      </SidebarMenu>
    </SidebarGroup>
  )
}

const NavBadge = ({ children }: { children: ReactNode }) => (
  <Badge className='rounded-full px-1 py-0 text-xs'>{children}</Badge>
)

const SidebarMenuLink = ({ item, href }: { item: NavLink; href: string }) => {
  const { setOpenMobile } = useSidebar()
  
  // Loại bỏ phần hashtag từ URL hiện tại
  const currentPathWithoutHash = href.split('#')[0];
  
  // Loại bỏ phần hashtag từ URL của item
  const itemPathWithoutHash = typeof item.url === 'string' 
    ? (item.url as string).split('#')[0] 
    : item.url;
  
  // Kiểm tra xem URL hiện tại (không có hashtag) có khớp với URL của item không
  // Hoặc kiểm tra xem URL hiện tại có bắt đầu bằng URL của item không (cho các trang con)
  const isCurrentPage = currentPathWithoutHash === itemPathWithoutHash || 
                        (currentPathWithoutHash.startsWith(itemPathWithoutHash + '/') && itemPathWithoutHash !== '/');
  
  return (
    <SidebarMenuItem>
      <SidebarMenuButton
        asChild
        isActive={isCurrentPage}
        tooltip={item.title}
      >
        <Link to={item.url} onClick={() => setOpenMobile(false)}>
          {item.icon && <item.icon />}
          <span>{item.title}</span>
          {item.badge && <NavBadge>{item.badge}</NavBadge>}
        </Link>
      </SidebarMenuButton>
    </SidebarMenuItem>
  )
}

const SidebarMenuCollapsible = ({
  item,
  href,
}: {
  item: NavCollapsible
  href: string
}) => {
  const { setOpenMobile } = useSidebar()
  // Loại bỏ phần hashtag từ URL hiện tại
  const currentPathWithoutHash = href.split('#')[0];
  
  // Kiểm tra xem có item con nào mà URL của nó (không có hashtag) khớp với URL hiện tại không
  // Hoặc kiểm tra xem URL hiện tại có bắt đầu bằng URL của item con không
  const exactItemMatch = item.items.some(subItem => {
    const subItemPath = typeof subItem.url === 'string' 
      ? (subItem.url as string).split('#')[0] 
      : subItem.url;
    return currentPathWithoutHash === subItemPath || 
           (currentPathWithoutHash.startsWith(subItemPath + '/') && subItemPath !== '/');
  });
  
  return (
    <Collapsible
      asChild
      defaultOpen={exactItemMatch}
      className='group/collapsible'
    >
      <SidebarMenuItem>
        <CollapsibleTrigger asChild>
          <SidebarMenuButton tooltip={item.title} isActive={exactItemMatch}>
            {item.icon && <item.icon />}
            <span>{item.title}</span>
            {item.badge && <NavBadge>{item.badge}</NavBadge>}
            <ChevronRight className='ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90' />
          </SidebarMenuButton>
        </CollapsibleTrigger>
        <CollapsibleContent className='CollapsibleContent'>
          <SidebarMenuSub>
            {item.items.map((subItem) => {
              // Loại bỏ phần hashtag từ URL của subItem
              const subItemPath = typeof subItem.url === 'string' 
                ? (subItem.url as string).split('#')[0] 
                : subItem.url;
              // Kiểm tra xem URL hiện tại (không có hashtag) có khớp với URL của subItem không
              const isCurrentPage = currentPathWithoutHash === subItemPath;
              
              return (
                <SidebarMenuSubItem key={subItem.title}>
                  <SidebarMenuSubButton
                    asChild
                    isActive={isCurrentPage}
                  >
                    <Link to={subItem.url} onClick={() => setOpenMobile(false)}>
                      {subItem.icon && <subItem.icon />}
                      <span>{subItem.title}</span>
                      {subItem.badge && <NavBadge>{subItem.badge}</NavBadge>}
                    </Link>
                  </SidebarMenuSubButton>
                </SidebarMenuSubItem>
              );
            })}
          </SidebarMenuSub>
        </CollapsibleContent>
      </SidebarMenuItem>
    </Collapsible>
  )
}

const SidebarMenuCollapsedDropdown = ({
  item,
  href,
}: {
  item: NavCollapsible
  href: string
}) => {
  // Loại bỏ phần hashtag từ URL hiện tại
  const currentPathWithoutHash = href.split('#')[0];
  
  // Kiểm tra xem có item con nào mà URL của nó (không có hashtag) khớp với URL hiện tại không
  // Hoặc kiểm tra xem URL hiện tại có bắt đầu bằng URL của item con không
  const exactItemMatch = item.items.some(subItem => {
    const subItemPath = typeof subItem.url === 'string' 
      ? (subItem.url as string).split('#')[0] 
      : subItem.url;
    return currentPathWithoutHash === subItemPath || 
           (currentPathWithoutHash.startsWith(subItemPath + '/') && subItemPath !== '/');
  });
  
  return (
    <SidebarMenuItem>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <SidebarMenuButton
            tooltip={item.title}
            isActive={exactItemMatch}
          >
            {item.icon && <item.icon />}
            <span>{item.title}</span>
            {item.badge && <NavBadge>{item.badge}</NavBadge>}
            <ChevronRight className='ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90' />
          </SidebarMenuButton>
        </DropdownMenuTrigger>
        <DropdownMenuContent side='right' align='start' sideOffset={4}>
          <DropdownMenuLabel>
            {item.title} {item.badge ? `(${item.badge})` : ''}
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          {item.items.map((sub) => {
            // Loại bỏ phần hashtag từ URL của subItem
            const subItemPath = typeof sub.url === 'string' 
              ? (sub.url as string).split('#')[0] 
              : sub.url;
            // Kiểm tra xem URL hiện tại (không có hashtag) có khớp với URL của subItem không
            const isCurrentPage = currentPathWithoutHash === subItemPath;
            
            return (
              <DropdownMenuItem key={`${sub.title}-${sub.url}`} asChild>
                <Link
                  to={sub.url}
                  className={`${isCurrentPage ? 'bg-secondary' : ''} hover:bg-secondary transition-colors`}
                >
                  {sub.icon && <sub.icon />}
                  <span className='max-w-52 text-wrap'>{sub.title}</span>
                  {sub.badge && (
                    <span className='ml-auto text-xs'>{sub.badge}</span>
                  )}
                </Link>
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    </SidebarMenuItem>
  )
}
