import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { DealsDialogs } from './components/deals-dialogs'
import { DealsView } from './components/deals-view'
import DealsProvider from './context/deals-context'
import { useDealsContext } from './context/use-deals-context'
import { dealListSchema } from './data/schema'
import { deals } from './data/deals'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { formatCurrency } from './data/data'
import { DealStage } from './data/schema'
import { Button } from '@/components/ui/button'
import { IconPlus } from '@tabler/icons-react'
import { ViewModeToggle } from './components/view-mode-toggle'
import { FilterToggleButton } from './components/filter-toggle-button'

// Tạo component DealsContent để có thể sử dụng useDealsContext hook
function DealsContent() {
  const { deals, viewMode, setViewMode, setOpenDialog } = useDealsContext()

  const totalValue = deals.reduce((sum, d) => sum + d.value, 0)
  const totalWon = deals.filter(d => d.stage === DealStage.CLOSED_WON).length
  const totalLost = deals.filter(d => d.stage === DealStage.CLOSED_LOST).length

  return (
    <>
      <Header fixed>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='mb-2 flex flex-wrap items-center justify-between space-y-2'>
          <div>
            <h2 className='text-2xl font-bold tracking-tight'>Deals</h2>
            <p className='text-muted-foreground'>
              Quản lý và theo dõi các cơ hội kinh doanh của bạn.
            </p>
          </div>
          <div className="inline-flex items-center gap-2">
            <ViewModeToggle viewMode={viewMode} setViewMode={setViewMode} />
            <FilterToggleButton />
            <Button variant="default" className="gap-1" onClick={() => setOpenDialog('createDeal')}>
              <IconPlus className="h-4 w-4" />
              <span>Tạo Deal</span>
            </Button>
          </div>
        </div>

        {/* Dashboard tổng quan */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Tổng số Deals</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{deals.length}</div>
              <p className="text-xs text-muted-foreground mt-1">Tổng số cơ hội</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Tổng giá trị</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(totalValue)}</div>
              <p className="text-xs text-muted-foreground mt-1">Tổng giá trị gốc</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Chốt thành công</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalWon}</div>
              <p className="text-xs text-muted-foreground mt-1">Cơ hội thành công</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Chốt thất bại</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalLost}</div>
              <p className="text-xs text-muted-foreground mt-1">Cơ hội thất bại</p>
            </CardContent>
          </Card>
        </div>

        <DealsView data={deals} />
      </Main>

      <DealsDialogs />
    </>
  )
}

export default function Deals() {
  // Parse deal list
  const dealList = dealListSchema.parse(deals)

  return (
    <DealsProvider initialDeals={dealList}>
      <DealsContent />
    </DealsProvider>
  )
} 