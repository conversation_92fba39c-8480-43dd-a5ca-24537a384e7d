import {
  useState,
  type ReactNode,
} from 'react'
import { DealsContext, ViewMode } from './deals-context-type'
import { Deal } from '../data/schema'

interface DealsProviderProps {
  children: ReactNode
  initialDeals: Deal[]
}

export default function DealsProvider({
  children,
  initialDeals,
}: DealsProviderProps) {
  const [openDialog, setOpenDialog] = useState('')
  const [selectedDealIds, setSelectedDealIds] = useState<string[]>([])
  const [viewMode, setViewMode] = useState<ViewMode>('table')
  const [showFilterPanel, setShowFilterPanel] = useState(false)
  const [deals, setDeals] = useState<Deal[]>(initialDeals)

  const updateDealStage = (dealId: string, newStage: string) => {
    setDeals((prevDeals) =>
      prevDeals.map((deal) =>
        deal.id === dealId ? { ...deal, stage: newStage } : deal
      )
    )
  }

  const updateDealTags = (dealId: string, tags: string[]) => {
    setDeals((prevDeals) =>
      prevDeals.map((deal) =>
        deal.id === dealId ? { ...deal, tags } : deal
      )
    )
  }

  return (
    <DealsContext.Provider
      value={{
        openDialog,
        setOpenDialog,
        selectedDealIds,
        setSelectedDealIds,
        viewMode,
        setViewMode,
        showFilterPanel,
        setShowFilterPanel,
        deals,
        setDeals,
        updateDealStage,
        updateDealTags,
      }}
    >
      {children}
    </DealsContext.Provider>
  )
} 