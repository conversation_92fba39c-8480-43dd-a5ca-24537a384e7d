import { createContext } from 'react'
import { Deal } from '../data/schema'

export type ViewMode = 'table' | 'pipeline' | 'calendar' | 'analytics'

export type DealsContextType = {
  openDialog: string
  setOpenDialog: (dialog: string) => void
  selectedDealIds: string[]
  setSelectedDealIds: (ids: string[]) => void
  viewMode: ViewMode
  setViewMode: (mode: ViewMode) => void
  showFilterPanel: boolean
  setShowFilterPanel: (show: boolean) => void
  deals: Deal[]
  setDeals: (deals: Deal[]) => void
  updateDealStage: (dealId: string, newStage: string) => void
  updateDealTags: (dealId: string, tags: string[]) => void
}

export const DealsContext = createContext<DealsContextType | null>(null) 