import { Button } from '@/components/ui/button'
import { 
  Stepper, 
  StepperItem, 
  StepperSeparator, 
  StepperTrigger, 
  StepperTitle, 
  StepperDescription 
} from '@/components/ui/stepper'
import { cn } from '@/lib/utils'
import { Circle, Phone, Star, FileText } from 'lucide-react'
import { Deal, DealStage } from '../data/schema'

interface SalesPathForDealProps {
  deal: Deal
  onStageChange?: (stage: string) => void
}

// Các giai đoạn trong deal pipeline
const stages = [
  { 
    value: DealStage.PROSPECTING, 
    label: 'Tìm hiểu', 
    icon: Circle,
    description: 'Tìm hiểu nhu cầu khách hàng'
  },
  { 
    value: DealStage.QUALIFICATION, 
    label: 'Đủ điều kiện', 
    icon: Phone,
    description: '<PERSON><PERSON><PERSON> định khả năng mua'
  },
  { 
    value: DealStage.PROPOSAL, 
    label: '<PERSON><PERSON> xuất', 
    icon: Star,
    description: '<PERSON><PERSON><PERSON> báo gi<PERSON> và đề xuất'
  },
  { 
    value: DealStage.NEGOTIATION, 
    label: 'Đà<PERSON> phán', 
    icon: FileText,
    description: 'Thương lượng điều khoản'
  }
]

export function SalesPathForDeal({ deal, onStageChange }: SalesPathForDealProps) {
  const currentStageIndex = stages.findIndex(s => s.value === deal.stage)
  const currentStep = currentStageIndex + 1

  const handleStageChange = (stage: string) => {
    if (onStageChange) {
      onStageChange(stage)
    }
  }
  
  return (
    <div className="space-y-4">
      <div className="text-sm font-medium text-muted-foreground">
        Tiến trình Deal
      </div>
      
      <Stepper currentStep={currentStep} className="flex w-full items-start gap-2">
        {stages.map((stage, index) => {
          const stepNumber = index + 1
          const Icon = stage.icon
          
          return (
            <StepperItem
              key={stage.value}
              step={stepNumber}
              className="relative flex w-full flex-col items-center justify-center"
            >
              {({ state }) => (
                <>
                  {index < stages.length - 1 && (
                    <StepperSeparator />
                  )}
                  
                  <StepperTrigger>
                    <Button
                      variant={state === 'completed' || state === 'active' ? 'default' : 'outline'}
                      size="icon"
                      className={cn(
                        'z-10 rounded-full shrink-0',
                        onStageChange && 'cursor-pointer hover:opacity-80'
                      )}
                      onClick={() => handleStageChange(stage.value)}
                      disabled={!onStageChange}
                    >
                      <Icon className="h-5 w-5" />
                    </Button>
                  </StepperTrigger>
                  
                  <div className="mt-5 flex flex-col items-center text-center">
                    <StepperTitle
                      className={cn(
                        'text-sm font-semibold transition lg:text-base',
                        state === 'active' && 'text-primary'
                      )}
                    >
                      {stage.label}
                    </StepperTitle>
                    <StepperDescription
                      className={cn(
                        'text-xs text-muted-foreground transition md:text-sm',
                        state === 'active' && 'text-primary'
                      )}
                    >
                      {stage.description}
                    </StepperDescription>
                  </div>
                </>
              )}
            </StepperItem>
          )
        })}
      </Stepper>
      
      {/* Status summary */}
      <div className="flex items-center justify-between text-sm text-muted-foreground bg-muted/50 rounded-lg p-3">
        <div className="flex items-center gap-2">
          <div className={cn(
            'w-2 h-2 rounded-full',
            deal.stage === DealStage.PROSPECTING && 'bg-blue-500',
            deal.stage === DealStage.QUALIFICATION && 'bg-orange-500',
            deal.stage === DealStage.PROPOSAL && 'bg-green-500',
            deal.stage === DealStage.NEGOTIATION && 'bg-purple-500'
          )} />
          <span>
            Giai đoạn hiện tại: <strong>{stages.find(s => s.value === deal.stage)?.label}</strong>
          </span>
        </div>
        
        <div className="flex items-center gap-4">
          <span>Xác suất: <strong>{deal.probability}%</strong></span>
          <span>Giá trị: <strong>{deal.value.toLocaleString('vi-VN')} VNĐ</strong></span>
        </div>
      </div>
    </div>
  )
}
