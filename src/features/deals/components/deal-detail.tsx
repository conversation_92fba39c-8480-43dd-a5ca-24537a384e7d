import { useState } from 'react'
import { <PERSON> } from '@tanstack/react-router'
import { useDealsContext } from '../context/use-deals-context'
import {
  Mail,
  Pencil,
  Calendar,
  Phone,
  MessageSquare,
  Building,
  Info,
  Plus,
  CheckCircle2,
  Star,
  MoreVertical,
  ArrowLeftRight,
  Activity,
  FileText,
  ListTodo,
  Users,
  CalendarClock,
  DollarSign,
  Send,
  Trash,
  Package,
  Clock,
  MapPin,
  Briefcase,
  Globe,
  Bold,
  Italic,
  List
} from 'lucide-react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui/tooltip'
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card'
import { Breadcrumb, BreadcrumbList, BreadcrumbItem, BreadcrumbLink, BreadcrumbSeparator, BreadcrumbPage } from '@/components/ui/breadcrumb'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { type Deal } from '../data/schema'
import { SalesPathForDeal } from './sales-path-for-deal'

interface DealDetailProps {
  deal: Deal
}function DealDetail({ deal }: DealDetailProps) {
  const { deals, setOpenDialog, setSelectedDealIds, updateDealStage } = useDealsContext()
  const [activeTab, setActiveTab] = useState('overview')

  // Find the deal in context to ensure it's up to date
  const currentDeal = deals.find(d => d.id === deal.id) || deal

  const handleEdit = () => {
    setSelectedDealIds([currentDeal.id])
    setOpenDialog('editDeal')
  }

  const handleDelete = () => {
    setSelectedDealIds([currentDeal.id])
    setOpenDialog('deleteDeal')
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleStageChange = (newStage: string) => {
    updateDealStage(currentDeal.id, newStage)
  }

  // Tạo avatar fallback từ tên
  const getInitials = (name: string) => {
    if (!name) return "NA"
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
  }

  const getStatusBadge = (stage: string) => {
    const stageMap: Record<string, { color: string, icon: React.ReactNode, bg: string }> = {
      'prospecting': {
        color: 'text-blue-700',
        bg: 'bg-blue-50',
        icon: <Info className="h-3.5 w-3.5 mr-1" />
      },
      'qualification': {
        color: 'text-orange-700',
        bg: 'bg-orange-50',
        icon: <CheckCircle2 className="h-3.5 w-3.5 mr-1" />
      },
      'proposal': {
        color: 'text-green-700',
        bg: 'bg-green-50',
        icon: <FileText className="h-3.5 w-3.5 mr-1" />
      },
      'negotiation': {
        color: 'text-purple-700',
        bg: 'bg-purple-50',
        icon: <MessageSquare className="h-3.5 w-3.5 mr-1" />
      },
      'closed_won': {
        color: 'text-emerald-700',
        bg: 'bg-emerald-50',
        icon: <CheckCircle2 className="h-3.5 w-3.5 mr-1" />
      },
      'closed_lost': {
        color: 'text-red-700',
        bg: 'bg-red-50',
        icon: <Info className="h-3.5 w-3.5 mr-1" />
      }
    }

    if (!stage) return null

    const { color, icon, bg } = stageMap[stage] || {
      color: 'text-gray-700',
      bg: 'bg-gray-50',
      icon: null
    }

    return (
      <Badge variant="outline" className={`flex items-center gap-1 px-2 py-1 ${bg} ${color} border-transparent`}>
        {icon}
        <span className="capitalize font-medium">{stage.replace('_', ' ')}</span>
      </Badge>
    )
  }

  const getPriorityBadge = (priority: string | undefined) => {
    if (!priority) return null

    const priorityMap: Record<string, { color: string, bg: string }> = {
      'high': {
        color: 'text-red-700',
        bg: 'bg-red-50'
      },
      'medium': {
        color: 'text-orange-700',
        bg: 'bg-orange-50'
      },
      'low': {
        color: 'text-green-700',
        bg: 'bg-green-50'
      }
    }

    const { color, bg } = priorityMap[priority] || {
      color: 'text-gray-700',
      bg: 'bg-gray-50'
    }

    return (
      <Badge variant="outline" className={`flex items-center gap-1 px-2 py-1 ${bg} ${color} border-transparent`}>
        <Star className="h-3 w-3 mr-1" />
        <span className="capitalize font-medium">{priority}</span>
      </Badge>
    )
  }

  return (
    <>
      <Header fixed>
        <Breadcrumb>
          <BreadcrumbList className="mb-0">
            <BreadcrumbItem>
              <BreadcrumbLink asChild><Link to="/deals" className="text-sm">Deals</Link></BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-sm">{currentDeal.name}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </Header>
      <Main>
        <div className="container py-6 px-4 max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="flex items-center gap-3">
                <Avatar className="h-12 w-12 border-2 border-background shadow-sm">
                  <AvatarFallback className="bg-primary/90 text-white">
                    {getInitials(currentDeal.name)}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <div className="flex items-center gap-2 flex-wrap">
                    <h1 className="text-2xl font-bold tracking-tight">{currentDeal.name}</h1>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full">
                            <Star className="h-[18px] w-[18px] text-muted-foreground" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Đánh dấu quan trọng</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <div className="flex items-center gap-2 mt-1">
                    {getStatusBadge(currentDeal.stage)}
                    {getPriorityBadge(currentDeal.priority)}
                    <Badge variant="outline" className="flex items-center gap-1 px-2 py-1 bg-green-50 text-green-700 border-transparent">
                      <DollarSign className="h-3.5 w-3.5 mr-1" />
                      <span>{currentDeal.value.toLocaleString('vi-VN')} VNĐ</span>
                    </Badge>
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <HoverCard>
                  <HoverCardTrigger asChild>
                    <Button variant="outline" size="sm" className="gap-1.5">
                      <Avatar className="h-5 w-5">
                        <AvatarFallback className="text-[10px] bg-primary/90 text-white">
                          {currentDeal.ownerName?.substring(0, 2) || 'UN'}
                        </AvatarFallback>
                      </Avatar>
                      <span>{currentDeal.ownerName || 'Chưa phân công'}</span>
                    </Button>
                  </HoverCardTrigger>
                  <HoverCardContent className="w-64">
                    <div className="flex justify-between space-x-4">
                      <Avatar>
                        <AvatarFallback className="bg-primary/90 text-white">
                          {currentDeal.ownerName?.substring(0, 2) || 'UN'}
                        </AvatarFallback>
                      </Avatar>
                      <div className="space-y-1">
                        <h4 className="text-sm font-semibold">{currentDeal.ownerName || 'Chưa phân công'}</h4>
                        <p className="text-sm text-muted-foreground">Người phụ trách deal</p>
                        <div className="flex items-center pt-1">
                          <Button variant="link" size="sm" className="px-0 h-auto text-xs">
                            Đổi người phụ trách
                          </Button>
                        </div>
                      </div>
                    </div>
                  </HoverCardContent>
                </HoverCard>

                <Button variant="outline" onClick={handleEdit}>
                  <Pencil className="h-4 w-4 mr-2" />
                  <span>Chỉnh sửa</span>
                </Button>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="icon" className="h-9 w-9">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={handleEdit}>
                      <Pencil className="h-4 w-4 mr-2" />
                      <span>Chỉnh sửa deal</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Users className="h-4 w-4 mr-2" />
                      <span>Thay đổi người phụ trách</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem className="text-destructive focus:text-destructive" onClick={handleDelete}>
                      <Trash className="h-4 w-4 mr-2" />
                      <span>Xóa deal</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>          {/* Main content */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <div className="w-full overflow-x-auto pb-2">
              <TabsList>
                <TabsTrigger value="overview"><Info className="h-4 w-4 mr-2" /> Tổng quan</TabsTrigger>
                <TabsTrigger value="activity"><Activity className="h-4 w-4 mr-2" /> Hoạt động</TabsTrigger>
                <TabsTrigger value="tasks"><ListTodo className="h-4 w-4 mr-2" /> Nhiệm vụ</TabsTrigger>
                <TabsTrigger value="products"><Package className="h-4 w-4 mr-2" /> Sản phẩm</TabsTrigger>
              </TabsList>
            </div>

            {/* Tab Contents */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:items-start">
              {/* Main Content Area */}
              <div className="md:col-span-2">
                <TabsContent value="overview" className="mt-0 space-y-4">
                  <DealOverview deal={currentDeal} getStatusBadge={getStatusBadge} onStageChange={handleStageChange} />
                </TabsContent>

                <TabsContent value="activity" className="mt-0 space-y-4">
                  <ActivityFeed />
                </TabsContent>

                <TabsContent value="tasks" className="mt-0 space-y-4">
                  <TasksList />
                </TabsContent>

                <TabsContent value="products" className="mt-0 space-y-4">
                  <ProductsList />
                </TabsContent>
              </div>

              {/* Right Sidebar - Deal Info */}
              <div className="md:col-span-1">
                <DealInfoPanel deal={currentDeal} />
              </div>
            </div>
          </Tabs>
        </div>
      </Main>
    </>
  )
}

function DealInfoPanel({ deal }: { deal: Deal }) {
  return (
    <Card className="overflow-hidden border p-0 flex flex-col">
      <div className="bg-muted/50 px-4 py-2 border-b">
        <h3 className="text-base font-medium">Thông tin Deal</h3>
      </div>
      <div className="flex-1">
        <div className="divide-y">
          <InfoItem
            icon={<DollarSign className="h-4 w-4 text-muted-foreground" />}
            label="Giá trị"
            value={
              <span className="text-primary font-medium">
                {deal.value.toLocaleString('vi-VN')} VNĐ
              </span>
            }
          />
          <InfoItem
            icon={<CalendarClock className="h-4 w-4 text-muted-foreground" />}
            label="Ngày đóng dự kiến"
            value={deal.expectedCloseDate}
          />
          <InfoItem
            icon={<Star className="h-4 w-4 text-muted-foreground" />}
            label="Xác suất"
            value={`${deal.probability}%`}
          />
          <InfoItem
            icon={<Building className="h-4 w-4 text-muted-foreground" />}
            label="Khách hàng"
            value={deal.accountName || <span className="text-muted-foreground">Chưa có thông tin</span>}
          />
          <InfoItem
            icon={<Users className="h-4 w-4 text-muted-foreground" />}
            label="Người phụ trách"
            value={deal.ownerName || <span className="text-muted-foreground">Chưa phân công</span>}
          />
          <InfoItem
            icon={<Send className="h-4 w-4 text-muted-foreground" />}
            label="Chiến dịch"
            value={deal.campaignName || <span className="text-muted-foreground">Không có</span>}
          />
        </div>

        {/* Tags */}
        {deal.tags && deal.tags.length > 0 && (
          <div className="p-4 border-t">
            <h4 className="text-sm font-medium mb-2">Tags</h4>
            <div className="flex flex-wrap gap-1">
              {deal.tags.map((tag, index) => (
                <Badge key={index} variant="secondary" className="capitalize">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className="p-3 bg-muted/30 border-t">
        <div className="grid grid-cols-2 gap-2 mb-2">
          <Button variant="outline" size="sm" className="w-full">
            <Phone className="h-3.5 w-3.5 mr-2" />
            Gọi điện
          </Button>
          <Button variant="outline" size="sm" className="w-full">
            <Mail className="h-3.5 w-3.5 mr-2" />
            Gửi email
          </Button>
        </div>
        <div className="grid grid-cols-2 gap-2">
          <Button variant="outline" size="sm" className="w-full">
            <Calendar className="h-3.5 w-3.5 mr-2" />
            Lịch hẹn
          </Button>
          <Button variant="outline" size="sm" className="w-full">
            <FileText className="h-3.5 w-3.5 mr-2" />
            Tạo báo giá
          </Button>
        </div>
      </div>
    </Card>
  )
}

function InfoItem({ icon, label, value }: { icon: React.ReactNode, label: string, value: React.ReactNode }) {
  return (
    <div className="px-4 py-3.5">
      <div className="flex items-start justify-between">
        <div className="flex gap-2 items-center">
          {icon}
          <div className="text-sm font-medium">{label}</div>
        </div>
        <div className="text-sm">{value}</div>
      </div>
    </div>
  )
}function DealOverview({ deal, getStatusBadge, onStageChange }: { 
  deal: Deal, 
  getStatusBadge: (stage: string) => React.ReactNode,
  onStageChange: (newStage: string) => void 
}) {
  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="text-base font-medium">Lộ trình Deal (Sales Path)</CardTitle>
        </CardHeader>
        <CardContent>
          <SalesPathForDeal deal={deal} onStageChange={onStageChange} />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-base font-medium">Thông tin chi tiết</CardTitle>
        </CardHeader>
        <CardContent className="grid gap-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Deal Info - Left Column */}
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium mb-3">Thông tin Deal</h3>
                <div className="space-y-3">
                  <div className="flex items-start justify-between">
                    <span className="text-xs text-muted-foreground w-28">Tên Deal:</span>
                    <span className="text-sm font-medium flex-1 text-right">{deal.name}</span>
                  </div>
                  <div className="flex items-start justify-between">
                    <span className="text-xs text-muted-foreground w-28">Giai đoạn:</span>
                    <div className="flex-1 text-right">
                      <div className="inline-flex">{getStatusBadge(deal.stage)}</div>
                    </div>
                  </div>
                  <div className="flex items-start justify-between">
                    <span className="text-xs text-muted-foreground w-28">Giá trị:</span>
                    <span className="text-sm font-medium flex-1 text-right text-primary">
                      {deal.value.toLocaleString('vi-VN')} VNĐ
                    </span>
                  </div>
                  <div className="flex items-start justify-between">
                    <span className="text-xs text-muted-foreground w-28">Xác suất:</span>
                    <span className="text-sm font-medium flex-1 text-right">{deal.probability}%</span>
                  </div>
                  <div className="flex items-start justify-between">
                    <span className="text-xs text-muted-foreground w-28">Đóng dự kiến:</span>
                    <span className="text-sm font-medium flex-1 text-right">{deal.expectedCloseDate}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Additional Info - Right Column */}
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium mb-3">Thông tin khác</h3>
                <div className="space-y-3">
                  <div className="flex items-start justify-between">
                    <span className="text-xs text-muted-foreground w-28">Khách hàng:</span>
                    <span className="text-sm font-medium flex-1 text-right">
                      {deal.accountName || "Chưa có thông tin"}
                    </span>
                  </div>
                  <div className="flex items-start justify-between">
                    <span className="text-xs text-muted-foreground w-28">Người phụ trách:</span>
                    <span className="text-sm font-medium flex-1 text-right">
                      {deal.ownerName || "Chưa phân công"}
                    </span>
                  </div>
                  <div className="flex items-start justify-between">
                    <span className="text-xs text-muted-foreground w-28">Chiến dịch:</span>
                    <span className="text-sm font-medium flex-1 text-right">
                      {deal.campaignName || "Không có"}
                    </span>
                  </div>
                  <div className="flex items-start justify-between">
                    <span className="text-xs text-muted-foreground w-28">Ngày tạo:</span>
                    <span className="text-sm font-medium flex-1 text-right">{deal.createdAt}</span>
                  </div>
                  <div className="flex items-start justify-between">
                    <span className="text-xs text-muted-foreground w-28">Cập nhật:</span>
                    <span className="text-sm font-medium flex-1 text-right">{deal.updatedAt}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Description */}
          {deal.description && (
            <div>
              <h3 className="text-sm font-medium mb-2">Mô tả</h3>
              <div className="p-3 bg-muted/30 rounded-md">
                <p className="text-sm">{deal.description}</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}          <TaskItem
            title="Gửi báo giá chi tiết"
            dueDate="Ngày mai, 10:00"
            assignee="John Doe"
            status="pending"
          />
          <TaskItem
            title="Lên lịch demo sản phẩm"
            dueDate="Hôm nay, 15:00"
            assignee="Jane Smith"
            status="completed"
          />
          <TaskItem
            title="Chuẩn bị hợp đồng"
            dueDate="Tuần sau, 09:00"
            assignee="John Doe"
            status="pending"
          />
        </div>
      </CardContent>
    </Card>
  )
}

function TaskItem({ title, dueDate, assignee, status }: { title: string, dueDate: string, assignee: string, status: string }) {
  return (
    <div className="flex items-center justify-between p-4">
      <div className="flex gap-4 items-center">
        <div className="flex flex-col">
          <div className="font-medium">{title}</div>
          <div className="text-sm text-muted-foreground flex items-center gap-1">
            <Clock className="h-3 w-3" /> {dueDate}
          </div>
        </div>
      </div>
      <div className="flex gap-2 items-center">
        <Avatar className="h-8 w-8">
          <AvatarFallback className="text-[10px]">{assignee.charAt(0)}</AvatarFallback>
        </Avatar>
        <Badge variant={status === "completed" ? "default" : "outline"}>
          {status === "completed" ? "Hoàn thành" : "Đang chờ"}
        </Badge>
      </div>
    </div>
  )
}

function ProductsList() {
  const products = [
    {
      id: '1',
      name: 'CRM Premium Package',
      quantity: 1,
      unitPrice: 50000000,
      total: 50000000
    },
    {
      id: '2', 
      name: 'Training & Support',
      quantity: 1,
      unitPrice: 10000000,
      total: 10000000
    }
  ]

  const totalValue = products.reduce((sum, product) => sum + product.total, 0)

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-3">
        <div>
          <CardTitle className="text-base font-medium">Sản phẩm & Dịch vụ</CardTitle>
          <CardDescription>Danh sách sản phẩm trong deal</CardDescription>
        </div>
        <Button variant="outline" size="sm">
          <Plus className="h-3.5 w-3.5 mr-2" />
          Thêm sản phẩm
        </Button>
      </CardHeader>
      <CardContent className="p-0">
        <div className="divide-y">
          {products.map((product) => (
            <div key={product.id} className="flex items-center justify-between p-4">
              <div className="flex gap-4 items-center">
                <Package className="h-5 w-5 text-muted-foreground" />
                <div className="flex flex-col">
                  <div className="font-medium">{product.name}</div>
                  <div className="text-sm text-muted-foreground">
                    SL: {product.quantity} x {product.unitPrice.toLocaleString('vi-VN')} VNĐ
                  </div>
                </div>
              </div>
              <div className="text-sm font-medium">
                {product.total.toLocaleString('vi-VN')} VNĐ
              </div>
            </div>
          ))}
        </div>
        <div className="border-t bg-muted/30 p-4">
          <div className="flex items-center justify-between">
            <span className="font-medium">Tổng giá trị:</span>
            <span className="text-lg font-bold text-primary">
              {totalValue.toLocaleString('vi-VN')} VNĐ
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default DealDetail