import { IconTable, IconCalendarEvent, IconChartBar, IconLayoutKanban } from '@tabler/icons-react'
import { ViewMode } from '../context/deals-context-type'
import { cn } from '@/lib/utils'

interface ViewModeToggleProps {
  viewMode: ViewMode
  setViewMode: (mode: ViewMode) => void
}

export function ViewModeToggle({ viewMode, setViewMode }: ViewModeToggleProps) {
  return (
    <div className="inline-flex h-10 items-center justify-center rounded-lg bg-muted text-muted-foreground p-[4px]">
      <button
        className={cn(
          "inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
          viewMode === "table" && "bg-background text-foreground shadow-sm"
        )}
        onClick={() => setViewMode('table')}
        title="Chế độ xem bảng"
      >
        <IconTable className="mr-2 h-4 w-4" />
        <span>Bảng</span>
      </button>
      <button
        className={cn(
          "inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
          viewMode === "pipeline" && "bg-background text-foreground shadow-sm"
        )}
        onClick={() => setViewMode('pipeline')}
        title="Chế độ xem Pipeline"
      >
        <IconLayoutKanban className="mr-2 h-4 w-4" />
        <span>Pipeline</span>
      </button>
      <button
        className={cn(
          "inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
          viewMode === "calendar" && "bg-background text-foreground shadow-sm"
        )}
        onClick={() => setViewMode('calendar')}
        title="Chế độ xem lịch"
      >
        <IconCalendarEvent className="mr-2 h-4 w-4" />
        <span>Lịch</span>
      </button>
      <button
        className={cn(
          "inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
          viewMode === "analytics" && "bg-background text-foreground shadow-sm"
        )}
        onClick={() => setViewMode('analytics')}
        title="Chế độ xem phân tích"
      >
        <IconChartBar className="mr-2 h-4 w-4" />
        <span>Phân tích</span>
      </button>
    </div>
  )
}
