import React from 'react'
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { cn } from '@/lib/utils'
import { useDealsContext } from '../context/use-deals-context'
import { Deal, DealStageValue } from '../data/schema'
import { pipelineConfig, stageOrder, formatCurrency } from '../data/data'
import { useRouter } from '@tanstack/react-router'

// Định nghĩa các trạng thái cho Kanban từ stageOrder và pipelineConfig
const dealStatuses = stageOrder.map(stage => ({
  value: stage as DealStageValue,
  label: pipelineConfig[stage].name,
  color: pipelineConfig[stage].color,
}));

// Helper function để sắp xếp lại các deal trong cùng một cột
const reorder = (list: Deal[], startIndex: number, endIndex: number): Deal[] => {
  const result = Array.from(list)
  const [removed] = result.splice(startIndex, 1)
  result.splice(endIndex, 0, removed)
  return result
}

// Helper function để di chuyển deal giữa các cột
const move = (
  source: Deal[],
  destination: Deal[],
  droppableSource: { index: number; droppableId: string },
  droppableDestination: { index: number; droppableId: string }
): { [key: string]: Deal[] } => {
  const sourceClone = Array.from(source)
  const destClone = Array.from(destination)
  const [removed] = sourceClone.splice(droppableSource.index, 1)

  // Tạo deal mới với trạng thái đã cập nhật
  const updatedDeal = {
    ...removed,
    stage: droppableDestination.droppableId as DealStageValue
  }

  destClone.splice(droppableDestination.index, 0, updatedDeal)

  const result: { [key: string]: Deal[] } = {}
  result[droppableSource.droppableId] = sourceClone
  result[droppableDestination.droppableId] = destClone

  return result
}

// Hàm lấy chữ cái đầu của tên
const getInitials = (name?: string) => {
  if (!name) return ''
  return name
    .split(' ')
    .map(part => part[0])
    .join('')
    .toUpperCase()
    .substring(0, 2)
}

// Component hiển thị card deal
const DealCard = ({ deal }: { deal: Deal }) => {
  const router = useRouter()
  return (
    <div 
      className="flex-1 bg-card rounded-md p-3 shadow-sm border border-border hover:border-primary/20 hover:shadow-lg transition-all cursor-pointer"
      onClick={() => router.navigate({ to: '/deals/$id', params: { id: deal.id } })}
    >
      <div className="space-y-2.5">
        <div className="flex justify-between items-start">
          <h4 className="text-sm font-medium text-foreground line-clamp-2 leading-tight">
            {deal.name}
          </h4>
          {/* Có thể thêm Badge cho priority nếu cần */}
        </div>

        {deal.accountName && (
          <p className="text-xs text-muted-foreground truncate">{deal.accountName}</p>
        )}

        <div className="flex justify-between items-center">
          <span className="text-sm font-semibold text-primary">
            {formatCurrency(deal.value)}
          </span>
          {deal.probability !== undefined && (
             <Badge variant="outline" className="text-xs">
               {deal.probability}%
             </Badge>
          )}
        </div>
        
        {deal.probability !== undefined && (
          <Progress value={deal.probability} className="h-1.5 mt-1" />
        )}

        <div className="flex items-center justify-between pt-1 text-xs text-muted-foreground">
          <span>
            {deal.expectedCloseDate ? new Date(deal.expectedCloseDate).toLocaleDateString('vi-VN') : 'N/A'}
          </span>
          {deal.ownerName && (
            <div className="flex items-center gap-1">
               <Avatar className="h-5 w-5">
                <AvatarImage src={deal.ownerAvatar} alt={deal.ownerName} />
                <AvatarFallback className="bg-primary/10 text-primary text-[10px]">
                  {getInitials(deal.ownerName)}
                </AvatarFallback>
              </Avatar>
              <span className="truncate">{deal.ownerName.split(' ')[0]}</span> {/* Chỉ hiển thị tên */}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

// Component chính KanbanBoard
interface KanbanBoardProps {
  dealsData: Deal[];
}

export function KanbanBoard({ dealsData }: KanbanBoardProps) {
  const { setDeals, updateDealStage } = useDealsContext()

  // Nhóm deals theo trạng thái
  const dealsByStage = dealStatuses.reduce<Record<string, Deal[]>>((acc, status) => {
    acc[status.value] = dealsData.filter(deal => deal.stage === status.value)
    return acc
  }, {})

  // Xử lý khi kéo thả kết thúc
  const onDragEnd = (result: DropResult) => {
    const { source, destination } = result

    if (!destination) {
      return
    }

    if (
      source.droppableId === destination.droppableId &&
      source.index === destination.index
    ) {
      return
    }

    const sourceStage = source.droppableId as DealStageValue;
    const destinationStage = destination.droppableId as DealStageValue;

    if (source.droppableId === destination.droppableId) {
      const reorderedDeals = reorder(
        dealsByStage[sourceStage],
        source.index,
        destination.index
      )
      const newDealsByStage = {
        ...dealsByStage,
        [sourceStage]: reorderedDeals
      }
      const updatedDeals = Object.values(newDealsByStage).flat()
      setDeals(updatedDeals)
    }
    else {
      const resultMove = move(
        dealsByStage[sourceStage],
        dealsByStage[destinationStage],
        source,
        destination
      )
      const newDealsByStage = {
        ...dealsByStage,
        [sourceStage]: resultMove[sourceStage],
        [destinationStage]: resultMove[destinationStage]
      }
      const updatedDeals = Object.values(newDealsByStage).flat()
      setDeals(updatedDeals)
      
      // Cập nhật trạng thái của deal cụ thể trên server/context
      const dealId = dealsByStage[sourceStage][source.index].id
      updateDealStage(dealId, destinationStage)
    }
  }

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <div className="flex overflow-x-auto pb-4 gap-6 h-[calc(100vh-8rem)] -mx-4 px-4">
        {dealStatuses.map(status => (
          <div key={status.value} className="flex-none w-[350px] h-full">
            <div className="bg-background rounded-lg p-4 border border-border h-full flex flex-col">
              {/* Column header */}
              <div className="mb-3 flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="flex items-center space-x-1">
                    <h3 className="font-medium text-sm">{status.label}</h3>
                  </div>
                  <div className="rounded-md bg-muted px-1.5 py-0.5 text-xs text-muted-foreground">
                    {dealsByStage[status.value]?.length || 0}
                  </div>
                </div>
              </div>

              {/* Droppable area for deals */}
              <Droppable droppableId={status.value}>
                {(provided, snapshot) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    className={cn(
                      "flex-1 flex flex-col gap-2 p-1 overflow-y-auto",
                      snapshot.isDraggingOver && "bg-primary/5 rounded-md"
                    )}
                    style={{
                      // Custom scrollbar styling
                      scrollbarWidth: 'thin',
                      scrollbarColor: 'hsl(var(--muted-foreground)) transparent',
                    }}
                  >
                    {dealsByStage[status.value]?.map((deal, index) => (
                      <Draggable key={deal.id} draggableId={deal.id} index={index}>
                        {(providedDraggable, snapshotDraggable) => (
                          <div
                            ref={providedDraggable.innerRef}
                            {...providedDraggable.draggableProps}
                            {...providedDraggable.dragHandleProps}
                            className={cn(
                              "flex items-start group",
                              snapshotDraggable.isDragging && "opacity-80"
                            )}
                            style={{
                              ...providedDraggable.draggableProps.style,
                            }}
                          >
                            <DealCard deal={deal} />
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                    {dealsByStage[status.value]?.length === 0 && !snapshot.isDraggingOver && (
                       <div className="flex-1 flex flex-col items-center justify-center text-sm text-muted-foreground italic p-4 text-center">
                         <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round" className="opacity-50 mb-2">
                           <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                           <line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line>
                         </svg>
                         <span>Kéo và thả deal vào đây hoặc click để tạo mới.</span>
                       </div>
                    )}
                  </div>
                )}
              </Droppable>
            </div>
          </div>
        ))}
      </div>
    </DragDropContext>
  )
} 