import { cn } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'
import { Deal } from '../data/schema'

import { pipelineConfig, stageOrder } from '../data/data'

interface DealPipelineProps {
  deal: Deal
}

export function DealPipeline({ deal }: DealPipelineProps) {

  // Lấy danh sách các giai đoạn đã lọc bỏ 'Tìm kiếm khách hàng'
  const stages = stageOrder
    .filter(stageValue => pipelineConfig[stageValue]?.name !== 'Tìm kiếm khách hàng')
    .map(stageValue => ({
      value: stageValue,
      ...pipelineConfig[stageValue]
    }));

  // Tìm index của giai đoạn hiện tại
  const currentStageIndex = stages.findIndex(stage => stage.value === deal.stage);


  return (
    <div className="space-y-6">
      {/* Timeline - Hiển thị dạng timeline */}
      <div className="space-y-2">
        <div className="text-sm font-medium">Timeline giai đoạn</div>
        <div className="border rounded-md p-4 space-y-4">
          <div className="relative pl-6 pb-6 border-l border-border">
            <div className="absolute left-0 top-0 -translate-x-1/2 w-4 h-4 rounded-full bg-primary flex items-center justify-center">
              <div className="w-2 h-2 rounded-full bg-white"></div>
            </div>
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <span className="font-medium">Tạo mới Deal</span>
                <Badge variant="outline" className="text-xs">
                  {new Date(deal.createdAt || '').toLocaleDateString('vi-VN')}
                </Badge>
              </div>
              <p className="text-sm text-muted-foreground">
                Deal được tạo bởi {deal.ownerName || 'Hệ thống'}
              </p>
            </div>
          </div>

          {currentStageIndex >= 1 && (
            <div className="relative pl-6 pb-6 border-l border-border">
              <div className="absolute left-0 top-0 -translate-x-1/2 w-4 h-4 rounded-full bg-purple-500 flex items-center justify-center">
                <div className="w-2 h-2 rounded-full bg-white"></div>
              </div>
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <span className="font-medium">Đánh giá tư cách</span>
                  <Badge variant="outline" className="text-xs">
                    {new Date(deal.updatedAt).toLocaleDateString('vi-VN')}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  Đã đánh giá khả năng hợp tác và tiềm năng của khách hàng
                </p>
              </div>
            </div>
          )}

          {currentStageIndex >= 2 && (
            <div className="relative pl-6 pb-6 border-l border-border">
              <div className="absolute left-0 top-0 -translate-x-1/2 w-4 h-4 rounded-full bg-amber-500 flex items-center justify-center">
                <div className="w-2 h-2 rounded-full bg-white"></div>
              </div>
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <span className="font-medium">Phân tích nhu cầu</span>
                  <Badge variant="outline" className="text-xs">
                    {new Date(deal.updatedAt).toLocaleDateString('vi-VN')}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  Đã phân tích sâu nhu cầu của khách hàng để đề xuất giải pháp phù hợp
                </p>
              </div>
            </div>
          )}

          {currentStageIndex >= 3 && (
            <div className="relative pl-6 pb-6 border-l border-border">
              <div className="absolute left-0 top-0 -translate-x-1/2 w-4 h-4 rounded-full bg-indigo-500 flex items-center justify-center">
                <div className="w-2 h-2 rounded-full bg-white"></div>
              </div>
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <span className="font-medium">Đề xuất</span>
                  <Badge variant="outline" className="text-xs">
                    {new Date(deal.updatedAt).toLocaleDateString('vi-VN')}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  Đã gửi đề xuất giá và giải pháp chi tiết cho khách hàng
                </p>
              </div>
            </div>
          )}

          {currentStageIndex >= 4 && (
            <div className="relative pl-6">
              <div className="absolute left-0 top-0 -translate-x-1/2 w-4 h-4 rounded-full bg-orange-500 flex items-center justify-center">
                <div className="w-2 h-2 rounded-full bg-white"></div>
              </div>
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <span className="font-medium">Đàm phán</span>
                  <Badge variant="outline" className="text-xs">
                    {new Date(deal.updatedAt).toLocaleDateString('vi-VN')}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  Đang thương lượng chi tiết hợp đồng và điều khoản
                </p>
              </div>
            </div>
          )}

          {(deal.stage === 'closed_won' || deal.stage === 'closed_lost') && (
            <div className="relative pl-6">
              <div className={cn(
                "absolute left-0 top-0 -translate-x-1/2 w-4 h-4 rounded-full flex items-center justify-center",
                deal.stage === 'closed_won' ? "bg-emerald-500" : "bg-rose-500"
              )}>
                <div className="w-2 h-2 rounded-full bg-white"></div>
              </div>
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <span className="font-medium">
                    {deal.stage === 'closed_won' ? 'Thành công' : 'Thất bại'}
                  </span>
                  <Badge variant="outline" className="text-xs">
                    {new Date(deal.updatedAt).toLocaleDateString('vi-VN')}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  {deal.stage === 'closed_won'
                    ? 'Deal đã được chốt thành công'
                    : 'Deal không thành công'}
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
