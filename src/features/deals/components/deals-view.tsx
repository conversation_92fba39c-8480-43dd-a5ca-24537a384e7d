import { useDealsContext } from '../context/use-deals-context'
import { DealsTable } from './deals-table'
import { KanbanBoard } from './kanban-board'
import { FilterPanel } from '@/features/campaigns/components/filter-panel'
import { Deal } from '../data/schema'
import { IconCalendarEvent, IconChartBar } from '@tabler/icons-react'

interface DealsViewProps {
  data: Deal[]
}

export function DealsView({ data }: DealsViewProps) {
  const { viewMode, showFilterPanel } = useDealsContext()
  const filteredData = data
  
  return (
    <div className="space-y-4">
      {showFilterPanel && <FilterPanel />}
      
      {viewMode === 'table' && (
        <DealsTable data={filteredData} />
      )}
      {viewMode === 'pipeline' && (
        <KanbanBoard dealsData={filteredData} />
      )}
      {viewMode === 'calendar' && (
        <div className="text-center py-20 text-muted-foreground">
          <IconCalendarEvent className="h-16 w-16 mx-auto mb-4 opacity-20" />
          <h3 className="text-lg font-medium mb-2">Chế độ xem Lịch đang được phát triển</h3>
          <p className="max-w-md mx-auto">Chế độ xem Lịch đang được phát triển và sẽ sớm được cập nhật.</p>
        </div>
      )}
      {viewMode === 'analytics' && (
        <div className="text-center py-20 text-muted-foreground">
          <IconChartBar className="h-16 w-16 mx-auto mb-4 opacity-20" />
          <h3 className="text-lg font-medium mb-2">Chế độ xem Phân tích đang được phát triển</h3>
          <p className="max-w-md mx-auto">Chế độ xem Phân tích đang được phát triển và sẽ sớm được cập nhật.</p>
        </div>
      )}
    </div>
  )
} 