import { DataTable } from '@/components/ui/data-table'
import { Deal } from '../data/schema'
import { columns } from './deals-columns'
import { pipelineConfig } from '../data/data'

interface DealsTableProps {
  data: Deal[]
}

export function DealsTable({ data }: DealsTableProps) {
  const filters = [
    {
      column: 'stage',
      title: '<PERSON><PERSON><PERSON> đo<PERSON>',
      options: Object.entries(pipelineConfig).map(([value, config]) => ({
        label: config.name,
        value,
      })),
    },
  ]

  return (
    <DataTable
      columns={columns}
      data={data}
      searchKey="name"
      searchPlaceholder="Tìm kiếm deal..."
      filters={filters}
    />
  )
}