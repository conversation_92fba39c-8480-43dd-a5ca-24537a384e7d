import { PlusIcon, DownloadIcon } from '@radix-ui/react-icons'
import { Upload } from 'lucide-react'
import { PrimaryButtonsGroup } from '@/components/ui/primary-buttons-group'
import { useDealsContext } from '../context/use-deals-context'

export function DealsPrimaryButtons() {
  const { setOpenDialog } = useDealsContext()

  const buttons = [
    {
      label: 'Tạo Deal',
      icon: PlusIcon,
      onClick: () => setOpenDialog('createDeal'),
    },
    {
      label: 'Nhập',
      icon: Upload,
      onClick: () => setOpenDialog('importDeals'),
      variant: 'outline' as const,
    },
    {
      label: 'Xuất',
      icon: DownloadIcon,
      onClick: () => setOpenDialog('exportDeals'),
      variant: 'outline' as const,
    },
  ]

  return <PrimaryButtonsGroup buttons={buttons} />
}