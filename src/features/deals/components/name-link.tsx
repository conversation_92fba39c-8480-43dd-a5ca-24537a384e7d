import { Link } from '@tanstack/react-router'
import { Row } from '@tanstack/react-table'

interface NameLinkProps<TData> {
  row: Row<TData>
}

export function NameLink<TData>({ row }: NameLinkProps<TData>) {
  const id = (row.original as { id: string }).id
  const name = row.getValue('name') as string

  return (
    <div className="max-w-[500px] truncate">
      <Link
        to="/deals/$id" params={{ id }}
        className="font-medium hover:underline text-foreground"
      >
        {name}
      </Link>
    </div>
  )
} 