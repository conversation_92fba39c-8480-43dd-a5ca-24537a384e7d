import { ColumnDef } from '@tanstack/react-table'
import { formatCurrency, formatProbability, getProbabilityColorClass, pipelineConfig } from '../data/data'
import { Deal } from '../data/schema'
import { DataTableColumnHeader } from '@/components/ui/data-table'
import { DataTableRowActions } from './data-table-row-actions'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { cn } from '@/lib/utils'
import { NameLink } from './name-link'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Progress } from '@/components/ui/progress'

export const columns: ColumnDef<Deal>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Chọn tất cả"
        className="translate-y-[2px]"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Chọn hàng"
        className="translate-y-[2px]"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Tên deal" />
    ),
    cell: ({ row }) => <NameLink row={row} />,
    enableSorting: true,
    enableHiding: false,
  },
  {
    accessorKey: 'stage',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Giai đoạn" />
    ),
    cell: ({ row }) => {
      const stage = row.getValue('stage') as string
      const config = pipelineConfig[stage]

      return (
        <div className="flex items-center">
          <Badge className={cn('rounded-md', config.color)}>
            {config.name}
          </Badge>
        </div>
      )
    },
    enableSorting: true,
    enableHiding: true,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    accessorKey: 'value',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Giá trị" />
    ),
    cell: ({ row }) => {
      const value = row.getValue('value') as number
      
      return (
        <div className="font-medium">{formatCurrency(value)}</div>
      )
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'probability',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Xác suất" />
    ),
    cell: ({ row }) => {
      const probability = row.getValue('probability') as number
      const colorClass = getProbabilityColorClass(probability)
      
      return (
        <div className="w-28">
          <div className="font-medium mb-1.5">{formatProbability(probability)}</div>
          <Progress
            value={probability}
            className="h-2"
            indicatorClassName={cn(colorClass)}
          />
        </div>
      )
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'expectedCloseDate',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Ngày đóng dự kiến" />
    ),
    cell: ({ row }) => {
      const date = new Date(row.getValue('expectedCloseDate') as string)
      const formattedDate = new Intl.DateTimeFormat('vi-VN').format(date)
      
      return (
        <div className="font-medium">{formattedDate}</div>
      )
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'accountName',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Công ty" />
    ),
    cell: ({ row }) => {
      return (
        <div className="font-medium">{row.getValue('accountName') as string}</div>
      )
    },
    enableSorting: true,
    enableHiding: true,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    accessorKey: 'ownerName',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Người phụ trách" />
    ),
    cell: ({ row }) => {
      const owner = row.getValue('ownerName') as string
      const avatar = row.original.ownerAvatar || ''
      const initials = owner?.split(' ').map(n => n[0]).join('').toUpperCase()
      
      return (
        <div className="flex items-center gap-2">
          <Avatar className="h-8 w-8">
            <AvatarImage src={avatar} alt={owner} />
            <AvatarFallback>{initials}</AvatarFallback>
          </Avatar>
          <div className="font-medium">{owner}</div>
        </div>
      )
    },
    enableSorting: true,
    enableHiding: true,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => <DataTableRowActions row={row} />,
  },
] 