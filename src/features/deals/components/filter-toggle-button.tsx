import { Button } from '@/components/ui/button'
import { IconFilter } from '@tabler/icons-react'
import { useDealsContext } from '../context/use-deals-context'

export function FilterToggleButton() {
  const { showFilterPanel, setShowFilterPanel } = useDealsContext()
  return (
    <Button
      variant="outline"
      size="sm"
      onClick={() => setShowFilterPanel(!showFilterPanel)}
      className="h-8"
    >
      <IconFilter className="mr-2 h-4 w-4" />
      Bộ lọc
    </Button>
  )
}
