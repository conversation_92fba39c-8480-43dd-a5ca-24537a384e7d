import { Button } from '@/components/ui/button'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'

import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useDealsContext } from '../context/use-deals-context'
import { useState } from 'react'
import { DealStage, DealPriority, DealStageValue, DealPriorityValue } from '../data/schema'
import { v4 as uuidv4 } from 'uuid'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'



export function DealsDialogs() {
  const { openDialog, setOpenDialog, deals, setDeals } = useDealsContext()

  const [formData, setFormData] = useState({
    name: '',
    value: '',
    probability: '50',
    stage: DealStage.PROSPECTING as DealStageValue,
    priority: DealPriority.MEDIUM as DealPriorityValue,
    expectedCloseDate: '',
    accountName: '',
    description: ''
  })

  const resetForm = () => {
    setFormData({
      name: '',
      value: '',
      probability: '50',
      stage: DealStage.PROSPECTING as DealStageValue,
      priority: DealPriority.MEDIUM as DealPriorityValue,
      expectedCloseDate: '',
      accountName: '',
      description: ''
    })
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    const valueNum = parseFloat(formData.value)
    const probNum = parseInt(formData.probability)
    
    const newDeal = {
      id: uuidv4(),
      name: formData.name,
      stage: formData.stage,
      value: valueNum,
      probability: probNum,
      expectedCloseDate: formData.expectedCloseDate || new Date().toISOString().split('T')[0],
      accountName: formData.accountName,
      priority: formData.priority,
      description: formData.description,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      weightedValue: Math.round(valueNum * (probNum / 100)),
      forecastCategory: probNum >= 70 ? 'best_case' : probNum >= 30 ? 'most_likely' : 'worst_case'
    }
    
    setDeals([newDeal, ...deals])
    setOpenDialog('')
    resetForm()
  }

  return (
    <>
      <Dialog open={openDialog === 'createDeal'} onOpenChange={(open) => {
        if (!open) {
          setOpenDialog('')
          resetForm()
        }
      }}>
        <DialogContent className="sm:max-w-[500px]">
          <form onSubmit={handleSubmit}>
            <DialogHeader>
              <DialogTitle>Tạo Deal mới</DialogTitle>
              <DialogDescription>
                Nhập thông tin để tạo một deal mới. Nhấn Lưu khi hoàn tất.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Tên deal
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  className="col-span-3"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="stage" className="text-right">
                  Giai đoạn
                </Label>
                <Select
                  value={formData.stage}
                  onValueChange={(value) => setFormData({...formData, stage: value as DealStageValue})}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Chọn giai đoạn" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={DealStage.PROSPECTING}>Tìm kiếm khách hàng</SelectItem>
                    <SelectItem value={DealStage.QUALIFICATION}>Đánh giá tư cách</SelectItem>
                    <SelectItem value={DealStage.NEEDS_ANALYSIS}>Phân tích nhu cầu</SelectItem>
                    <SelectItem value={DealStage.PROPOSAL}>Đề xuất</SelectItem>
                    <SelectItem value={DealStage.NEGOTIATION}>Đàm phán</SelectItem>
                    <SelectItem value={DealStage.CLOSED_WON}>Thành công</SelectItem>
                    <SelectItem value={DealStage.CLOSED_LOST}>Thất bại</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="value" className="text-right">
                  Giá trị (VND)
                </Label>
                <Input
                  id="value"
                  type="number"
                  min="0"
                  value={formData.value}
                  onChange={(e) => setFormData({...formData, value: e.target.value})}
                  className="col-span-3"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="probability" className="text-right">
                  Xác suất (%)
                </Label>
                <Input
                  id="probability"
                  type="number"
                  min="0"
                  max="100"
                  value={formData.probability}
                  onChange={(e) => setFormData({...formData, probability: e.target.value})}
                  className="col-span-3"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="priority" className="text-right">
                  Độ ưu tiên
                </Label>
                <Select
                  value={formData.priority}
                  onValueChange={(value) => setFormData({...formData, priority: value as DealPriorityValue})}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Chọn độ ưu tiên" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={DealPriority.HIGH}>Cao</SelectItem>
                    <SelectItem value={DealPriority.MEDIUM}>Trung bình</SelectItem>
                    <SelectItem value={DealPriority.LOW}>Thấp</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="expectedCloseDate" className="text-right">
                  Ngày đóng dự kiến
                </Label>
                <Input
                  id="expectedCloseDate"
                  type="date"
                  value={formData.expectedCloseDate}
                  onChange={(e) => setFormData({...formData, expectedCloseDate: e.target.value})}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="accountName" className="text-right">
                  Công ty
                </Label>
                <Input
                  id="accountName"
                  value={formData.accountName}
                  onChange={(e) => setFormData({...formData, accountName: e.target.value})}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="description" className="text-right">
                  Mô tả
                </Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  className="col-span-3"
                  rows={3}
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => {
                setOpenDialog('')
                resetForm()
              }}>
                Hủy
              </Button>
              <Button type="submit">Lưu</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      <Dialog open={openDialog === 'importDeals'} onOpenChange={(open) => {
        if (!open) setOpenDialog('')
      }}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Nhập Deals</DialogTitle>
            <DialogDescription>
              Chức năng này đang được phát triển. Vui lòng thử lại sau.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button type="button" onClick={() => setOpenDialog('')}>
              Đóng
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={openDialog === 'exportDeals'} onOpenChange={(open) => {
        if (!open) setOpenDialog('')
      }}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Xuất Deals</DialogTitle>
            <DialogDescription>
              Chức năng này đang được phát triển. Vui lòng thử lại sau.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button type="button" onClick={() => setOpenDialog('')}>
              Đóng
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
} 