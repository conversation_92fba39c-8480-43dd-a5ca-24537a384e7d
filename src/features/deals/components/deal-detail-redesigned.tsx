import { type Deal } from '../data/schema'
import { useDealsContext } from '../context/use-deals-context'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Kanban } from '@/components/ui/kanban'
import { CalendarIcon, DollarSign, UserIcon, ClockIcon } from 'lucide-react'

interface DealDetailProps {
  deal: Deal
}

export function DealDetail({ deal }: DealDetailProps) {
  const { deals } = useDealsContext()
  const currentDeal = deals.find(d => d.id === deal.id) || deal

  // Pipeline stages
  const stages = [
    { id: 'new', label: 'Mới', color: 'bg-gray-500' },
    { id: 'contacted', label: 'Đ<PERSON> liên hệ', color: 'bg-blue-500' },
    { id: 'quoted', label: 'Đã báo giá', color: 'bg-yellow-500' },
    { id: 'won', label: 'Thành công', color: 'bg-green-500' },
    { id: 'lost', label: 'Thất bại', color: 'bg-red-500' }
  ] as const

  // Pipeline stage progress
  const currentStageIndex = stages.findIndex(stage => stage.id === currentDeal.stage)
  const progress = ((currentStageIndex + 1) / stages.length) * 100

  // Kanban board data
  const kanbanData = {
    columns: [
      { id: 'new', title: 'Mới', items: [] },
      { id: 'contacted', title: 'Đã liên hệ', items: [] },
      { id: 'quoted', title: 'Đã báo giá', items: [] },
      { id: 'won', title: 'Thành công', items: [] },
      { id: 'lost', title: 'Thất bại', items: [] }
    ]
  }

  return (
    <div className="space-y-6">
      {/* Header with deal name and status */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold">{currentDeal.name}</h1>
          <div className="flex items-center mt-2">
            <Badge variant="outline" className="flex items-center">
              <ClockIcon className="w-4 h-4 mr-1" />
              Cập nhật: {new Date(currentDeal.updatedAt).toLocaleDateString('vi-VN')}
            </Badge>
          </div>
        </div>
        <Badge className={`bg-${currentDeal.stage === 'won' ? 'green' : currentDeal.stage === 'lost' ? 'red' : 'blue'}-500`}>
          {stages.find(s => s.id === currentDeal.stage)?.label}
        </Badge>
      </div>

      {/* Pipeline progress */}
      <Card>
        <CardHeader>
          <CardTitle>Quy trình bán hàng</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between text-sm">
              {stages.map((stage, index) => (
                <div key={stage.id} className="flex items-center">
                  <span className="text-sm">{stage.label}</span>
                  {index < stages.length - 1 && <div className="w-8 h-px bg-gray-200 mx-2" />}
                </div>
              ))}
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        </CardContent>
      </Card>

      {/* Deal details grid */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Left column */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Thông tin chi tiết</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center">
                  <UserIcon className="w-5 h-5 mr-2 text-gray-500" />
                  <span className="font-medium">Khách hàng:</span>
                  <span className="ml-auto">{currentDeal.accountName || 'Chưa xác định'}</span>
                </div>
                <div className="flex items-center">
                  <DollarSign className="w-5 h-5 mr-2 text-gray-500" />
                  <span className="font-medium">Giá trị:</span>
                  <span className="ml-auto">{currentDeal.value.toLocaleString('vi-VN')} ₫</span>
                </div>
                <div className="flex items-center">
                  <CalendarIcon className="w-5 h-5 mr-2 text-gray-500" />
                  <span className="font-medium">Dự kiến đóng:</span>
                  <span className="ml-auto">{currentDeal.expectedCloseDate ? new Date(currentDeal.expectedCloseDate).toLocaleDateString('vi-VN') : 'Chưa xác định'}</span>
                </div>
                <div className="flex items-center">
                  <ClockIcon className="w-5 h-5 mr-2 text-gray-500" />
                  <span className="font-medium">Xác suất:</span>
                  <span className="ml-auto">{currentDeal.probability}%</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Ghi chú</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-500">{currentDeal.notes || 'Chưa có ghi chú nào'}</p>
            </CardContent>
          </Card>
        </div>

        {/* Right column */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Hoạt động</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center">
                  <UserIcon className="w-5 h-5 mr-2 text-gray-500" />
                  <span className="font-medium">Người phụ trách:</span>
                  <span className="ml-auto font-medium">{currentDeal.ownerName || 'Chưa phân công'}</span>
                </div>
                <div className="flex items-center">
                  <CalendarIcon className="w-5 h-5 mr-2 text-gray-500" />
                  <span className="font-medium">Ngày tạo:</span>
                  <span className="ml-auto">{new Date(currentDeal.createdAt).toLocaleDateString('vi-VN')}</span>
                </div>
                <div className="flex items-center">
                  <ClockIcon className="w-5 h-5 mr-2 text-gray-500" />
                  <span className="font-medium">Cập nhật cuối:</span>
                  <span className="ml-auto">{new Date(currentDeal.updatedAt).toLocaleDateString('vi-VN')}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Kanban board */}
      <Card>
        <CardHeader>
          <CardTitle>Trạng thái giao dịch</CardTitle>
        </CardHeader>
        <CardContent>
          <Kanban 
            columns={kanbanData.columns}
            items={[]}
            onDragEnd={() => {}}
            emptyState="Kéo thả để thay đổi trạng thái"
          />
        </CardContent>
      </Card>

      {/* Tabs for related items */}
      <Tabs defaultValue="tasks" className="w-full">
        <TabsList>
          <TabsTrigger value="tasks">Nhiệm vụ</TabsTrigger>
          <TabsTrigger value="notes">Ghi chú</TabsTrigger>
          <TabsTrigger value="history">Lịch sử</TabsTrigger>
        </TabsList>
        <TabsContent value="tasks">
          <Card>
            <CardHeader>
              <CardTitle>Danh sách nhiệm vụ</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-500">Chưa có nhiệm vụ nào</p>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="notes">
          <Card>
            <CardHeader>
              <CardTitle>Ghi chú</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-500">Chưa có ghi chú nào</p>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="history">
          <Card>
            <CardHeader>
              <CardTitle>Lịch sử hoạt động</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-500">Chưa có hoạt động nào</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default DealDetail
