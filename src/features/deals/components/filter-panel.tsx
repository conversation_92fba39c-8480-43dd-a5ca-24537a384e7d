import { pipelineConfig } from '../data/data'
import { DealPriority } from '../data/schema'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { useState } from 'react'
import { Cross2Icon } from '@radix-ui/react-icons'
import { cn } from '@/lib/utils'

// Custom Slider component as a replacement for missing @/components/ui/slider
const Slider = ({ 
  value, 
  min, 
  max, 
  step, 
  onValueChange, 
  className 
}: { 
  value: [number, number]; 
  min: number; 
  max: number; 
  step: number; 
  onValueChange: (value: [number, number]) => void; 
  className?: string;
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const newValue = [...value] as [number, number];
    newValue[index] = Number(e.target.value);
    
    // Ensure min <= max
    if (index === 0 && newValue[0] > newValue[1]) {
      newValue[0] = newValue[1];
    } else if (index === 1 && newValue[1] < newValue[0]) {
      newValue[1] = newValue[0];
    }
    
    onValueChange(newValue);
  };
  
  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex space-x-2">
        <input 
          type="range" 
          min={min} 
          max={max} 
          step={step}
          value={value[0]}
          onChange={(e) => handleChange(e, 0)}
          className="w-full" 
        />
        <input 
          type="range" 
          min={min} 
          max={max} 
          step={step}
          value={value[1]}
          onChange={(e) => handleChange(e, 1)}
          className="w-full" 
        />
      </div>
    </div>
  );
};

interface FilterOptions {
  stage: string[];
  priority: string[];
  valueRange: [number, number];
  probRange: [number, number];
  owner: string;
  account: string;
  campaign: string;
}

interface FilterPanelProps {
  applyFilters: (filters: FilterOptions) => void;
  resetFilters: () => void;
}

export function FilterPanel({ applyFilters, resetFilters }: FilterPanelProps) {
  const [stage, setStage] = useState<string[]>([])
  const [priority, setPriority] = useState<string[]>([])
  const [valueRange, setValueRange] = useState<[number, number]>([0, *********])
  const [probRange, setProbRange] = useState<[number, number]>([0, 100])
  const [owner, setOwner] = useState<string>('')
  const [account, setAccount] = useState<string>('')
  const [campaign, setCampaign] = useState<string>('')
  
  const handleApplyFilters = () => {
    applyFilters({
      stage,
      priority,
      valueRange,
      probRange,
      owner,
      account,
      campaign
    })
  }
  
  const handleResetFilters = () => {
    setStage([])
    setPriority([])
    setValueRange([0, *********])
    setProbRange([0, 100])
    setOwner('')
    setAccount('')
    setCampaign('')
    resetFilters()
  }
  
  return (
    <div className="border rounded-md p-4 space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="font-medium">Bộ lọc nâng cao</h4>
        <Button
          variant="outline"
          size="sm"
          onClick={handleResetFilters}
          className="h-8"
        >
          <Cross2Icon className="mr-2 h-4 w-4" />
          Đặt lại
        </Button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Lọc theo giai đoạn */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Giai đoạn</Label>
          <div className="flex flex-wrap gap-2">
            {Object.entries(pipelineConfig).map(([key, config]) => (
              <Badge
                key={key}
                className={cn(
                  'cursor-pointer transition-colors',
                  stage.includes(key) ? config.color : 'bg-muted hover:bg-muted/80'
                )}
                onClick={() => {
                  setStage(prev => 
                    prev.includes(key) 
                      ? prev.filter(s => s !== key)
                      : [...prev, key]
                  )
                }}
              >
                {config.name}
              </Badge>
            ))}
          </div>
        </div>
        
        {/* Lọc theo priority */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Mức độ ưu tiên</Label>
          <div className="space-y-2">
            {Object.entries(DealPriority).map(([key, value]) => (
              <div key={key} className="flex items-center space-x-2">
                <Checkbox
                  id={`priority-${value}`}
                  checked={priority.includes(value)}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setPriority(prev => [...prev, value])
                    } else {
                      setPriority(prev => prev.filter(p => p !== value))
                    }
                  }}
                />
                <Label htmlFor={`priority-${value}`} className="text-sm cursor-pointer">
                  {key === 'HIGH' ? 'Cao' : key === 'MEDIUM' ? 'Trung bình' : 'Thấp'}
                </Label>
              </div>
            ))}
          </div>
        </div>
        
        {/* Lọc theo giá trị */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Khoảng giá trị (₫)</Label>
          <div className="px-2">
            <Slider
              value={valueRange}
              min={0}
              max={*********}
              step={10000000}
              onValueChange={setValueRange}
              className="my-6"
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{new Intl.NumberFormat('vi-VN').format(valueRange[0])} ₫</span>
              <span>{new Intl.NumberFormat('vi-VN').format(valueRange[1])} ₫</span>
            </div>
          </div>
        </div>
        
        {/* Lọc theo xác suất */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Xác suất thành công (%)</Label>
          <div className="px-2">
            <Slider
              value={probRange}
              min={0}
              max={100}
              step={5}
              onValueChange={setProbRange}
              className="my-6"
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{probRange[0]}%</span>
              <span>{probRange[1]}%</span>
            </div>
          </div>
        </div>
        
        {/* Lọc theo owner */}
        <div className="space-y-3">
          <Label htmlFor="owner" className="text-sm font-medium">Người phụ trách</Label>
          <Input
            id="owner"
            value={owner}
            onChange={(e) => setOwner(e.target.value)}
            placeholder="Tên người phụ trách"
          />
        </div>
        
        {/* Lọc theo account */}
        <div className="space-y-3">
          <Label htmlFor="account" className="text-sm font-medium">Công ty</Label>
          <Input
            id="account"
            value={account}
            onChange={(e) => setAccount(e.target.value)}
            placeholder="Tên công ty"
          />
        </div>
      </div>
      
      <Button onClick={handleApplyFilters} className="w-full">
        Áp dụng bộ lọc
      </Button>
    </div>
  )
} 