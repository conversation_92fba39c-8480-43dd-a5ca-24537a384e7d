import { ReactNode, useMemo } from 'react'
import { useDealsContext } from '../context/use-deals-context'
import { LeadsContext } from '@/features/leads/context/leads-context-type'
import { Lead } from '@/features/leads/data/schema'

interface AdapterProps {
  children: ReactNode
}

export default function DealsToLeadsAdapter({ children }: AdapterProps) {
  const { 
    openDialog, 
    setOpenDialog, 
    selectedDealIds, 
    setSelectedDealIds, 
    deals, 
    updateDealStage,
    updateDealTags 
  } = useDealsContext()

  // Map deal to lead format
  const leads = useMemo<Lead[]>(() => {
    if (!deals.length) return []
    
    return deals.map(deal => ({
      id: deal.id,
      name: deal.name,
      status: deal.stage,
      email: '',
      mobileNo: '',
      assignedTo: deal.ownerName || '',
      lastModified: deal.updatedAt,
      source: '',
      leadOwner: deal.ownerName || '',
      avatar: deal.ownerAvatar || '',
      company: deal.accountName || '',
      position: '',
      estimatedValue: deal.value,
      leadScore: deal.probability,
      scoreBasedOn: [],
      address: '',
      website: '',
      industry: '',
      tags: deal.tags || [],
      notes: '',
      createdAt: deal.createdAt,
      updatedAt: deal.updatedAt,
    } as Lead))
  }, [deals])

  const value = {
    openDialog,
    setOpenDialog,
    selectedLeadIds: selectedDealIds,
    setSelectedLeadIds: setSelectedDealIds,
    viewMode: 'table' as const,
    setViewMode: () => {},
    showFilterPanel: false,
    setShowFilterPanel: () => {},
    leads,
    setLeads: () => {},
    updateLeadStatus: (leadId: string, newStatus: string) => {
      updateDealStage(leadId, newStatus)
    },
    updateLeadTags: (leadId: string, tags: string[]) => {
      updateDealTags(leadId, tags)
    },
  }

  return (
    <LeadsContext.Provider value={value}>
      {children}
    </LeadsContext.Provider>
  )
}
