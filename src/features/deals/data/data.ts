import { DealStage } from './schema'

// Pipeline configuration with colors and icons for each stage
export const pipelineConfig = {
  [DealStage.PROSPECTING]: {
    name: 'T<PERSON><PERSON> kiếm khách hàng',
    color: 'bg-blue-50 text-blue-700 dark:bg-blue-950 dark:text-blue-300',
    badgeColor: 'bg-blue-50 dark:bg-blue-950',
    icon: 'SearchIcon',
    description: 'B<PERSON>ớc đầu tiếp cận và tìm kiếm khách hàng tiềm năng'
  },
  [DealStage.QUALIFICATION]: {
    name: '<PERSON><PERSON><PERSON> giá tư cách',
    color: 'bg-purple-50 text-purple-700 dark:bg-purple-950 dark:text-purple-300',
    badgeColor: 'bg-purple-50 dark:bg-purple-950',
    icon: 'CheckCircleIcon',
    description: '<PERSON><PERSON><PERSON> gi<PERSON> khả năng hợp tác và tiềm năng của khách hàng'
  },
  [DealStage.NEEDS_ANALYSIS]: {
    name: '<PERSON>ân tích nhu cầu',
    color: 'bg-amber-50 text-amber-700 dark:bg-amber-950 dark:text-amber-300',
    badgeColor: 'bg-amber-50 dark:bg-amber-950',
    icon: 'LightBulbIcon',
    description: 'Phân tích sâu nhu cầu của khách hàng để đề xuất giải pháp phù hợp'
  },
  [DealStage.PROPOSAL]: {
    name: 'Đề xuất',
    color: 'bg-indigo-50 text-indigo-700 dark:bg-indigo-950 dark:text-indigo-300',
    badgeColor: 'bg-indigo-50 dark:bg-indigo-950',
    icon: 'FileTextIcon',
    description: 'Gửi đề xuất giá và giải pháp chi tiết cho khách hàng'
  },
  [DealStage.NEGOTIATION]: {
    name: 'Đàm phán',
    color: 'bg-orange-50 text-orange-700 dark:bg-orange-950 dark:text-orange-300',
    badgeColor: 'bg-orange-50 dark:bg-orange-950',
    icon: 'MessageSquareIcon',
    description: 'Thương lượng chi tiết hợp đồng và điều khoản'
  },
  [DealStage.CLOSED_WON]: {
    name: 'Thành công',
    color: 'bg-emerald-50 text-emerald-700 dark:bg-emerald-950 dark:text-emerald-300',
    badgeColor: 'bg-emerald-50 dark:bg-emerald-950',
    icon: 'CheckIcon',
    description: 'Deal đã được chốt thành công'
  },
  [DealStage.CLOSED_LOST]: {
    name: 'Thất bại',
    color: 'bg-rose-50 text-rose-700 dark:bg-rose-950 dark:text-rose-300',
    badgeColor: 'bg-rose-50 dark:bg-rose-950',
    icon: 'XIcon',
    description: 'Deal không thành công'
  }
}

// Sort order for pipeline stages
export const stageOrder = [
  DealStage.PROSPECTING,
  DealStage.QUALIFICATION,
  DealStage.NEEDS_ANALYSIS,
  DealStage.PROPOSAL,
  DealStage.NEGOTIATION,
  DealStage.CLOSED_WON,
  DealStage.CLOSED_LOST
]

// Format currency to Vietnamese format
export const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value)
}

// Format probability as percentage
export const formatProbability = (value: number): string => {
  return `${value}%`
}

// Get background color class based on probability value
export const getProbabilityColorClass = (probability: number): string => {
  if (probability >= 80) return 'bg-emerald-600 dark:bg-emerald-500'
  if (probability >= 60) return 'bg-lime-600 dark:bg-lime-500'
  if (probability >= 40) return 'bg-amber-600 dark:bg-amber-500'
  if (probability >= 20) return 'bg-orange-600 dark:bg-orange-500'
  return 'bg-rose-600 dark:bg-rose-500'
}

// Calculate weighted value based on deal value and probability
export const calculateWeightedValue = (value: number, probability: number): number => {
  return Math.round(value * (probability / 100))
} 