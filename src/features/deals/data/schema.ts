import { z } from 'zod'

// Đ<PERSON>nh nghĩa các enum cho Deal
export const DealStage = {
  PROSPECTING: 'prospecting',
  QUALIFICATION: 'qualification',
  NEEDS_ANALYSIS: 'needs_analysis',
  PROPOSAL: 'proposal',
  NEGOTIATION: 'negotiation',
  CLOSED_WON: 'closed_won',
  CLOSED_LOST: 'closed_lost'
} as const

export const DealPriority = {
  HIGH: 'high',
  MEDIUM: 'medium',
  LOW: 'low'
} as const

// Create type for the values from the constants
export type DealStageValue = typeof DealStage[keyof typeof DealStage];
export type DealPriorityValue = typeof DealPriority[keyof typeof DealPriority];

export const dealSchema = z.object({
  id: z.string(),
  name: z.string(),
  stage: z.string(), // Sử dụng các giá trị từ DealStage
  value: z.number(),
  probability: z.number(), // <PERSON><PERSON><PERSON> su<PERSON>t thành công từ 0-100
  expectedCloseDate: z.string(),
  accountId: z.string().optional(),
  accountName: z.string().optional(),
  campaignId: z.string().optional(), // ID chiến dịch nguồn
  campaignName: z.string().optional(), // Tên chiến dịch nguồn
  ownerId: z.string().optional(),
  ownerName: z.string().optional(),
  ownerAvatar: z.string().optional(),
  createdAt: z.string(),
  updatedAt: z.string(),
  lastActivity: z.string().optional(),
  description: z.string().optional(),
  priority: z.string().optional(), // Sử dụng các giá trị từ DealPriority
  tags: z.array(z.string()).optional(),
  // Thêm trường và metric liên quan đến forecasting
  weightedValue: z.number().optional(), // Giá trị có trọng số (value × probability)
  forecastCategory: z.string().optional(), // 'best_case', 'most_likely', 'worst_case'
})

export const dealListSchema = z.array(dealSchema)

export type Deal = z.infer<typeof dealSchema>

// Các loại dữ liệu bổ sung
export type DealActivity = {
  id: string
  dealId: string
  type: 'note' | 'email' | 'call' | 'meeting' | 'task' | 'product_demo' | 'proposal' | 'system'
  content: string
  timestamp: string
  user: string
  metadata?: Record<string, unknown>
}

export type DealProduct = {
  id: string
  dealId: string
  productId: string
  productName: string
  quantity: number
  unitPrice: number
  discount: number
  totalPrice: number // quantity * unitPrice - discount
  notes?: string
}

export type DealDocument = {
  id: string
  dealId: string
  type: 'contract' | 'proposal' | 'quote' | 'presentation' | 'other'
  name: string
  url: string
  createdBy: string
  createdAt: string
  updatedAt?: string
  size?: number // Kích thước tệp (byte)
  version?: string // Phiên bản tài liệu
}

export type DealTask = {
  id: string
  dealId: string
  title: string
  description?: string
  dueDate: string
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled'
  priority: 'low' | 'medium' | 'high'
  assignee?: string
  createdAt: string
  updatedAt?: string
}

export type DealEmail = {
  id: string
  dealId: string
  subject: string
  content: string
  sender: string
  recipients: string[]
  timestamp: string
  hasAttachments: boolean
  attachments?: { name: string, url: string }[]
}

export type DealCall = {
  id: string
  dealId: string
  type: 'incoming' | 'outgoing'
  duration: number // in seconds
  timestamp: string
  notes?: string
  recordingUrl?: string
  caller: string
  status: 'completed' | 'missed' | 'scheduled'
}

export type DealMeeting = {
  id: string
  dealId: string
  title: string
  description?: string
  startTime: string
  endTime: string
  location?: string
  attendees: string[]
  status: 'scheduled' | 'completed' | 'cancelled'
  notes?: string
} 