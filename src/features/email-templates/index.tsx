import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'

import { EmailTemplatesDialogs } from './components/email-templates-dialogs'
import { EmailTemplatesTable } from './components/email-templates-table'
import EmailTemplatesProvider from './context/email-templates-context'
import { useEmailTemplatesContext } from './context/use-email-templates-context'
import { EmailTemplate, emailTemplateListSchema } from './data/schema'
import { emailTemplates } from './data/email-templates'
import { columns } from './components/email-templates-columns'
import { useNavigate } from '@tanstack/react-router'
import { IconPlus, IconFilter } from '@tabler/icons-react'

// Component EmailTemplatesContent để có thể sử dụng useEmailTemplatesContext hook
function EmailTemplatesContent() {
  const { templates, showFilterPanel, setShowFilterPanel, setOpenDialog, setSelectedTemplateIds } = useEmailTemplatesContext()
  // Không cần state searchQuery vì sử dụng component Search
  const navigate = useNavigate()

  // Sử dụng tất cả templates vì đã có Search component trong header
  const filteredTemplates = templates

  // Xử lý khi click vào template
  const handleTemplateClick = (template: EmailTemplate) => {
    setSelectedTemplateIds([template.id])
    navigate({ to: `/email-templates/${template.id}` })
  }

  // Xử lý khi tạo template mới
  const handleCreateTemplate = () => {
    setOpenDialog('edit')
  }

  return (
    <>
      <Header fixed>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='mb-2 flex flex-wrap items-center justify-between space-y-2'>
          <div>
            <h2 className='text-2xl font-bold tracking-tight'>Mẫu Email</h2>
            <p className='text-muted-foreground'>
              Quản lý và tùy chỉnh các mẫu email cho chiến dịch marketing.
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              className="h-9 gap-1"
              onClick={handleCreateTemplate}
            >
              <IconPlus className="h-4 w-4" />
              <span className="hidden sm:inline">Tạo mẫu</span>
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="h-9 gap-1"
              onClick={() => setShowFilterPanel(!showFilterPanel)}
            >
              <IconFilter className="h-4 w-4" />
              <span className="hidden sm:inline">Bộ lọc</span>
            </Button>
          </div>
        </div>

        <div className='mb-4 flex items-center justify-between gap-2 overflow-x-auto'>
          <div className='flex items-center gap-2 flex-shrink-0'>
            <div className="rounded-md bg-muted px-3 py-1.5 text-sm text-muted-foreground">
              {templates.length} mẫu email
            </div>
          </div>
        </div>

        {showFilterPanel && (
          <Card className="mb-6">
            <CardContent className="pt-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Các bộ lọc sẽ được thêm vào đây */}
              </div>
            </CardContent>
          </Card>
        )}
        
        <EmailTemplatesTable
          columns={columns}
          data={filteredTemplates}
          onRowClick={handleTemplateClick}
        />
      </Main>

      <EmailTemplatesDialogs />
    </>
  )
}

export default function EmailTemplates() {
  // Parse template list
  const templateList = emailTemplateListSchema.parse(emailTemplates)

  return (
    <EmailTemplatesProvider initialTemplates={templateList}>
      <EmailTemplatesContent />
    </EmailTemplatesProvider>
  )
}
