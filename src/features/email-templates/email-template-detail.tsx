import { useState, useEffect } from 'react'
import { use<PERSON>ara<PERSON>, useNavigate } from '@tanstack/react-router'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'

import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'

import {
  IconArrowLeft,
  IconEdit,
  IconTrash,
  IconMail,
  IconEye,
  IconCopy,
  IconCheck,
  IconVariable,
  IconLink,
  IconChartBar,
  IconNotes,
  IconFileText,
  IconCalendarEvent
} from '@tabler/icons-react'
import { emailTemplates } from './data/email-templates'
import { EmailTemplate, EmailTemplateStatus } from './data/schema'
import { formatDate } from '@/utils/format'
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  Legend
} from 'recharts'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'

import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'

export default function EmailTemplateDetail() {
  const { id } = useParams({ from: '/_authenticated/email-templates/$id' })
  const navigate = useNavigate()
  const [template, setTemplate] = useState<EmailTemplate | null>(null)
  const [activeTab, setActiveTab] = useState('overview')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [copied, setCopied] = useState<string | null>(null)
  const [previewValues, setPreviewValues] = useState<Record<string, string>>({})

  useEffect(() => {
    // Simulate API call
    setLoading(true)
    try {
      const foundTemplate = emailTemplates.find(t => t.id === id)
      if (foundTemplate) {
        setTemplate(foundTemplate)
      } else {
        setError('Không tìm thấy mẫu email')
      }
    } catch (err) {
      setError('Có lỗi xảy ra khi tải dữ liệu')
    } finally {
      setLoading(false)
    }
  }, [id])

  const getStatusColor = (status: string) => {
    switch (status) {
      case EmailTemplateStatus.ACTIVE:
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case EmailTemplateStatus.DRAFT:
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
      case EmailTemplateStatus.ARCHIVED:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case EmailTemplateStatus.ACTIVE:
        return 'Đang hoạt động'
      case EmailTemplateStatus.DRAFT:
        return 'Bản nháp'
      case EmailTemplateStatus.ARCHIVED:
        return 'Đã lưu trữ'
      default:
        return status
    }
  }

  const handleBack = () => {
    navigate({ to: '/email-templates' })
  }

  const handleCopy = (text: string, type: string) => {
    navigator.clipboard.writeText(text)
    setCopied(type)
    setTimeout(() => setCopied(null), 2000)
  }

  // Thay thế các biến trong nội dung
  const replaceVariables = (content: string, values: Record<string, string>) => {
    let result = content
    Object.entries(values).forEach(([key, value]) => {
      result = result.replace(new RegExp(key, 'g'), value || key)
    })
    return result
  }

  // Tạo dữ liệu mẫu cho biểu đồ
  const generateChartData = () => {
    if (!template?.metrics) return []

    return [
      { name: 'Tháng 1', openRate: 45, clickRate: 20 },
      { name: 'Tháng 2', openRate: 50, clickRate: 25 },
      { name: 'Tháng 3', openRate: 55, clickRate: 30 },
      { name: 'Tháng 4', openRate: 60, clickRate: 35 },
      { name: 'Tháng 5', openRate: 65, clickRate: 40 },
      { name: 'Tháng 6', openRate: template.metrics?.openRate || 0, clickRate: template.metrics?.clickRate || 0 },
    ]
  }

  const chartData = generateChartData()

  // Dữ liệu cho biểu đồ tròn
  const pieData = template?.metrics?.openRate !== undefined ? [
    { name: 'Đã mở', value: template.metrics.openRate },
    { name: 'Chưa mở', value: 100 - template.metrics.openRate },
  ] : []

  // Màu cho biểu đồ
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042']

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4">Đang tải...</p>
        </div>
      </div>
    )
  }

  if (error || !template) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">{error || 'Không tìm thấy mẫu email'}</h2>
          <Button onClick={handleBack}>Quay lại danh sách</Button>
        </div>
      </div>
    )
  }

  return (
    <>
      <Header fixed>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className="mb-4 flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={handleBack}>
            <IconArrowLeft className="h-4 w-4 mr-2" />
            Quay lại
          </Button>
          <h2 className="text-2xl font-bold">{template.name}</h2>
          <Badge className={getStatusColor(template.status)}>
            {getStatusText(template.status)}
          </Badge>
          <div className="ml-auto flex gap-2">
            <Button variant="outline" size="sm">
              <IconEye className="h-4 w-4 mr-2" />
              Xem trước
            </Button>
            <Button variant="outline" size="sm">
              <IconEdit className="h-4 w-4 mr-2" />
              Chỉnh sửa
            </Button>
            <Button variant="destructive" size="sm">
              <IconTrash className="h-4 w-4 mr-2" />
              Xóa
            </Button>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="overview">
              <IconFileText className="h-4 w-4 mr-2" />
              Tổng quan
            </TabsTrigger>
            <TabsTrigger value="variables">
              <IconVariable className="h-4 w-4 mr-2" />
              Biến
            </TabsTrigger>
            <TabsTrigger value="activities">
              <IconCalendarEvent className="h-4 w-4 mr-2" />
              Hoạt động
            </TabsTrigger>
            <TabsTrigger value="links">
              <IconLink className="h-4 w-4 mr-2" />
              Liên kết
            </TabsTrigger>
            <TabsTrigger value="performance">
              <IconChartBar className="h-4 w-4 mr-2" />
              Hiệu suất
            </TabsTrigger>
            <TabsTrigger value="notes">
              <IconNotes className="h-4 w-4 mr-2" />
              Ghi chú
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Thông tin mẫu email</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Loại mẫu</p>
                      <p className="font-medium">{template.type}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Trạng thái</p>
                      <p className="font-medium">{getStatusText(template.status)}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Ngày tạo</p>
                      <p className="font-medium">{formatDate(template.createdAt)}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Cập nhật lần cuối</p>
                      <p className="font-medium">{template.updatedAt ? formatDate(template.updatedAt) : 'Chưa cập nhật'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Sử dụng lần cuối</p>
                      <p className="font-medium">{template.lastUsed ? formatDate(template.lastUsed) : 'Chưa sử dụng'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Số lần sử dụng</p>
                      <p className="font-medium">{template.usageCount || 0}</p>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Tiêu đề email</p>
                    <p className="font-medium">{template.subject}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Mô tả</p>
                    <p className="font-medium">{template.description || 'Chưa có mô tả'}</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Nội dung mẫu</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="border rounded-md p-4 bg-white dark:bg-gray-950 h-[400px] overflow-auto">
                    {template.htmlContent ? (
                      <div dangerouslySetInnerHTML={{ __html: template.htmlContent }} />
                    ) : (
                      <pre className="whitespace-pre-wrap">{template.content}</pre>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="variables" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Biến cá nhân hóa</CardTitle>
                <CardDescription>
                  Các biến có thể được sử dụng để cá nhân hóa nội dung email
                </CardDescription>
              </CardHeader>
              <CardContent>
                {template.variables && template.variables.length > 0 ? (
                  <div className="space-y-6">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Tên biến</TableHead>
                          <TableHead>Giá trị mặc định</TableHead>
                          <TableHead>Mô tả</TableHead>
                          <TableHead className="w-[100px]">Thao tác</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {template.variables.map((variable, index) => (
                          <TableRow key={index}>
                            <TableCell className="font-medium">{variable.name}</TableCell>
                            <TableCell>{variable.defaultValue || '-'}</TableCell>
                            <TableCell>{variable.description || '-'}</TableCell>
                            <TableCell>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleCopy(variable.name, `variable-${index}`)}
                              >
                                {copied === `variable-${index}` ? (
                                  <IconCheck className="h-4 w-4" />
                                ) : (
                                  <IconCopy className="h-4 w-4" />
                                )}
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>

                    <div className="space-y-4">
                      <h3 className="text-base font-medium">Xem trước với biến</h3>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {template.variables.map((variable, index) => (
                          <div key={index} className="space-y-2">
                            <Label htmlFor={`var-${index}`}>{variable.name}</Label>
                            <Input
                              id={`var-${index}`}
                              value={previewValues[variable.name] || ''}
                              onChange={(e) => setPreviewValues({
                                ...previewValues,
                                [variable.name]: e.target.value
                              })}
                              placeholder={variable.defaultValue || ''}
                            />
                          </div>
                        ))}
                      </div>

                      <div className="flex justify-between">
                        <Button
                          variant="outline"
                          onClick={() => {
                            const defaults = template.variables?.reduce((acc, v) => ({
                              ...acc,
                              [v.name]: v.defaultValue || ''
                            }), {}) || {}
                            setPreviewValues(defaults)
                          }}
                        >
                          Sử dụng giá trị mặc định
                        </Button>
                        <Button
                          variant="outline"
                          onClick={() => setPreviewValues({})}
                        >
                          Xóa tất cả
                        </Button>
                      </div>

                      <Card>
                        <CardHeader>
                          <CardTitle className="text-base">Kết quả xem trước</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="border rounded-md p-4 bg-white dark:bg-gray-950">
                            <div className="font-medium mb-2">
                              Tiêu đề: {replaceVariables(template.subject, previewValues)}
                            </div>
                            <Separator className="my-2" />
                            {template.htmlContent ? (
                              <div dangerouslySetInnerHTML={{
                                __html: replaceVariables(template.htmlContent, previewValues)
                              }} />
                            ) : (
                              <pre className="whitespace-pre-wrap">
                                {replaceVariables(template.content, previewValues)}
                              </pre>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <IconVariable className="h-12 w-12 mx-auto mb-4 opacity-20" />
                    <p>Mẫu email này không có biến cá nhân hóa nào</p>
                    <Button variant="link" size="sm" className="mt-2">
                      Thêm biến mới
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="activities" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Lịch sử hoạt động</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  <IconMail className="h-12 w-12 mx-auto mb-4 opacity-20" />
                  <p>Chưa có hoạt động nào</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="links" className="space-y-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Chiến dịch sử dụng mẫu này</CardTitle>
                  <CardDescription>
                    Các chiến dịch email đã sử dụng mẫu này
                  </CardDescription>
                </div>
                <Button size="sm">
                  <IconMail className="h-4 w-4 mr-2" />
                  Tạo chiến dịch mới
                </Button>
              </CardHeader>
              <CardContent>
                {template.campaignIds && template.campaignIds.length > 0 ? (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <p className="text-sm text-muted-foreground">
                        Hiển thị {template.campaignIds.length} chiến dịch
                      </p>
                      <Button variant="outline" size="sm">
                        Xem tất cả
                      </Button>
                    </div>

                    <div className="space-y-2">
                      {template.campaignIds.map((campaignId, index) => (
                        <div
                          key={campaignId}
                          className="flex items-center justify-between p-3 border rounded-md hover:bg-muted/50 transition-colors"
                        >
                          <div className="flex items-center gap-3">
                            <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10">
                              <IconMail className="h-4 w-4 text-primary" />
                            </div>
                            <div>
                              <p className="font-medium">Chiến dịch #{index + 1}</p>
                              <p className="text-xs text-muted-foreground">ID: {campaignId}</p>
                            </div>
                          </div>
                          <Button variant="ghost" size="sm">
                            Xem chi tiết
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <IconLink className="h-12 w-12 mx-auto mb-4 opacity-20" />
                    <p>Chưa có chiến dịch nào sử dụng mẫu này</p>
                    <Button variant="link" size="sm" className="mt-2">
                      Tạo chiến dịch mới với mẫu này
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Tài liệu đính kèm</CardTitle>
                <CardDescription>
                  Các tài liệu được đính kèm với mẫu email này
                </CardDescription>
              </CardHeader>
              <CardContent>
                {template.attachments && template.attachments.length > 0 ? (
                  <div className="space-y-2">
                    {template.attachments.map((attachment) => (
                      <div
                        key={attachment.id}
                        className="flex items-center justify-between p-3 border rounded-md hover:bg-muted/50 transition-colors"
                      >
                        <div className="flex items-center gap-3">
                          <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10">
                            <IconFileText className="h-4 w-4 text-primary" />
                          </div>
                          <div>
                            <p className="font-medium">{attachment.name}</p>
                            <p className="text-xs text-muted-foreground">
                              {attachment.size ? Math.round(attachment.size / 1024) + ' KB' : 'N/A'}
                            </p>
                          </div>
                        </div>
                        <Button variant="ghost" size="sm">
                          Tải xuống
                        </Button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <IconFileText className="h-12 w-12 mx-auto mb-4 opacity-20" />
                    <p>Không có tài liệu đính kèm nào</p>
                    <Button variant="link" size="sm" className="mt-2">
                      Thêm tài liệu đính kèm
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="performance" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card className="md:col-span-4">
                <CardHeader className="pb-2">
                  <CardTitle>Tổng quan hiệu suất</CardTitle>
                  <CardDescription>
                    Hiệu suất của mẫu email trong các chiến dịch
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {template.metrics ? (
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="p-4 border rounded-md">
                        <p className="text-sm text-muted-foreground">Tỷ lệ mở</p>
                        <p className="text-2xl font-bold">{template.metrics.openRate || 0}%</p>
                      </div>
                      <div className="p-4 border rounded-md">
                        <p className="text-sm text-muted-foreground">Tỷ lệ nhấp</p>
                        <p className="text-2xl font-bold">{template.metrics.clickRate || 0}%</p>
                      </div>
                      <div className="p-4 border rounded-md">
                        <p className="text-sm text-muted-foreground">Tỷ lệ chuyển đổi</p>
                        <p className="text-2xl font-bold">{template.metrics.conversionRate || 0}%</p>
                      </div>
                      <div className="p-4 border rounded-md">
                        <p className="text-sm text-muted-foreground">Tỷ lệ trả lại</p>
                        <p className="text-2xl font-bold">{template.metrics.bounceRate || 0}%</p>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <IconChartBar className="h-12 w-12 mx-auto mb-4 opacity-20" />
                      <p>Chưa có dữ liệu hiệu suất</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {template.metrics && (
                <>
                  <Card className="md:col-span-3">
                    <CardHeader className="pb-2">
                      <CardTitle>Xu hướng theo thời gian</CardTitle>
                    </CardHeader>
                    <CardContent className="h-[300px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart
                          data={chartData}
                          margin={{
                            top: 5,
                            right: 30,
                            left: 20,
                            bottom: 5,
                          }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <RechartsTooltip />
                          <Legend />
                          <Line
                            type="monotone"
                            dataKey="openRate"
                            stroke="#8884d8"
                            name="Tỷ lệ mở"
                            activeDot={{ r: 8 }}
                          />
                          <Line
                            type="monotone"
                            dataKey="clickRate"
                            stroke="#82ca9d"
                            name="Tỷ lệ nhấp"
                          />
                        </LineChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle>Tỷ lệ mở</CardTitle>
                    </CardHeader>
                    <CardContent className="h-[300px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={pieData}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="value"
                            label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          >
                            {pieData.map((_, index) => (
                              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                          </Pie>
                          <RechartsTooltip />
                          <Legend />
                        </PieChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>

                  <Card className="md:col-span-2">
                    <CardHeader className="pb-2">
                      <CardTitle>Phân tích theo thiết bị</CardTitle>
                    </CardHeader>
                    <CardContent className="h-[300px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={[
                            { name: 'Desktop', value: 45 },
                            { name: 'Mobile', value: 40 },
                            { name: 'Tablet', value: 15 },
                          ]}
                          margin={{
                            top: 5,
                            right: 30,
                            left: 20,
                            bottom: 5,
                          }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <RechartsTooltip />
                          <Legend />
                          <Bar dataKey="value" name="Tỷ lệ (%)" fill="#8884d8" />
                        </BarChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>

                  <Card className="md:col-span-2">
                    <CardHeader className="pb-2">
                      <CardTitle>Phân tích theo vị trí</CardTitle>
                    </CardHeader>
                    <CardContent className="h-[300px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={[
                            { name: 'Hà Nội', value: 35 },
                            { name: 'TP.HCM', value: 40 },
                            { name: 'Đà Nẵng', value: 10 },
                            { name: 'Khác', value: 15 },
                          ]}
                          margin={{
                            top: 5,
                            right: 30,
                            left: 20,
                            bottom: 5,
                          }}
                          layout="vertical"
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis type="number" />
                          <YAxis dataKey="name" type="category" />
                          <RechartsTooltip />
                          <Legend />
                          <Bar dataKey="value" name="Tỷ lệ (%)" fill="#82ca9d" />
                        </BarChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </>
              )}
            </div>
          </TabsContent>

          <TabsContent value="notes" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Ghi chú</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  <p>Chức năng ghi chú đang được phát triển</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </Main>
    </>
  )
}
