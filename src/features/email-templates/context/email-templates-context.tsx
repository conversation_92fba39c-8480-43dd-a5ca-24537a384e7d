import { createContext, useState, ReactNode } from 'react'
import { EmailTemplate } from '../data/schema'

// Đ<PERSON>nh nghĩa kiểu dữ liệu cho context
export interface EmailTemplatesContextType {
  // Quản lý dialog
  openDialog: string
  setOpenDialog: (dialog: string) => void
  
  // Quản lý template được chọn
  selectedTemplateIds: string[]
  setSelectedTemplateIds: (ids: string[]) => void
  
  // Quản lý bộ lọc
  showFilterPanel: boolean
  setShowFilterPanel: (show: boolean) => void
  
  // Quản lý dữ liệu template
  templates: EmailTemplate[]
  setTemplates: (templates: EmailTemplate[]) => void
  
  // Quản lý template yêu thích
  favoriteTemplateIds: string[]
  toggleFavoriteTemplate: (id: string) => void
  
  // <PERSON>ác hàm xử lý template
  addTemplate: (template: EmailTemplate) => void
  updateTemplate: (id: string, template: Partial<EmailTemplate>) => void
  deleteTemplate: (id: string) => void
  duplicateTemplate: (id: string) => void
  archiveTemplate: (id: string) => void
  activateTemplate: (id: string) => void
}

// Tạo context
export const EmailTemplatesContext = createContext<EmailTemplatesContextType | undefined>(undefined)

// Props cho provider
interface EmailTemplatesProviderProps {
  children: ReactNode
  initialTemplates: EmailTemplate[]
}

// Provider component
export default function EmailTemplatesProvider({
  children,
  initialTemplates,
}: EmailTemplatesProviderProps) {
  // State cho dialog
  const [openDialog, setOpenDialog] = useState('')
  
  // State cho template được chọn
  const [selectedTemplateIds, setSelectedTemplateIds] = useState<string[]>([])
  
  // State cho bộ lọc
  const [showFilterPanel, setShowFilterPanel] = useState(false)
  
  // State cho dữ liệu template
  const [templates, setTemplates] = useState<EmailTemplate[]>(initialTemplates)
  
  // State cho template yêu thích
  const [favoriteTemplateIds, setFavoriteTemplateIds] = useState<string[]>([])
  
  // Toggle template yêu thích
  const toggleFavoriteTemplate = (id: string) => {
    setFavoriteTemplateIds((prev) => {
      if (prev.includes(id)) {
        return prev.filter((templateId) => templateId !== id)
      } else {
        return [...prev, id]
      }
    })
  }
  
  // Thêm template mới
  const addTemplate = (template: EmailTemplate) => {
    setTemplates((prev) => [...prev, template])
  }
  
  // Cập nhật template
  const updateTemplate = (id: string, template: Partial<EmailTemplate>) => {
    setTemplates((prev) =>
      prev.map((item) => (item.id === id ? { ...item, ...template } : item))
    )
  }
  
  // Xóa template
  const deleteTemplate = (id: string) => {
    setTemplates((prev) => prev.filter((template) => template.id !== id))
  }
  
  // Nhân bản template
  const duplicateTemplate = (id: string) => {
    const template = templates.find((template) => template.id === id)
    if (template) {
      const newTemplate: EmailTemplate = {
        ...template,
        id: `${Date.now()}`,
        name: `${template.name} (Bản sao)`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        usageCount: 0,
        lastUsed: undefined
      }
      addTemplate(newTemplate)
    }
  }
  
  // Lưu trữ template
  const archiveTemplate = (id: string) => {
    updateTemplate(id, { status: 'archived', updatedAt: new Date().toISOString() })
  }
  
  // Kích hoạt template
  const activateTemplate = (id: string) => {
    updateTemplate(id, { status: 'active', updatedAt: new Date().toISOString() })
  }
  
  // Giá trị context
  const contextValue: EmailTemplatesContextType = {
    openDialog,
    setOpenDialog,
    selectedTemplateIds,
    setSelectedTemplateIds,
    showFilterPanel,
    setShowFilterPanel,
    templates,
    setTemplates,
    favoriteTemplateIds,
    toggleFavoriteTemplate,
    addTemplate,
    updateTemplate,
    deleteTemplate,
    duplicateTemplate,
    archiveTemplate,
    activateTemplate
  }
  
  return (
    <EmailTemplatesContext.Provider value={contextValue}>
      {children}
    </EmailTemplatesContext.Provider>
  )
}
