import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { Card, CardContent } from '@/components/ui/card'
import { EmailTemplate } from '../data/schema'
import { personalizationVariables } from '../data/data'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'

interface EmailTemplatePreviewProps {
  template: EmailTemplate
}

export function EmailTemplatePreview({ template }: EmailTemplatePreviewProps) {
  const [activeTab, setActiveTab] = useState('preview')
  const [previewValues, setPreviewValues] = useState<Record<string, string>>({})

  // Tìm tất cả các biến trong nội dung
  const findVariables = (content: string) => {
    const regex = /\{([^}]+)\}/g
    const matches = []
    let match
    
    while ((match = regex.exec(content)) !== null) {
      matches.push(match[0])
    }
    
    return [...new Set(matches)]
  }

  const textVariables = findVariables(template.content)
  const htmlVariables = template.htmlContent ? findVariables(template.htmlContent) : []
  const allVariables = [...new Set([...textVariables, ...htmlVariables])]

  // Thay thế các biến trong nội dung
  const replaceVariables = (content: string, values: Record<string, string>) => {
    let result = content
    Object.entries(values).forEach(([key, value]) => {
      result = result.replace(new RegExp(key, 'g'), value || key)
    })
    return result
  }

  const previewTextContent = replaceVariables(template.content, previewValues)
  const previewHtmlContent = template.htmlContent 
    ? replaceVariables(template.htmlContent, previewValues)
    : ''

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-3 gap-4">
        <div className="col-span-2">
          <h3 className="text-lg font-medium">Xem trước: {template.name}</h3>
          <p className="text-sm text-muted-foreground">{template.description}</p>
        </div>
        <div className="text-right">
          <Badge variant="outline" className="mr-2">
            {template.type.replace('_', ' ')}
          </Badge>
          <Badge variant={
            template.status === 'active' ? 'success' :
            template.status === 'draft' ? 'warning' : 'secondary'
          }>
            {template.status === 'active' ? 'Hoạt động' :
             template.status === 'draft' ? 'Bản nháp' : 'Đã lưu trữ'}
          </Badge>
        </div>
      </div>

      <div className="grid grid-cols-3 gap-6">
        <div className="space-y-4">
          <h4 className="font-medium">Biến cá nhân hóa</h4>
          {allVariables.length > 0 ? (
            <div className="space-y-3">
              {allVariables.map((variable) => {
                const varInfo = personalizationVariables.find(v => v.name === variable)
                return (
                  <div key={variable} className="space-y-1">
                    <Label htmlFor={variable}>{variable}</Label>
                    <Input
                      id={variable}
                      placeholder={varInfo?.description || 'Giá trị thay thế'}
                      value={previewValues[variable] || ''}
                      onChange={(e) => setPreviewValues({
                        ...previewValues,
                        [variable]: e.target.value
                      })}
                    />
                  </div>
                )
              })}
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setPreviewValues({})}
                className="w-full"
              >
                Đặt lại
              </Button>
            </div>
          ) : (
            <p className="text-sm text-muted-foreground">
              Không có biến cá nhân hóa nào trong mẫu này.
            </p>
          )}
        </div>

        <div className="col-span-2">
          <Card>
            <CardContent className="p-0">
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="w-full">
                  <TabsTrigger value="preview" className="flex-1">Xem trước</TabsTrigger>
                  <TabsTrigger value="text" className="flex-1">Văn bản thuần</TabsTrigger>
                  {template.htmlContent && (
                    <TabsTrigger value="html" className="flex-1">HTML</TabsTrigger>
                  )}
                </TabsList>
                <TabsContent value="preview" className="p-4">
                  <div className="space-y-2">
                    <div className="border-b pb-2">
                      <div className="font-medium">Từ: Công ty ABC &lt;<EMAIL>&gt;</div>
                      <div className="font-medium">Đến: Người nhận &lt;<EMAIL>&gt;</div>
                      <div className="font-medium">Tiêu đề: {replaceVariables(template.subject, previewValues)}</div>
                    </div>
                    {template.htmlContent ? (
                      <div dangerouslySetInnerHTML={{ __html: previewHtmlContent }} />
                    ) : (
                      <div className="whitespace-pre-line">{previewTextContent}</div>
                    )}
                  </div>
                </TabsContent>
                <TabsContent value="text" className="p-4">
                  <pre className="whitespace-pre-wrap font-mono text-sm">{previewTextContent}</pre>
                </TabsContent>
                {template.htmlContent && (
                  <TabsContent value="html" className="p-4">
                    <pre className="whitespace-pre-wrap font-mono text-sm overflow-auto max-h-[400px]">{previewHtmlContent}</pre>
                  </TabsContent>
                )}
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
