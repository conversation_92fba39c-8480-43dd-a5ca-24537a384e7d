import { useState } from 'react'
import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { EmailTemplate } from '../data/schema'
import { useEmailTemplatesContext } from '../context/use-email-templates-context'
import { emailTemplateTypes, emailTemplateStatuses } from '../data/data'

interface EmailTemplateEditorProps {
  template: EmailTemplate
  onClose: () => void
}

const formSchema = z.object({
  name: z.string().min(1, 'Tên mẫu email không được để trống'),
  subject: z.string().min(1, 'Tiêu đề email không được để trống'),
  type: z.string(),
  status: z.string(),
  content: z.string().min(1, 'Nội dung email không được để trống'),
  htmlContent: z.string().optional(),
  description: z.string().optional(),
})

export function EmailTemplateEditor({ template, onClose }: EmailTemplateEditorProps) {
  const { updateTemplate } = useEmailTemplatesContext()
  const [activeTab, setActiveTab] = useState('text')

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: template.name,
      subject: template.subject,
      type: template.type,
      status: template.status,
      content: template.content,
      htmlContent: template.htmlContent || '',
      description: template.description || '',
    },
  })

  function onSubmit(values: z.infer<typeof formSchema>) {
    updateTemplate(template.id, {
      ...values,
      updatedAt: new Date().toISOString(),
    })
    onClose()
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Tên mẫu</FormLabel>
                <FormControl>
                  <Input placeholder="Nhập tên mẫu email" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="subject"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Tiêu đề</FormLabel>
                <FormControl>
                  <Input placeholder="Nhập tiêu đề email" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Loại mẫu</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Chọn loại mẫu email" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {emailTemplateTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Trạng thái</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Chọn trạng thái" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {emailTemplateStatuses.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        {status.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Mô tả</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Nhập mô tả về mẫu email này"
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="text">Nội dung văn bản</TabsTrigger>
            <TabsTrigger value="html">Nội dung HTML</TabsTrigger>
          </TabsList>
          <TabsContent value="text">
            <FormField
              control={form.control}
              name="content"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nội dung văn bản</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Nhập nội dung email dạng văn bản thuần"
                      className="min-h-[300px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Sử dụng các biến như {'{name}'}, {'{company}'} để cá nhân hóa email.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </TabsContent>
          <TabsContent value="html">
            <FormField
              control={form.control}
              name="htmlContent"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nội dung HTML</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Nhập nội dung email dạng HTML"
                      className="min-h-[300px] font-mono text-sm"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Sử dụng các biến như {'{name}'}, {'{company}'} để cá nhân hóa email.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </TabsContent>
        </Tabs>

        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={onClose} type="button">
            Hủy
          </Button>
          <Button type="submit">Lưu thay đổi</Button>
        </div>
      </form>
    </Form>
  )
}
