import { ColumnDef } from '@tanstack/react-table'
import { Checkbox } from '@/components/ui/checkbox'
import { EmailTemplate } from '../data/schema'
import { DataTableColumnHeader } from '@/components/ui/data-table'
import { DataTableRowActions } from './data-table-row-actions'
import { StatusBadge } from '@/components/ui/status-badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { formatDate } from '@/utils/format'
import { Badge } from '@/components/ui/badge'
import { Mail } from 'lucide-react'

export const columns: ColumnDef<EmailTemplate>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
        className="translate-y-[2px]"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
        className="translate-y-[2px]"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Tên mẫu" />
    ),
    cell: ({ row }) => {
      const template = row.original
      return (
        <div className="flex items-center gap-2">
          <Avatar className="h-8 w-8">
            <AvatarImage src="" alt={template.name} />
            <AvatarFallback className="bg-primary/10 text-primary">
              <Mail className="h-4 w-4" />
            </AvatarFallback>
          </Avatar>
          <div className="font-medium">{template.name}</div>
        </div>
      )
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'subject',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Tiêu đề" />
    ),
    cell: ({ row }) => {
      return (
        <div className="max-w-[300px] truncate">
          {row.getValue('subject')}
        </div>
      )
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'type',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Loại" />
    ),
    cell: ({ row }) => {
      const type = row.getValue('type') as string
      return (
        <Badge variant="outline" className="capitalize">
          {type.replace('_', ' ')}
        </Badge>
      )
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'status',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Trạng thái" />
    ),
    cell: ({ row }) => {
      const status = row.getValue('status') as string
      return (
        <StatusBadge
          status={status}
          statusMap={{
            active: { label: 'Hoạt động', variant: 'success' },
            draft: { label: 'Bản nháp', variant: 'warning' },
            archived: { label: 'Đã lưu trữ', variant: 'secondary' },
          }}
        />
      )
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'usageCount',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Lượt sử dụng" />
    ),
    cell: ({ row }) => {
      const usageCount = row.getValue('usageCount') as number
      return (
        <div className="text-right font-medium">
          {usageCount || 0}
        </div>
      )
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'updatedAt',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Cập nhật" />
    ),
    cell: ({ row }) => {
      const updatedAt = row.getValue('updatedAt') as string
      return (
        <div className="text-muted-foreground">
          {formatDate(updatedAt)}
        </div>
      )
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      const template = row.original
      return (
        <DataTableRowActions
          row={row}
          actions={[
            {
              label: 'Chỉnh sửa',
              action: 'edit',
            },
            {
              label: 'Xem trước',
              action: 'preview',
            },
            {
              label: 'Nhân bản',
              action: 'duplicate',
            },
            {
              label: 'Lưu trữ',
              action: 'archive',
              show: template.status !== 'archived',
            },
            {
              label: 'Kích hoạt',
              action: 'activate',
              show: template.status === 'archived',
            },
            {
              label: 'Xóa',
              action: 'delete',
              destructive: true,
            },
          ]}
        />
      )
    },
  },
]
