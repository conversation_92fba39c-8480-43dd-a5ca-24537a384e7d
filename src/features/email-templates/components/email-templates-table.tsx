import { ColumnDef } from '@tanstack/react-table'
import { DataTable } from '@/components/ui/data-table'

interface EmailTemplatesTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  onRowClick?: (row: TData) => void
}

export function EmailTemplatesTable<TData, TValue>({
  columns,
  data,
  onRowClick,
}: EmailTemplatesTableProps<TData, TValue>) {
  return (
    <DataTable
      columns={columns}
      data={data}
      searchKey="name"
      searchPlaceholder="Tìm kiếm template..."
      onRowClick={onRowClick}
    />
  )
}
