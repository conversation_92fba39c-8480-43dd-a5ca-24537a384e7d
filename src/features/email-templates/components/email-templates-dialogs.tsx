import { useEmailTemplatesContext } from '../context/use-email-templates-context'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { useState } from 'react'
import { EmailTemplateEditor } from './email-template-editor'
import { EmailTemplatePreview } from './email-template-preview'

export function EmailTemplatesDialogs() {
  const {
    openDialog,
    setOpenDialog,
    selectedTemplateIds,
    deleteTemplate,
    templates,
  } = useEmailTemplatesContext()

  const handleClose = () => {
    setOpenDialog('')
  }

  const handleDelete = () => {
    selectedTemplateIds.forEach((id) => {
      deleteTemplate(id)
    })
    handleClose()
  }

  // Lấy thông tin template đang được chọn
  const selectedTemplate = selectedTemplateIds.length === 1
    ? templates.find((template) => template.id === selectedTemplateIds[0])
    : null

  return (
    <>
      {/* Dialog xóa template */}
      <Dialog open={openDialog === 'delete'} onOpenChange={handleClose}>
        <DialogContent className="max-w-md max-h-[90vh] h-auto">
          <DialogHeader>
            <DialogTitle>Xác nhận xóa</DialogTitle>
            <DialogDescription>
              {selectedTemplateIds.length > 1
                ? `Bạn có chắc chắn muốn xóa ${selectedTemplateIds.length} mẫu email đã chọn không?`
                : 'Bạn có chắc chắn muốn xóa mẫu email này không?'}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={handleClose}>
              Hủy
            </Button>
            <Button variant="destructive" onClick={handleDelete}>
              Xóa
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog chỉnh sửa template */}
      <Dialog open={openDialog === 'edit'} onOpenChange={handleClose}>
        <DialogContent className="max-w-5xl max-h-[90vh] h-auto overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Chỉnh sửa mẫu email</DialogTitle>
            <DialogDescription>
              Chỉnh sửa nội dung và thông tin của mẫu email
            </DialogDescription>
          </DialogHeader>
          {selectedTemplate && <EmailTemplateEditor template={selectedTemplate} onClose={handleClose} />}
        </DialogContent>
      </Dialog>

      {/* Dialog xem trước template */}
      <Dialog open={openDialog === 'preview'} onOpenChange={handleClose}>
        <DialogContent className="max-w-5xl max-h-[90vh] h-auto overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Xem trước mẫu email</DialogTitle>
            <DialogDescription>
              Xem trước mẫu email trước khi sử dụng
            </DialogDescription>
          </DialogHeader>
          {selectedTemplate && <EmailTemplatePreview template={selectedTemplate} />}
          <DialogFooter>
            <Button variant="outline" onClick={handleClose}>
              Đóng
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
