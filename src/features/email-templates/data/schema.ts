import { z } from 'zod'

// Enum for template types
export const EmailTemplateType = {
  WELCOME: 'welcome',
  NEWSLETTER: 'newsletter',
  PROMOTIONAL: 'promotional',
  FOLLOW_UP: 'follow_up',
  EVENT_INVITATION: 'event_invitation',
  THANK_YOU: 'thank_you',
  OTHER: 'other'
} as const

// Enum for template status
export const EmailTemplateStatus = {
  ACTIVE: 'active',
  DRAFT: 'draft',
  ARCHIVED: 'archived'
} as const

// Schema for email template
export const emailTemplateSchema = z.object({
  id: z.string(),
  name: z.string().min(1, "Tên mẫu email không được để trống"),
  subject: z.string().min(1, "Tiêu đề email không được để trống"),
  type: z.string(), // Sử dụng các giá trị từ EmailTemplateType
  status: z.string(), // Sử dụng các giá trị từ EmailTemplateStatus
  content: z.string().min(1, "Nội dung email không được để trống"),
  htmlContent: z.string().optional(),
  description: z.string().optional(),
  createdBy: z.string().optional(),
  createdAt: z.string(),
  updatedAt: z.string().optional(),
  lastUsed: z.string().optional(),
  usageCount: z.number().optional(),
  tags: z.array(z.string()).optional(),
  
  // Personalization variables
  variables: z.array(z.object({
    name: z.string(),
    defaultValue: z.string().optional(),
    description: z.string().optional()
  })).optional(),
  
  // Campaign associations
  campaignIds: z.array(z.string()).optional(),
  
  // Attachments
  attachments: z.array(z.object({
    id: z.string(),
    name: z.string(),
    type: z.string(),
    url: z.string(),
    size: z.number().optional(),
  })).optional(),
  
  // Performance metrics
  metrics: z.object({
    openRate: z.number().optional(),
    clickRate: z.number().optional(),
    conversionRate: z.number().optional(),
    bounceRate: z.number().optional(),
  }).optional(),
})

export const emailTemplateListSchema = z.array(emailTemplateSchema)

// Types
export type EmailTemplateTypeType = typeof EmailTemplateType[keyof typeof EmailTemplateType]
export type EmailTemplateStatusType = typeof EmailTemplateStatus[keyof typeof EmailTemplateStatus]
export type EmailTemplate = z.infer<typeof emailTemplateSchema>
