import { EmailTemplateStatus, EmailTemplateType } from './schema'

// <PERSON><PERSON>c lo<PERSON>i mẫu email
export const emailTemplateTypes = [
  {
    value: EmailTemplateType.WELCOME,
    label: 'Chào mừng',
    description: '<PERSON><PERSON><PERSON> cho khách hàng mới'
  },
  {
    value: EmailTemplateType.NEWSLETTER,
    label: 'Bản tin',
    description: '<PERSON>ập nhật tin tức định kỳ'
  },
  {
    value: EmailTemplateType.PROMOTIONAL,
    label: '<PERSON>hu<PERSON>ến mãi',
    description: 'Thông báo ưu đãi, giảm giá'
  },
  {
    value: EmailTemplateType.FOLLOW_UP,
    label: '<PERSON> dõi',
    description: '<PERSON><PERSON><PERSON> hệ sau giao dịch'
  },
  {
    value: EmailTemplateType.EVENT_INVITATION,
    label: '<PERSON><PERSON><PERSON> sự kiện',
    description: 'Mờ<PERSON> tham dự sự kiện'
  },
  {
    value: EmailTemplateType.THANK_YOU,
    label: '<PERSON>ảm ơn',
    description: 'Cảm ơn khách hàng'
  },
  {
    value: EmailTemplateType.OTHER,
    label: '<PERSON><PERSON><PERSON><PERSON>',
    description: 'Loại mẫu khác'
  }
]

// Trạng thái mẫu email
export const emailTemplateStatuses = [
  {
    value: EmailTemplateStatus.ACTIVE,
    label: 'Đang hoạt động',
    color: 'success'
  },
  {
    value: EmailTemplateStatus.DRAFT,
    label: 'Bản nháp',
    color: 'warning'
  },
  {
    value: EmailTemplateStatus.ARCHIVED,
    label: 'Đã lưu trữ',
    color: 'secondary'
  }
]

// Các biến cá nhân hóa
export const personalizationVariables = [
  { name: '{name}', description: 'Tên khách hàng' },
  { name: '{company}', description: 'Tên công ty' },
  { name: '{product}', description: 'Tên sản phẩm' },
  { name: '{date}', description: 'Ngày tháng' },
  { name: '{amount}', description: 'Số tiền' },
  { name: '{link}', description: 'Đường dẫn' },
  { name: '{email}', description: 'Email khách hàng' },
  { name: '{phone}', description: 'Số điện thoại' }
]
