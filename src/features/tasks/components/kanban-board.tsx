import React from 'react'
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd'
import { statuses, priorities, labels } from '../data/data'
import { useTasks } from '../context/tasks-context'
import { Task } from '../data/schema'
import { StatusBadge } from '@/components/ui/status-badge'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'

// Helper function to reorder tasks
const reorder = (list: Task[], startIndex: number, endIndex: number): Task[] => {
  const result = Array.from(list)
  const [removed] = result.splice(startIndex, 1)
  result.splice(endIndex, 0, removed)
  return result
}

// Helper function to move task between columns
const move = (
  source: Task[],
  destination: Task[],
  droppableSource: { index: number; droppableId: string },
  droppableDestination: { index: number; droppableId: string }
): { [key: string]: Task[] } => {
  const sourceClone = Array.from(source)
  const destClone = Array.from(destination)
  const [removed] = sourceClone.splice(droppableSource.index, 1)
  
  // Create a new task with updated status
  const updatedTask = {
    ...removed,
    status: droppableDestination.droppableId
  }
  
  destClone.splice(droppableDestination.index, 0, updatedTask)
  
  const result: { [key: string]: Task[] } = {}
  result[droppableSource.droppableId] = sourceClone
  result[droppableDestination.droppableId] = destClone
  
  return result
}

// A styled TaskCard component that maintains the same appearance as before
const TaskCard = ({ task }: { task: Task }) => {
  return (
    <div className="flex-1 bg-card rounded-md p-3 shadow-sm border border-border hover:border-border/80 transition-colors">
      <div className="space-y-2.5">
        <div className="flex justify-between items-center">
          <Badge variant="outline" className="text-xs font-normal">
            {task.id}
          </Badge>
          <StatusBadge 
            status={statuses.find(st => st.value === task.status)?.label || task.status} 
            variant={
              task.status === 'done' ? 'done' : 
              task.status === 'in progress' ? 'inProgress' : 
              task.status === 'todo' ? 'todo' : 
              task.status === 'canceled' ? 'cancelled' : 'default'
            }
            className="h-5 px-1.5 text-[10px]"
          />
        </div>
        <h4 className="text-xs font-medium text-foreground line-clamp-2">
          {task.title}
        </h4>
        <div className="flex flex-wrap gap-1.5">
          <Badge variant="secondary" className="text-[10px] font-normal px-1.5 py-0 h-5">
            {labels.find(l => l.value === task.label)?.label || task.label}
          </Badge>
          <Badge 
            variant="outline" 
            className={cn(
              "text-[10px] font-normal px-1.5 py-0 h-5",
              task.priority === 'high' ? "border-red-200 bg-red-50 text-red-700 dark:border-red-800 dark:bg-red-950 dark:text-red-300" :
              task.priority === 'medium' ? "border-amber-200 bg-amber-50 text-amber-700 dark:border-amber-800 dark:bg-amber-950 dark:text-amber-300" :
              "border-blue-200 bg-blue-50 text-blue-700 dark:border-blue-800 dark:bg-blue-950 dark:text-blue-300"
            )}
          >
            <div className="flex items-center">
              {priorities.find(p => p.value === task.priority)?.icon && 
                React.createElement(
                  priorities.find(p => p.value === task.priority)?.icon || (() => null), 
                  { className: "h-3 w-3 mr-1" }
                )
              }
              <span>{priorities.find(p => p.value === task.priority)?.label || task.priority}</span>
            </div>
          </Badge>
        </div>
      </div>
    </div>
  )
}

// The main KanbanBoard component using @hello-pangea/dnd
export function KanbanBoard() {
  const { tasks, updateTaskStatus, setTasks } = useTasks()
  
  // Group tasks by status
  const tasksByStatus = statuses.reduce<Record<string, Task[]>>((acc, status) => {
    acc[status.value] = tasks.filter(task => task.status === status.value)
    return acc
  }, {})
  
  // Handle drag end
  const onDragEnd = (result: DropResult) => {
    const { source, destination } = result
    
    // Dropped outside the list
    if (!destination) {
      return
    }
    
    // Same position
    if (
      source.droppableId === destination.droppableId &&
      source.index === destination.index
    ) {
      return
    }
    
    // Moving within the same column
    if (source.droppableId === destination.droppableId) {
      const reorderedTasks = reorder(
        tasksByStatus[source.droppableId],
        source.index,
        destination.index
      )
      
      // Update state
      const newTasksByStatus = {
        ...tasksByStatus,
        [source.droppableId]: reorderedTasks
      }
      
      // Flatten and update tasks
      const updatedTasks = Object.values(newTasksByStatus).flat()
      setTasks(updatedTasks)
    } 
    // Moving between columns
    else {
      const result = move(
        tasksByStatus[source.droppableId],
        tasksByStatus[destination.droppableId],
        source,
        destination
      )
      
      // Update state
      const newTasksByStatus = {
        ...tasksByStatus,
        [source.droppableId]: result[source.droppableId],
        [destination.droppableId]: result[destination.droppableId]
      }
      
      // Flatten and update tasks
      const updatedTasks = Object.values(newTasksByStatus).flat()
      setTasks(updatedTasks)
      
      // Update specific task status
      const taskId = tasksByStatus[source.droppableId][source.index].id
      updateTaskStatus(taskId, destination.droppableId)
    }
  }
  
  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <div className="flex overflow-x-auto pb-4 gap-6 h-[calc(100vh-8rem)] -mx-4 px-4">
        {statuses.map(status => (
          <div key={status.value} className="flex-none w-[350px] h-full">
            <div className="bg-background rounded-lg p-4 border border-border h-full flex flex-col">
              {/* Column header */}
              <div className="mb-3 flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="flex items-center space-x-1">
                    <h3 className="font-medium text-sm">{status.label}</h3>
                  </div>
                  <div className="rounded-md bg-muted px-1.5 py-0.5 text-xs text-muted-foreground">
                    {tasksByStatus[status.value]?.length || 0}
                  </div>
                </div>
              </div>
              
              {/* Droppable area for tasks */}
              <Droppable droppableId={status.value}>
                {(provided, snapshot) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    className={cn(
                      "flex-1 flex flex-col gap-2 p-1 overflow-y-auto",
                      snapshot.isDraggingOver && "bg-primary/5 rounded-md"
                    )}
                    style={{
                      // Custom scrollbar styling
                      scrollbarWidth: 'thin',
                      scrollbarColor: 'hsl(var(--muted-foreground)) transparent',
                    }}
                  >
                    {tasksByStatus[status.value]?.map((task, index) => (
                      <Draggable key={task.id} draggableId={task.id} index={index}>
                        {(provided, snapshot) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            className={cn(
                              "flex items-start group",
                              snapshot.isDragging && "opacity-80"
                            )}
                            style={{
                              ...provided.draggableProps.style,
                            }}
                          >
                            <TaskCard task={task} />
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </div>
          </div>
        ))}
      </div>
    </DragDropContext>
  )
}