import { IconDownload, IconPlus } from '@tabler/icons-react'
import { PrimaryButtonsGroup } from '@/components/ui/primary-buttons-group'
import { useTasks } from '../context/tasks-context'

export function TasksPrimaryButtons() {
  const { setOpen } = useTasks()

  const buttons = [
    {
      label: 'Import',
      icon: IconDownload,
      onClick: () => setOpen('import'),
      variant: 'outline' as const,
    },
    {
      label: 'Create',
      icon: IconPlus,
      onClick: () => setOpen('create'),
    },
  ]

  return <PrimaryButtonsGroup buttons={buttons} />
}
