// React import not needed with new JSX transform
import { useNavigate } from '@tanstack/react-router'
import { IconArrowLeft, IconEdit, IconTrash, IconCopy } from '@tabler/icons-react'
import { Button } from '@/components/ui/button'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { Search } from '@/components/search'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { ThemeSwitch } from '@/components/theme-switch'
import { StatusBadge } from '@/components/ui/status-badge'
import { Badge } from '@/components/ui/badge'
import { useTasks } from '../context/tasks-context'
import { statuses, priorities } from '../data/data'
import type { Task } from '../data/schema'
import { TasksDialogs } from './tasks-dialogs'
import { Route as TasksRoute } from '@/routes/_authenticated/tasks'

interface TaskDetailPageProps {
  task: Task
}

export function TaskDetailPage({ task }: TaskDetailPageProps) {
  const navigate = useNavigate()
  const { setOpen, setCurrentRow } = useTasks()

  const handleBack = () => navigate({ to: TasksRoute.to })
  const handleDuplicate = () => {
    setCurrentRow(task)
    setOpen('create')
  }
  const handleEdit = () => {
    setCurrentRow(task)
    setOpen('update')
  }
  const handleDelete = () => {
    setCurrentRow(task)
    setOpen('delete')
  }

  const statusItem = statuses.find((s) => s.value === task.status)
  const statusVariant =
    statusItem?.value === 'done'
      ? 'done'
      : statusItem?.value === 'in progress'
      ? 'inProgress'
      : 'default'

  const priorityItem = priorities.find((p) => p.value === task.priority)

  return (
    <>
      <Header fixed>
        <Search />
        <div className="ml-auto flex items-center space-x-4">
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className="mb-4 flex items-center justify-between">
          <Button variant="ghost" onClick={handleBack}>
            <IconArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={handleDuplicate}>
              <IconCopy className="mr-2 h-4 w-4" />
              Duplicate
            </Button>
            <Button variant="outline" onClick={handleEdit}>
              <IconEdit className="mr-2 h-4 w-4" />
              Edit
            </Button>
            <Button variant="destructive" onClick={handleDelete}>
              <IconTrash className="mr-2 h-4 w-4" />
              Delete
            </Button>
          </div>
        </div>

        <div className="flex flex-col space-y-4">
          <div className="flex items-center gap-2">
            <span className="font-semibold">ID:</span> {task.id}
          </div>
          <div className="flex items-center gap-2">
            <span className="font-semibold">Title:</span> {task.title}
          </div>
          <div className="flex items-center gap-2">
            <span className="font-semibold">Status:</span>
            <StatusBadge status={statusItem?.label || task.status} variant={statusVariant} />
          </div>
          <div className="flex items-center gap-2">
            <span className="font-semibold">Label:</span>
            <Badge variant="outline">{task.label}</Badge>
          </div>
          <div className="flex items-center gap-2">
            <span className="font-semibold">Priority:</span>
            {priorityItem ? (
              <div className="flex items-center gap-2">
                {priorityItem.icon && <priorityItem.icon className="h-4 w-4" />}
                <span>{priorityItem.label}</span>
              </div>
            ) : (
              task.priority
            )}
          </div>
        </div>
      </Main>

      <TasksDialogs />
    </>
  )
}

export default TaskDetailPage
