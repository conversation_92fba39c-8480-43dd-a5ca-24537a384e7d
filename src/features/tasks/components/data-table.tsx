import * as React from 'react'
import { ColumnDef } from '@tanstack/react-table'
import { DataTable as SharedDataTable } from '@/components/ui/data-table'
import { useTasks } from '../context/tasks-context'
import { priorities, statuses } from '../data/data'

interface TasksDataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
}

export function DataTable<TData, TValue>({
  columns,
}: TasksDataTableProps<TData, TValue>) {
  const { tasks: data } = useTasks()

  const filters = [
    {
      column: 'status',
      title: 'Trạng thái',
      options: statuses.map((status) => ({
        label: status.label,
        value: status.value,
        icon: status.icon,
      })),
    },
    {
      column: 'priority',
      title: 'Ưu tiên',
      options: priorities.map((priority) => ({
        label: priority.label,
        value: priority.value,
        icon: priority.icon,
      })),
    },
  ]

  return (
    <SharedDataTable
      columns={columns}
      data={data as TData[]}
      searchKey="title"
      searchPlaceholder="Tìm kiếm task..."
      filters={filters}
    />
  )
}
