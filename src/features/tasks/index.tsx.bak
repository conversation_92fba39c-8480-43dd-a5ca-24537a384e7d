import { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { columns } from './components/columns'
import { DataTable } from './components/data-table'
import { KanbanBoard } from './components/kanban/kanban-board'
import { TasksDialogs } from './components/tasks-dialogs'
import { TasksPrimaryButtons } from './components/tasks-primary-buttons'
import { ViewTabs, ViewMode } from './components/tabs'
import TasksProvider, { useTasks } from './context/tasks-context'

function TasksContent() {
  const { tasks, moveTask } = useTasks()
  const [activeView, setActiveView] = useState<ViewMode>('list')

  const handleViewChange = (newView: ViewMode) => {
    setActiveView(newView)
  }

  return (
    <>
      <Header fixed>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='mb-4 flex flex-wrap items-center justify-between space-y-2 gap-x-4'>
          <div>
            <h2 className='text-2xl font-bold tracking-tight'>Tasks</h2>
            <p className='text-muted-foreground'>
              Here&apos;s a list of your tasks for this month!
            </p>
          </div>
          <div className="flex items-center gap-4">
            <ViewTabs activeView={activeView} onViewChange={handleViewChange} />
            <TasksPrimaryButtons />
          </div>
        </div>
        
        {activeView === 'list' ? (
          <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12'>
            <DataTable data={tasks} columns={columns} />
          </div>
        ) : (
          <div className='flex-1 overflow-hidden'>
            <KanbanBoard data={tasks} onTaskMove={moveTask} />
          </div>
        )}
      </Main>

      <TasksDialogs />
    </>
  )
}

export default function Tasks() {
  return (
    <TasksProvider>
      <TasksContent />
    </TasksProvider>
  )
}
