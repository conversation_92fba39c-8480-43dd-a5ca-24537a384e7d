import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { columns } from './components/columns'
import { DataTable } from './components/data-table'
import { TasksDialogs } from './components/tasks-dialogs'
import { TasksPrimaryButtons } from './components/tasks-primary-buttons'
import TasksProvider, { useTasks } from './context/tasks-context'
import { tasks } from './data/tasks'
import { ViewModeToggle } from './components/view-mode-toggle'
import { KanbanBoard } from './components/kanban-board'
import { cn } from '@/lib/utils'

// Tạo component TasksContent để có thể sử dụng useTasks hook
function TasksContent() {
  const { viewMode } = useTasks()

  return (
    <>
      <Header fixed>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='mb-4 flex items-center justify-between gap-2 overflow-x-auto'>
          <div>
            <h2 className='text-2xl font-bold tracking-tight'>Tasks</h2>
            <p className='text-muted-foreground'>
              Here&apos;s a list of your tasks for this month!
            </p>
          </div>
          
          <div className='flex items-center gap-2 flex-shrink-0'>
            <TasksPrimaryButtons />
          </div>
        </div>
        
        <div className='mb-6 flex items-center justify-between gap-2 overflow-x-auto'>
          <ViewModeToggle />
          <div className='flex items-center gap-2 flex-shrink-0'>
            {viewMode === 'list' && (
              <div className="rounded-md bg-muted px-3 py-1.5 text-sm text-muted-foreground">
                {tasks.length} tasks
              </div>
            )}
          </div>
        </div>
        
        <div className={cn(
          viewMode === 'kanban' 
            ? 'flex-1 h-[calc(100vh-12rem)] overflow-auto'
            : 'flex-1 overflow-auto'
        )}>
          {viewMode === 'list' ? (
            <DataTable columns={columns} />
          ) : (
            <div className="pr-4 pb-4">
              <KanbanBoard />
            </div>
          )}
        </div>
      </Main>

      <TasksDialogs />
    </>
  )
}

export default function Tasks() {
  return (
    <TasksProvider initialTasks={tasks}>
      <TasksContent />
    </TasksProvider>
  )
}

