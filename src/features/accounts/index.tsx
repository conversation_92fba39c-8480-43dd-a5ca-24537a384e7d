import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { AccountsDialogs } from './components/accounts-dialogs'
import { AccountsPrimaryButtons } from './components/accounts-primary-buttons'
import { AccountsView } from './components/accounts-view'
import AccountsProvider from './context/accounts-context'
import { useAccountsContext } from './context/use-accounts-context'
import { accountListSchema } from './data/schema'
import { accounts } from './data/accounts'

// Tạo component AccountsContent để có thể sử dụng useAccountsContext hook
function AccountsContent() {
  const { accounts } = useAccountsContext()

  return (
    <>
      <Header fixed>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='mb-2 flex flex-wrap items-center justify-between space-y-2'>
          <div>
            <h2 className='text-2xl font-bold tracking-tight'>Khách hàng</h2>
            <p className='text-muted-foreground'>
              Quản lý và theo dõi khách hàng của bạn.
            </p>
          </div>
          <div className="flex items-center gap-2">
            <AccountsPrimaryButtons />
          </div>
        </div>

        <div className='mb-4 flex items-center justify-between gap-2 overflow-x-auto'>
          <div className='flex items-center gap-2 flex-shrink-0'>
            <div className="rounded-md bg-muted px-3 py-1.5 text-sm text-muted-foreground">
              {accounts.length} khách hàng
            </div>
          </div>
        </div>

        <AccountsView data={accounts} />
      </Main>

      <AccountsDialogs />
    </>
  )
}

export default function Accounts() {
  // Parse account list
  const accountList = accountListSchema.parse(accounts)

  return (
    <AccountsProvider initialAccounts={accountList}>
      <AccountsContent />
    </AccountsProvider>
  )
}
