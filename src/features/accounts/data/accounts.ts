import { Account, AccountSize, AccountType, AccountStatus } from './schema'

export const accounts: Account[] = [
  {
    id: '1',
    name: '<PERSON><PERSON><PERSON><PERSON>ăn A',
    status: AccountStatus.ACTIVE,
    email: 'nguy<PERSON><PERSON>@gmail.com',
    phone: '**********',
    mobileNo: '**********',
    company: 'Công ty TNHH ABC',
    position: 'Giám đốc IT',
    address: 'Quận 1, TP.HCM',
    website: 'https://abc.com',
    industry: 'Công nghệ',
    size: AccountSize.MEDIUM,
    type: AccountType.BUSINESS,
    
    // Lead conversion tracking
    originalLeadId: 'lead_001',
    convertedFromDealId: 'deal_001',
    conversionDate: '2023-11-15T00:00:00Z',
    source: 'website',
    leadOwner: '<PERSON>',
    customerSince: '2023-11-15T00:00:00Z',
    
    // Customer value & metrics
    lifetimeValue: *********,
    acquisitionCost: 5000000,
    healthScore: 85,
    churnRisk: 15,
    satisfaction: 4,
    
    preferences: ['Công nghệ', 'B2B'],
    leadIds: ['lead_001'],
    dealIds: ['deal_001', 'deal_005'],
    orderIds: ['order_001', 'order_002'],
    tags: ['VIP', 'Tech Leader'],
    notes: 'Khách hàng quan trọng, đã mua gói Enterprise từ 2023',
    
    createdAt: '2023-11-15T00:00:00Z',
    updatedAt: '2024-12-20T00:00:00Z',
    lastOrderAt: '2024-12-20T00:00:00Z',
    lastContactAt: '2024-12-18T00:00:00Z',
    totalOrders: 8,
    totalRevenue: *********,
    
    avatar: 'https://example.com/avatars/01.png',
    assignedTo: 'John Doe',
    accountOwner: 'John Doe',
    accountManager: 'Sarah Wilson',
    omnichannelId: 'omni_001'
  },
  {
    id: '2',
    name: 'Trần Thị B',
    status: AccountStatus.VIP,
    email: '<EMAIL>',
    phone: '**********',
    mobileNo: '**********',
    company: 'Startup XYZ',
    position: 'CEO',
    address: 'Quận 7, TP.HCM',
    website: 'https://startupxyz.com',
    industry: 'E-commerce',
    size: AccountSize.SMALL,
    type: AccountType.BUSINESS,
    
    // Lead conversion tracking
    originalLeadId: 'lead_002',
    convertedFromDealId: 'deal_002',
    conversionDate: '2023-08-10T00:00:00Z',
    source: 'referral',
    leadOwner: 'Jane Smith',
    customerSince: '2023-08-10T00:00:00Z',
    
    // Customer value & metrics  
    lifetimeValue: *********,
    acquisitionCost: 3000000,
    healthScore: 92,
    churnRisk: 8,
    satisfaction: 5,
    
    preferences: ['E-commerce', 'Startup'],
    leadIds: ['lead_002'],
    dealIds: ['deal_002', 'deal_006', 'deal_007'],
    orderIds: ['order_003', 'order_004', 'order_005'],
    tags: ['VIP', 'High Growth'],
    notes: 'Startup tăng trưởng nhanh, rất hài lòng với dịch vụ',
    
    createdAt: '2023-08-10T00:00:00Z',
    updatedAt: '2024-12-15T00:00:00Z',
    lastOrderAt: '2024-12-15T00:00:00Z',
    lastContactAt: '2024-12-14T00:00:00Z',
    totalOrders: 12,
    totalRevenue: *********,
    
    avatar: 'https://example.com/avatars/02.png',
    assignedTo: 'Jane Smith',
    accountOwner: 'Jane Smith',
    accountManager: 'Michael Brown'
  },
  {
    id: '3',
    name: 'Lê Minh C',
    status: AccountStatus.NEW_CUSTOMER,
    email: '<EMAIL>',
    phone: '**********',
    mobileNo: '**********',
    position: 'Freelancer',
    address: 'Quận 3, TP.HCM',
    industry: 'Marketing',
    type: AccountType.INDIVIDUAL,
    
    // Lead conversion tracking
    originalLeadId: 'lead_003',
    convertedFromDealId: 'deal_003',
    conversionDate: '2024-12-01T00:00:00Z',
    source: 'facebook',
    leadOwner: 'Sarah Wilson',
    customerSince: '2024-12-01T00:00:00Z',
    
    // Customer value & metrics
    lifetimeValue: ********,
    acquisitionCost: 1000000,
    healthScore: 75,
    churnRisk: 25,
    satisfaction: 4,
    
    preferences: ['Marketing', 'Freelance'],
    leadIds: ['lead_003'],
    dealIds: ['deal_003'],
    orderIds: ['order_006'],
    tags: ['New Customer'],
    notes: 'Khách hàng mới, cần follow up thường xuyên',
    
    createdAt: '2024-12-01T00:00:00Z',
    updatedAt: '2024-12-20T00:00:00Z',
    lastOrderAt: '2024-12-01T00:00:00Z',
    lastContactAt: '2024-12-19T00:00:00Z',
    totalOrders: 1,
    totalRevenue: ********,
    
    avatar: 'https://example.com/avatars/03.png',
    assignedTo: 'Sarah Wilson',
    accountOwner: 'Sarah Wilson',
    accountManager: 'Alex Johnson'
  },
  {
    id: '4',
    name: 'Phạm Văn D',
    status: AccountStatus.AT_RISK,
    email: '<EMAIL>',
    phone: '**********',
    mobileNo: '**********',
    company: 'Traditional Corp',
    position: 'IT Manager',
    address: 'Quận Tân Bình, TP.HCM',
    website: 'https://traditionalcorp.com',
    industry: 'Manufacturing',
    size: AccountSize.LARGE,
    type: AccountType.BUSINESS,
    
    // Lead conversion tracking
    originalLeadId: 'lead_004',
    convertedFromDealId: 'deal_004',
    conversionDate: '2022-03-15T00:00:00Z',
    source: 'cold_call',
    leadOwner: 'Michael Brown',
    customerSince: '2022-03-15T00:00:00Z',
    
    // Customer value & metrics
    lifetimeValue: *********,
    acquisitionCost: 8000000,
    healthScore: 35,
    churnRisk: 65,
    satisfaction: 2,
    
    preferences: ['Manufacturing', 'Traditional'],
    leadIds: ['lead_004'],
    dealIds: ['deal_004'],
    orderIds: ['order_007', 'order_008'],
    tags: ['At Risk', 'Needs Attention'],
    notes: 'Khách hàng không hài lòng với support, cần intervention ngay',
    
    createdAt: '2022-03-15T00:00:00Z',
    updatedAt: '2024-11-30T00:00:00Z',
    lastOrderAt: '2023-08-10T00:00:00Z',
    lastContactAt: '2024-11-28T00:00:00Z',
    totalOrders: 3,
    totalRevenue: *********,
    
    avatar: 'https://example.com/avatars/04.png',
    assignedTo: 'Michael Brown',
    accountOwner: 'Michael Brown',
    accountManager: 'David Lee'
  },
  {
    id: '5',
    name: 'Nguyễn Thị E',
    status: AccountStatus.INACTIVE,
    email: '<EMAIL>',
    phone: '**********',
    mobileNo: '**********',
    company: 'Dormant LLC',
    position: 'Operations Manager',
    address: 'Quận 4, TP.HCM',
    industry: 'Retail',
    size: AccountSize.SMALL,
    type: AccountType.BUSINESS,
    
    // Lead conversion tracking
    originalLeadId: 'lead_005',
    convertedFromDealId: 'deal_008',
    conversionDate: '2021-12-01T00:00:00Z',
    source: 'trade_show',
    leadOwner: 'Alex Johnson',
    customerSince: '2021-12-01T00:00:00Z',
    
    // Customer value & metrics
    lifetimeValue: ********,
    acquisitionCost: 2500000,
    healthScore: 20,
    churnRisk: 80,
    satisfaction: 3,
    
    preferences: ['Retail'],
    leadIds: ['lead_005'],
    dealIds: ['deal_008'],
    orderIds: ['order_009'],
    tags: ['Inactive', 'Low Engagement'],
    notes: 'Không có hoạt động trong 6 tháng qua, cần win-back campaign',
    
    createdAt: '2021-12-01T00:00:00Z',
    updatedAt: '2024-05-15T00:00:00Z',
    lastOrderAt: '2022-06-15T00:00:00Z',
    lastContactAt: '2024-05-10T00:00:00Z',
    totalOrders: 2,
    totalRevenue: ********,
    
    avatar: 'https://example.com/avatars/05.png',
    assignedTo: 'Alex Johnson',
    accountOwner: 'Alex Johnson',
    accountManager: 'Lisa Chen'
  }
]