import { AccountStatus, AccountType, AccountSize } from './schema'

// C<PERSON>u hình hiển thị cho các trạng thái tài khoản
export const accountStatusConfig = {
  [AccountStatus.NEW_CUSTOMER]: {
    label: 'Kh<PERSON>ch hàng mới',
    color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    icon: 'user-plus'
  },
  [AccountStatus.ACTIVE]: {
    label: 'Đang hoạt động',
    color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    icon: 'circle-check'
  },
  [AccountStatus.VIP]: {
    label: 'VIP',
    color: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
    icon: 'crown'
  },
  [AccountStatus.AT_RISK]: {
    label: '<PERSON><PERSON> nguy cơ',
    color: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300',
    icon: 'alert-triangle'
  },
  [AccountStatus.INACTIVE]: {
    label: 'Không hoạt động',
    color: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300',
    icon: 'circle-x'
  },
  [AccountStatus.CHURNED]: {
    label: 'Đã churn',
    color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
    icon: 'x-circle'
  }
}

// Cấu hình hiển thị cho các loại tài khoản
export const accountTypeConfig = {
  [AccountType.INDIVIDUAL]: {
    label: 'Cá nhân',
    color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
  },
  [AccountType.BUSINESS]: {
    label: 'Doanh nghiệp',
    color: 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300'
  }
}

// Cấu hình hiển thị cho các kích thước doanh nghiệp
export const accountSizeConfig = {
  [AccountSize.SMALL]: {
    label: 'Nhỏ',
    color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
  },
  [AccountSize.MEDIUM]: {
    label: 'Vừa',
    color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
  },
  [AccountSize.LARGE]: {
    label: 'Lớn',
    color: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
  }
}

// Thứ tự hiển thị các trạng thái
export const statusOrder = [
  AccountStatus.NEW_CUSTOMER,
  AccountStatus.VIP,
  AccountStatus.ACTIVE,
  AccountStatus.AT_RISK,
  AccountStatus.INACTIVE,
  AccountStatus.CHURNED
]

// Hàm định dạng tiền tệ
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
    maximumFractionDigits: 0
  }).format(amount)
}

// Hàm lấy chữ cái đầu của tên
export function getInitials(name: string): string {
  return name
    .split(' ')
    .map((n) => n[0])
    .join('')
    .toUpperCase()
    .substring(0, 2)
}

// Danh sách các ngành nghề phổ biến
export const commonIndustries = [
  'Công nghệ',
  'Bán lẻ',
  'Sản xuất',
  'Tài chính',
  'Y tế',
  'Giáo dục',
  'Dịch vụ',
  'Xây dựng',
  'Thực phẩm',
  'Du lịch'
]
