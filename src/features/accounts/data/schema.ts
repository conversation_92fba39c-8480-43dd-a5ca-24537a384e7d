import { z } from 'zod'

// Đ<PERSON>nh nghĩa các enum cho Account (Customer)
export const AccountStatus = {
  NEW_CUSTOMER: 'new_customer',
  ACTIVE: 'active', 
  VIP: 'vip',
  AT_RISK: 'at_risk',
  INACTIVE: 'inactive',
  CHURNED: 'churned'
} as const

export const AccountType = {
  INDIVIDUAL: 'individual',
  BUSINESS: 'business'
} as const

export const AccountSize = {
  SMALL: 'small',
  MEDIUM: 'medium', 
  LARGE: 'large'
} as const

// Tạo type cho các giá trị từ constants
export type AccountSizeValue = typeof AccountSize[keyof typeof AccountSize];
export type AccountTypeValue = typeof AccountType[keyof typeof AccountType];
export type AccountStatusValue = typeof AccountStatus[keyof typeof AccountStatus];

export const accountSchema = z.object({
  id: z.string(),
  name: z.string(),
  status: z.nativeEnum(AccountStatus).default(AccountStatus.NEW_CUSTOMER),
  email: z.string().email().optional(),
  phone: z.string().optional(), // Đổi từ mobileNo sang phone
  mobileNo: z.string().optional(), // Giữ lại để tương thích với Lead
  company: z.string().optional(),
  position: z.string().optional(), // Thêm lại position từ Lead
  address: z.string().optional(),
  website: z.string().url().optional(),
  birthday: z.string().optional(),
  
  // Customer-specific info
  type: z.nativeEnum(AccountType),
  size: z.nativeEnum(AccountSize).optional(),
  industry: z.string().optional(),
  
  // Lead/Deal conversion tracking
  originalLeadId: z.string().optional(), // Lead gốc
  convertedFromDealId: z.string().optional(), // Deal đã convert
  conversionDate: z.string().datetime().optional(), // Ngày convert thành customer
  source: z.string().optional(), // Nguồn gốc từ lead
  leadOwner: z.string().optional(), // Sales rep đã convert lead
  
  // Customer journey & value
  customerSince: z.string().datetime().optional(), // Ngày trở thành customer
  lifetimeValue: z.number().min(0).default(0), // Tổng giá trị customer
  acquisitionCost: z.number().min(0).optional(), // Chi phí để có được customer này
  
  // Relationships
  leadIds: z.array(z.string()).default([]), // Các lead liên quan (nếu có nhiều)
  dealIds: z.array(z.string()).default([]), // Tất cả deals
  orderIds: z.array(z.string()).default([]), // Tất cả orders
  
  // Thông tin phân loại
  tags: z.array(z.string()).default([]),
  notes: z.string().optional(),
  preferences: z.array(z.string()).default([]),
  
  // Metadata
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
  lastOrderAt: z.string().datetime().optional(),
  lastContactAt: z.string().datetime().optional(), // Lần liên hệ cuối
  totalOrders: z.number().min(0).default(0),
  totalRevenue: z.number().min(0).default(0),
  
  // Avatar & Assignment
  avatar: z.string().url().optional(),
  assignedTo: z.string().optional(),
  accountOwner: z.string().optional(), // Account manager
  accountManager: z.string().optional(), // Người quản lý customer
  
  // Customer success metrics
  healthScore: z.number().min(0).max(100).optional(), // Customer health score
  churnRisk: z.number().min(0).max(100).optional(), // Churn risk percentage
  satisfaction: z.number().min(1).max(5).optional(), // Customer satisfaction (1-5)
  
  // Omnichannel
  omnichannelId: z.string().optional() // ID để liên kết với chat systems
})

export const accountListSchema = z.array(accountSchema)

export type Account = z.infer<typeof accountSchema>

// Các loại dữ liệu bổ sung cho Customer
export type AccountActivity = {
  id: string
  accountId: string
  type: 'note' | 'email' | 'call' | 'meeting' | 'order' | 'support' | 'system'
  content: string
  timestamp: string
  user: string
  metadata?: Record<string, unknown>
}

export type AccountNote = {
  id: string
  accountId: string
  content: string
  createdBy: string
  createdAt: string
  updatedAt?: string
}

export type AccountTask = {
  id: string
  accountId: string
  title: string
  description?: string
  dueDate: string
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled'
  priority: 'low' | 'medium' | 'high'
  assignee?: string
  createdAt: string
  updatedAt?: string
}

export type AccountOrder = {
  id: string
  accountId: string
  amount: number
  status: 'new' | 'processing' | 'completed' | 'cancelled'
  createdAt: string
  products: Array<{
    id: string
    name: string
    quantity: number
    price: number
  }>
}
