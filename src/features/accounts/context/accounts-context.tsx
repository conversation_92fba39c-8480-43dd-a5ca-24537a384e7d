import {
  useState,
  type ReactNode,
} from 'react'
import { AccountsContext } from './accounts-context-type'
import { Account, AccountStatusValue } from '../data/schema'

interface AccountsProviderProps {
  children: ReactNode
  initialAccounts: Account[]
}

export default function AccountsProvider({
  children,
  initialAccounts,
}: AccountsProviderProps) {
  const [openDialog, setOpenDialog] = useState('')
  const [selectedAccountIds, setSelectedAccountIds] = useState<string[]>([])
  const [showFilterPanel, setShowFilterPanel] = useState(false)
  const [accounts, setAccounts] = useState<Account[]>(initialAccounts)

  const updateAccountStatus = (accountId: string, newStatus: AccountStatusValue) => {
    setAccounts((prevAccounts) =>
      prevAccounts.map((account) =>
        account.id === accountId ? { ...account, status: newStatus } : account
      )
    )
  }

  const updateAccountTags = (accountId: string, tags: string[]) => {
    setAccounts((prevAccounts) =>
      prevAccounts.map((account) =>
        account.id === accountId ? { ...account, tags } : account
      )
    )
  }

  return (
    <AccountsContext.Provider
      value={{
        openDialog,
        setOpenDialog,
        selectedAccountIds,
        setSelectedAccountIds,
        showFilterPanel,
        setShowFilterPanel,
        accounts,
        setAccounts,
        updateAccountStatus,
        updateAccountTags,
      }}
    >
      {children}
    </AccountsContext.Provider>
  )
}
