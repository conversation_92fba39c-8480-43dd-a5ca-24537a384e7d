import { createContext } from 'react'
import { Account, AccountStatusValue } from '../data/schema'

export type AccountsContextType = {
  openDialog: string
  setOpenDialog: (dialog: string) => void
  selectedAccountIds: string[]
  setSelectedAccountIds: (ids: string[]) => void
  showFilterPanel: boolean
  setShowFilterPanel: (show: boolean) => void
  accounts: Account[]
  setAccounts: (accounts: Account[]) => void
  updateAccountStatus: (accountId: string, newStatus: AccountStatusValue) => void
  updateAccountTags: (accountId: string, tags: string[]) => void
}

export const AccountsContext = createContext<AccountsContextType | null>(null)
