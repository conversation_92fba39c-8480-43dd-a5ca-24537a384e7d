import React from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { AccountStatus, Account } from '../data/schema'
import { CheckCircle, Circle, Clock, Users, Star, Building, Activity, DollarSign } from 'lucide-react'
import { useAccountsContext } from '../context/use-accounts-context'

interface AccountSalesPathProps {
  account: Account
  onStatusChange?: (status: string) => void
}

export function AccountSalesPath({ account, onStatusChange }: AccountSalesPathProps) {
  const { updateAccountStatus } = useAccountsContext()

  // Các trạng thái trong customer lifecycle
  const statuses = [
    {
      value: AccountStatus.NEW_CUSTOMER,
      label: 'Khách hàng mới',
      icon: Users,
      color: 'bg-blue-100 text-blue-800',
      description: '<PERSON><PERSON><PERSON> chuyển đổi từ lead thành customer'
    },
    {
      value: AccountStatus.ACTIVE,
      label: 'Đang hoạt động',
      icon: CheckCircle,
      color: 'bg-green-100 text-green-800',
      description: 'Customer đang sử dụng tích cực'
    },
    {
      value: AccountStatus.VIP,
      label: 'VIP',
      icon: Star,
      color: 'bg-amber-100 text-amber-800',
      description: 'Customer có giá trị cao'
    },
    {
      value: AccountStatus.AT_RISK,
      label: 'Có nguy cơ',
      icon: Activity,
      color: 'bg-orange-100 text-orange-800',
      description: 'Customer có nguy cơ churn'
    },
    {
      value: AccountStatus.INACTIVE,
      label: 'Không hoạt động',
      icon: Clock,
      color: 'bg-gray-100 text-gray-800',
      description: 'Customer không còn hoạt động'
    },
    {
      value: AccountStatus.CHURNED,
      label: 'Đã churn',
      icon: Building,
      color: 'bg-red-100 text-red-800',
      description: 'Customer đã ngừng sử dụng dịch vụ'
    }
  ]

  // Xử lý khi click vào trạng thái
  const handleStatusClick = (status: string) => {
    if (onStatusChange) {
      onStatusChange(status)
    } else if (updateAccountStatus) {
      updateAccountStatus(account.id, status as any)
    }
  }

  return (
    <div className="space-y-6">
      {/* Account Path - Hiển thị dạng đường tiến trình */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col">
            <div className="text-sm font-medium mb-3">Customer Journey</div>
            <div className="flex items-center overflow-x-auto">
              {statuses.slice(0, 3).map((status, index) => (
                <React.Fragment key={status.value}>
                  {/* Trạng thái */}
                  <div className="flex flex-col items-center min-w-fit">
                    <Button
                      variant="ghost"
                      size="sm"
                      className={cn(
                        "h-auto py-1 px-2 rounded-full flex items-center gap-1.5 whitespace-nowrap",
                        account.status === status.value && "bg-primary/10 text-primary",
                        "hover:bg-primary/5"
                      )}
                      onClick={() => handleStatusClick(status.value)}
                    >
                      {account.status === status.value ? (
                        <CheckCircle className="h-4 w-4" />
                      ) : (
                        <Circle className="h-4 w-4" />
                      )}
                      <span>{status.label}</span>
                    </Button>
                  </div>

                  {/* Đường nối giữa các trạng thái */}
                  {index < 2 && (
                    <div className="flex-1 h-0.5 mx-1 bg-border min-w-[20px]" />
                  )}
                </React.Fragment>
              ))}
            </div>
            
            {/* Trạng thái đặc biệt - hiển thị riêng */}
            {(account.status === AccountStatus.AT_RISK || account.status === AccountStatus.INACTIVE || account.status === AccountStatus.CHURNED) && (
              <div className="mt-3 pt-3 border-t">
                <div className="text-xs text-muted-foreground mb-2">Trạng thái đặc biệt:</div>
                <div className="flex justify-center">
                  <Button
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "h-auto py-1 px-2 rounded-full flex items-center gap-1.5",
                      "bg-primary/10 text-primary"
                    )}
                  >
                    <CheckCircle className="h-4 w-4" />
                    <span>
                      {account.status === AccountStatus.AT_RISK ? 'Có nguy cơ' :
                       account.status === AccountStatus.INACTIVE ? 'Không hoạt động' :
                       'Đã churn'}
                    </span>
                  </Button>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Timeline - Customer Journey */}
      <div className="space-y-2">
        <div className="text-sm font-medium">Customer Timeline</div>
        <div className="border rounded-md p-4 space-y-4">
          {/* Lead to Customer Conversion */}
          <div className="relative pl-6 pb-6 border-l border-border">
            <div className="absolute left-0 top-0 -translate-x-1/2 w-4 h-4 rounded-full bg-blue-500 flex items-center justify-center">
              <div className="w-2 h-2 rounded-full bg-white"></div>
            </div>
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <span className="font-medium">Chuyển đổi từ Lead</span>
                <Badge variant="outline" className="text-xs">
                  {account.conversionDate ? new Date(account.conversionDate).toLocaleDateString('vi-VN') : new Date(account.createdAt).toLocaleDateString('vi-VN')}
                </Badge>
              </div>
              <p className="text-sm text-muted-foreground">
                Từ Lead {account.originalLeadId || 'N/A'} → Deal {account.convertedFromDealId || 'N/A'} → Customer
                {account.leadOwner && ` (Sales: ${account.leadOwner})`}
              </p>
              {account.source && (
                <p className="text-xs text-muted-foreground">Nguồn gốc: {account.source}</p>
              )}
            </div>
          </div>

          {/* Customer Since */}
          <div className="relative pl-6 pb-6 border-l border-border">
            <div className="absolute left-0 top-0 -translate-x-1/2 w-4 h-4 rounded-full bg-green-500 flex items-center justify-center">
              <div className="w-2 h-2 rounded-full bg-white"></div>
            </div>
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <span className="font-medium">Trở thành Customer</span>
                <Badge variant="outline" className="text-xs">
                  {account.customerSince ? new Date(account.customerSince).toLocaleDateString('vi-VN') : 'N/A'}
                </Badge>
              </div>
              <p className="text-sm text-muted-foreground">
                Customer được quản lý bởi {account.accountManager || account.accountOwner || 'N/A'}
              </p>
            </div>
          </div>

          {/* Latest Order */}
          {account.lastOrderAt && (
            <div className="relative pl-6 pb-6 border-l border-border">
              <div className="absolute left-0 top-0 -translate-x-1/2 w-4 h-4 rounded-full bg-purple-500 flex items-center justify-center">
                <div className="w-2 h-2 rounded-full bg-white"></div>
              </div>
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <span className="font-medium">Đơn hàng gần nhất</span>
                  <Badge variant="outline" className="text-xs">
                    {new Date(account.lastOrderAt).toLocaleDateString('vi-VN')}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  Tổng {account.totalOrders} đơn hàng • Lifetime Value: {new Intl.NumberFormat('vi-VN', { 
                    style: 'currency', 
                    currency: 'VND' 
                  }).format(account.lifetimeValue || account.totalRevenue || 0)}
                </p>
              </div>
            </div>
          )}

          {/* Current Status */}
          <div className="relative pl-6">
            <div className={cn(
              "absolute left-0 top-0 -translate-x-1/2 w-4 h-4 rounded-full flex items-center justify-center",
              account.status === AccountStatus.ACTIVE ? "bg-green-500" :
              account.status === AccountStatus.VIP ? "bg-amber-500" :
              account.status === AccountStatus.AT_RISK ? "bg-orange-500" :
              account.status === AccountStatus.INACTIVE ? "bg-gray-500" :
              account.status === AccountStatus.CHURNED ? "bg-red-500" : "bg-blue-500"
            )}>
              <div className="w-2 h-2 rounded-full bg-white"></div>
            </div>
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <span className="font-medium">Trạng thái hiện tại</span>
                <Badge variant="outline" className="text-xs">
                  {new Date(account.updatedAt).toLocaleDateString('vi-VN')}
                </Badge>
              </div>
              <p className="text-sm text-muted-foreground">
                {account.status === AccountStatus.NEW_CUSTOMER ? 'Khách hàng mới, cần onboarding và chăm sóc' :
                 account.status === AccountStatus.ACTIVE ? 'Customer đang hoạt động tích cực' :
                 account.status === AccountStatus.VIP ? 'Customer VIP với giá trị cao' :
                 account.status === AccountStatus.AT_RISK ? 'Customer có nguy cơ churn, cần intervention' :
                 account.status === AccountStatus.INACTIVE ? 'Customer không hoạt động, cần win-back' :
                 account.status === AccountStatus.CHURNED ? 'Customer đã churn' : account.status}
              </p>
              {account.healthScore && (
                <p className="text-xs text-muted-foreground">
                  Health Score: {account.healthScore}/100 • Churn Risk: {account.churnRisk || 0}%
                </p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Customer Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="p-3 border rounded-lg text-center">
          <div className="flex items-center justify-center mb-1">
            <DollarSign className="h-4 w-4 text-green-600" />
          </div>
          <div className="text-lg font-semibold">
            {new Intl.NumberFormat('vi-VN', { 
              style: 'currency', 
              currency: 'VND',
              maximumFractionDigits: 0
            }).format(account.lifetimeValue || account.totalRevenue || 0)}
          </div>
          <div className="text-xs text-muted-foreground">Lifetime Value</div>
        </div>
        <div className="p-3 border rounded-lg text-center">
          <div className="flex items-center justify-center mb-1">
            <Building className="h-4 w-4 text-muted-foreground" />
          </div>
          <div className="text-lg font-semibold">{account.totalOrders}</div>
          <div className="text-xs text-muted-foreground">Đơn hàng</div>
        </div>
        <div className="p-3 border rounded-lg text-center">
          <div className="flex items-center justify-center mb-1">
            <Activity className={cn(
              "h-4 w-4",
              (account.healthScore || 0) >= 80 ? "text-green-600" :
              (account.healthScore || 0) >= 60 ? "text-yellow-600" : "text-red-600"
            )} />
          </div>
          <div className="text-lg font-semibold">{account.healthScore || 'N/A'}</div>
          <div className="text-xs text-muted-foreground">Health Score</div>
        </div>
        <div className="p-3 border rounded-lg text-center">
          <div className="flex items-center justify-center mb-1">
            <Star className={cn(
              "h-4 w-4",
              (account.satisfaction || 0) >= 4 ? "text-yellow-500" :
              (account.satisfaction || 0) >= 3 ? "text-yellow-400" : "text-gray-400"
            )} />
          </div>
          <div className="text-lg font-semibold">{account.satisfaction ? `${account.satisfaction}/5` : 'N/A'}</div>
          <div className="text-xs text-muted-foreground">Satisfaction</div>
        </div>
      </div>
    </div>
  )
}