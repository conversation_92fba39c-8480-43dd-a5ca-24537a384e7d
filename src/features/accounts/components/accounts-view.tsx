import { useAccountsContext } from '../context/use-accounts-context'
import { AccountsTable } from './accounts-table'
import { FilterPanel } from './filter-panel'
import { useState, useEffect } from 'react'
import { Account } from '../data/schema'

interface AccountsViewProps {
  data: Account[]
}

export function AccountsView({ data }: AccountsViewProps) {
  const { showFilterPanel } = useAccountsContext()
  const [filteredData, setFilteredData] = useState<Account[]>(data)
  
  useEffect(() => {
    setFilteredData(data);
  }, [data]);
  
  const applyFilters = (filters: {
    status: string[],
    type: string[],
    size: string[],
    industry: string[],
    revenueRange: [number, number],
    assignedTo: string
  }) => {
    let result = [...data]
    
    // Lọc theo trạng thái
    if (filters.status.length > 0) {
      result = result.filter(account => filters.status.includes(account.status))
    }
    
    // Lọc theo loạ<PERSON> tà<PERSON>
    if (filters.type.length > 0) {
      result = result.filter(account => 
        account.type && filters.type.includes(account.type)
      )
    }
    
    // Lọc theo kích thước
    if (filters.size.length > 0) {
      result = result.filter(account => 
        account.size && filters.size.includes(account.size)
      )
    }
    
    // Lọc theo ngành nghề
    if (filters.industry.length > 0) {
      result = result.filter(account => 
        account.industry && filters.industry.includes(account.industry)
      )
    }
    
    // Lọc theo khoảng doanh số
    result = result.filter(account => 
      account.totalRevenue >= filters.revenueRange[0] && 
      account.totalRevenue <= filters.revenueRange[1]
    )
    
    // Lọc theo người phụ trách
    if (filters.assignedTo) {
      result = result.filter(account => 
        account.assignedTo?.toLowerCase().includes(filters.assignedTo.toLowerCase())
      )
    }
    
    setFilteredData(result)
  }
  
  const resetFilters = () => {
    setFilteredData(data)
  }
  
  return (
    <div className="space-y-4">
      {showFilterPanel && (
        <FilterPanel 
          applyFilters={applyFilters}
          resetFilters={resetFilters}
        />
      )}
      
      <AccountsTable data={filteredData} />
    </div>
  )
}
