import { accountStatusConfig, accountTypeConfig, accountSizeConfig, commonIndustries } from '../data/data'
import { AccountStatus, AccountType, AccountSize } from '../data/schema'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { useState } from 'react'
import { Cross2Icon } from '@radix-ui/react-icons'
import { cn } from '@/lib/utils'

// Component cho thanh trượt khoảng giá trị
interface RangeSliderProps {
  min: number;
  max: number;
  step: number;
  value: [number, number];
  onChange: (value: [number, number]) => void;
  className?: string;
}

const RangeSlider = ({ min, max, step, value, onChange, className }: RangeSliderProps) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const newValue = [...value] as [number, number];
    newValue[index] = parseInt(e.target.value);

    // Đảm bảo giá trị min không vượt quá max
    if (index === 0 && newValue[0] > newValue[1]) {
      newValue[0] = newValue[1];
    }

    // Đảm bảo giá trị max không nhỏ hơn min
    if (index === 1 && newValue[1] < newValue[0]) {
      newValue[1] = newValue[0];
    }

    onChange(newValue);
  };

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex space-x-2">
        <input
          type="range"
          min={min}
          max={max}
          step={step}
          value={value[0]}
          onChange={(e) => handleChange(e, 0)}
          className="w-full"
        />
        <input
          type="range"
          min={min}
          max={max}
          step={step}
          value={value[1]}
          onChange={(e) => handleChange(e, 1)}
          className="w-full"
        />
      </div>
    </div>
  );
};

interface FilterOptions {
  status: string[];
  type: string[];
  size: string[];
  industry: string[];
  revenueRange: [number, number];
  assignedTo: string;
}

interface FilterPanelProps {
  applyFilters: (filters: FilterOptions) => void;
  resetFilters: () => void;
}

export function FilterPanel({ applyFilters, resetFilters }: FilterPanelProps) {
  const [status, setStatus] = useState<string[]>([])
  const [type, setType] = useState<string[]>([])
  const [size, setSize] = useState<string[]>([])
  const [industry, setIndustry] = useState<string[]>([])
  const [revenueRange, setRevenueRange] = useState<[number, number]>([0, ********00])
  const [assignedTo, setAssignedTo] = useState<string>('')

  const handleApplyFilters = () => {
    applyFilters({
      status,
      type,
      size,
      industry,
      revenueRange,
      assignedTo
    })
  }

  const handleResetFilters = () => {
    setStatus([])
    setType([])
    setSize([])
    setIndustry([])
    setRevenueRange([0, ********00])
    setAssignedTo('')
    resetFilters()
  }

  return (
    <div className="border rounded-md p-4 space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="font-medium">Bộ lọc nâng cao</h4>
        <Button
          variant="outline"
          size="sm"
          onClick={handleResetFilters}
          className="h-8"
        >
          <Cross2Icon className="mr-2 h-4 w-4" />
          Đặt lại
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* Trạng thái */}
        <div className="space-y-2">
          <Label>Trạng thái</Label>
          <div className="space-y-1">
            {Object.entries(AccountStatus).map(([key, value]) => (
              <div key={value} className="flex items-center space-x-2">
                <Checkbox
                  id={`status-${value}`}
                  checked={status.includes(value)}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setStatus([...status, value])
                    } else {
                      setStatus(status.filter(s => s !== value))
                    }
                  }}
                />
                <Label htmlFor={`status-${value}`} className="font-normal">
                  <Badge variant="outline" className={cn("font-medium", accountStatusConfig[value].color)}>
                    {accountStatusConfig[value].label}
                  </Badge>
                </Label>
              </div>
            ))}
          </div>
        </div>

        {/* Loại tài khoản */}
        <div className="space-y-2">
          <Label>Loại tài khoản</Label>
          <div className="space-y-1">
            {Object.entries(AccountType).map(([key, value]) => (
              <div key={value} className="flex items-center space-x-2">
                <Checkbox
                  id={`type-${value}`}
                  checked={type.includes(value)}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setType([...type, value])
                    } else {
                      setType(type.filter(t => t !== value))
                    }
                  }}
                />
                <Label htmlFor={`type-${value}`} className="font-normal">
                  <Badge variant="outline" className={cn("font-medium", accountTypeConfig[value].color)}>
                    {accountTypeConfig[value].label}
                  </Badge>
                </Label>
              </div>
            ))}
          </div>
        </div>

        {/* Kích thước */}
        <div className="space-y-2">
          <Label>Kích thước</Label>
          <div className="space-y-1">
            {Object.entries(AccountSize).map(([key, value]) => (
              <div key={value} className="flex items-center space-x-2">
                <Checkbox
                  id={`size-${value}`}
                  checked={size.includes(value)}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setSize([...size, value])
                    } else {
                      setSize(size.filter(s => s !== value))
                    }
                  }}
                />
                <Label htmlFor={`size-${value}`} className="font-normal">
                  <Badge variant="outline" className={cn("font-medium", accountSizeConfig[value].color)}>
                    {accountSizeConfig[value].label}
                  </Badge>
                </Label>
              </div>
            ))}
          </div>
        </div>

        {/* Doanh số */}
        <div className="space-y-2">
          <Label>Doanh số (VNĐ)</Label>
          <div className="space-y-2">
            <RangeSlider
              min={0}
              max={********00}
              step={********}
              value={revenueRange}
              onChange={setRevenueRange}
            />
            <div className="flex justify-between text-sm">
              <span>{new Intl.NumberFormat('vi-VN').format(revenueRange[0])}</span>
              <span>{new Intl.NumberFormat('vi-VN').format(revenueRange[1])}</span>
            </div>
          </div>
        </div>

        {/* Người phụ trách */}
        <div className="space-y-2">
          <Label>Người phụ trách</Label>
          <Input
            placeholder="Nhập tên người phụ trách"
            value={assignedTo}
            onChange={(e) => setAssignedTo(e.target.value)}
          />
        </div>
      </div>

      <div className="flex justify-end space-x-2 pt-2">
        <Button onClick={handleApplyFilters}>
          Áp dụng bộ lọc
        </Button>
      </div>
    </div>
  )
}
