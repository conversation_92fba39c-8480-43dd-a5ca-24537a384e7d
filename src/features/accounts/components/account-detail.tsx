import { useState } from 'react'
import { <PERSON> } from '@tanstack/react-router'
import { useAccountsContext } from '../context/use-accounts-context'
import {
  Mail,
  Pencil,
  Calendar,
  Phone,
  MessageSquare,
  Globe,
  Building,
  MapPin,
  Info,
  Plus,
  Clock,
  CheckCircle2,
  Star,
  MoreVertical,
  ShoppingCart,
  Activity,
  FileText,
  ListTodo,
  Users,
  DollarSign,
  ExternalLink,
  Link as LinkIcon,
  X
} from 'lucide-react'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import {
  Toolt<PERSON>,
  <PERSON><PERSON>ipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui/tooltip'
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card'
import { Breadcrumb, BreadcrumbList, BreadcrumbItem, BreadcrumbLink, BreadcrumbSeparator, BreadcrumbPage } from '@/components/ui/breadcrumb'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { type Account, AccountStatus, AccountType } from '../data/schema'

interface AccountDetailProps {
  account: Account
}

export function AccountDetail({ account }: AccountDetailProps) {
  const { setOpenDialog, setSelectedAccountIds } = useAccountsContext()
  const [activeTab, setActiveTab] = useState('overview')

  const handleCreateOrder = () => {
    setSelectedAccountIds([account.id])
    setOpenDialog('create-order')
  }

  // Tạo avatar fallback từ tên
  const getInitials = (name: string) => {
    if (!name) return "NA"
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
  }

  const getStatusBadge = (status: string) => {
    const statusMap: Record<string, { color: string; icon: React.ReactNode; bg: string }> = {
      [AccountStatus.NEW_CUSTOMER]: {
        color: 'text-blue-700',
        bg: 'bg-blue-50',
        icon: <Users className="h-3.5 w-3.5 mr-1" />
      },
      [AccountStatus.ACTIVE]: {
        color: 'text-emerald-700',
        bg: 'bg-emerald-50',
        icon: <CheckCircle2 className="h-3.5 w-3.5 mr-1" />
      },
      [AccountStatus.VIP]: {
        color: 'text-amber-700',
        bg: 'bg-amber-50',
        icon: <Star className="h-3.5 w-3.5 mr-1" />
      },
      [AccountStatus.AT_RISK]: {
        color: 'text-orange-700',
        bg: 'bg-orange-50',
        icon: <Activity className="h-3.5 w-3.5 mr-1" />
      },
      [AccountStatus.INACTIVE]: {
        color: 'text-gray-700',
        bg: 'bg-gray-50',
        icon: <Clock className="h-3.5 w-3.5 mr-1" />
      },
      [AccountStatus.CHURNED]: {
        color: 'text-red-700',
        bg: 'bg-red-50',
        icon: <X className="h-3.5 w-3.5 mr-1" />
      }
    }

    if (!status) return null

    const { color, icon, bg } = statusMap[status] || {
      color: 'text-gray-700',
      bg: 'bg-gray-50',
      icon: null
    }

    return (
      <Badge variant="outline" className={`flex items-center gap-1 px-2 py-1 ${bg} ${color} border-transparent`}>
        {icon}
        <span className="capitalize font-medium">
          {status === AccountStatus.NEW_CUSTOMER ? 'Khách hàng mới' :
           status === AccountStatus.ACTIVE ? 'Đang hoạt động' :
           status === AccountStatus.VIP ? 'VIP' :
           status === AccountStatus.AT_RISK ? 'Có nguy cơ' :
           status === AccountStatus.INACTIVE ? 'Không hoạt động' :
           status === AccountStatus.CHURNED ? 'Đã churn' : status}
        </span>
      </Badge>
    )
  }

  const getTypeBadge = (type: string | undefined) => {
    if (!type) return null

    const typeMap: Record<string, { icon: React.ReactNode; color: string; bg: string }> = {
      [AccountType.BUSINESS]: {
        icon: <Building className="h-3 w-3 mr-1" />,
        color: 'text-indigo-700',
        bg: 'bg-indigo-50'
      },
      [AccountType.INDIVIDUAL]: {
        icon: <Users className="h-3 w-3 mr-1" />,
        color: 'text-purple-700',
        bg: 'bg-purple-50'
      }
    }

    const { icon, color, bg } = typeMap[type] || typeMap[AccountType.BUSINESS]

    return (
      <Badge variant="outline" className={`flex items-center gap-1 px-2 py-1 ${bg} ${color} border-transparent`}>
        {icon}
        <span className="capitalize font-medium">
          {type === AccountType.BUSINESS ? 'Doanh nghiệp' : 'Cá nhân'}
        </span>
      </Badge>
    )
  }

  return (
    <>
      <Header fixed>
        <Breadcrumb>
          <BreadcrumbList className="mb-0">
            <BreadcrumbItem>
              <BreadcrumbLink asChild><Link to="/accounts" className="text-sm">Tài khoản</Link></BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-sm">{account.name}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </Header>
      <Main>
        <div className="container py-6 px-4 max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="flex items-center gap-3">
                <Avatar className="h-12 w-12 border-2 border-background shadow-sm">
                  <AvatarFallback className="bg-primary/90 text-white">
                    {getInitials(account.name)}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <div className="flex items-center gap-2 flex-wrap">
                    <h1 className="text-2xl font-bold tracking-tight">{account.name}</h1>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full">
                            <Star className="h-[18px] w-[18px] text-muted-foreground" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Đánh dấu quan trọng</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <div className="flex items-center gap-2 mt-1">
                    {getStatusBadge(account.status)}
                    {getTypeBadge(account.type)}
                    <Badge variant="outline" className="flex items-center gap-1 px-2 py-1 bg-gray-50 text-gray-700 border-transparent">
                      <Calendar className="h-3.5 w-3.5 mr-1" />
                      <span>{new Date(account.updatedAt).toLocaleDateString('vi-VN')}</span>
                    </Badge>
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <HoverCard>
                  <HoverCardTrigger asChild>
                    <Button variant="outline" size="sm" className="gap-1.5">
                      <Avatar className="h-5 w-5">
                        <AvatarFallback className="text-[10px] bg-primary/90 text-white">
                          {account.accountOwner?.substring(0, 2) || 'JD'}
                        </AvatarFallback>
                      </Avatar>
                      <span>{account.accountOwner || 'Chưa phân công'}</span>
                    </Button>
                  </HoverCardTrigger>
                  <HoverCardContent className="w-64">
                    <div className="flex justify-between space-x-4">
                      <Avatar>
                        <AvatarFallback className="bg-primary/90 text-white">
                          {account.accountOwner?.substring(0, 2) || 'JD'}
                        </AvatarFallback>
                      </Avatar>
                      <div className="space-y-1">
                        <h4 className="text-sm font-semibold">{account.accountOwner || 'Chưa phân công'}</h4>
                        <p className="text-sm text-muted-foreground">Người phụ trách account</p>
                        <div className="flex items-center pt-1">
                          <Button variant="link" size="sm" className="px-0 h-auto text-xs">
                            Đổi người phụ trách
                          </Button>
                        </div>
                      </div>
                    </div>
                  </HoverCardContent>
                </HoverCard>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="icon" className="h-9 w-9">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>
                      <Calendar className="h-4 w-4 mr-2" />
                      <span>Thêm nhiệm vụ</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <MessageSquare className="h-4 w-4 mr-2" />
                      <span>Thêm ghi chú</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <FileText className="h-4 w-4 mr-2" />
                      <span>Thêm tài liệu</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Phone className="h-4 w-4 mr-2" />
                      <span>Ghi nhận cuộc gọi</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Mail className="h-4 w-4 mr-2" />
                      <span>Gửi email</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>

                <Button className="gap-2" onClick={handleCreateOrder}>
                  <ShoppingCart className="h-4 w-4" />
                  <span>Tạo đơn hàng</span>
                </Button>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="icon" className="h-9 w-9">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>
                      <Pencil className="h-4 w-4 mr-2" />
                      <span>Chỉnh sửa account</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Users className="h-4 w-4 mr-2" />
                      <span>Thay đổi người phụ trách</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem className="text-destructive focus:text-destructive">
                      <div className="flex items-center">
                        <Info className="h-4 w-4 mr-2" />
                        <span>Đánh dấu không phù hợp</span>
                      </div>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>

          {/* Main content */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <div className="w-full overflow-x-auto pb-2">
              <TabsList>
                <TabsTrigger value="overview"><Info className="h-4 w-4 mr-2" /> Tổng quan</TabsTrigger>
                <TabsTrigger value="activity"><Activity className="h-4 w-4 mr-2" /> Hoạt động</TabsTrigger>
                <TabsTrigger value="tasks"><ListTodo className="h-4 w-4 mr-2" /> Nhiệm vụ</TabsTrigger>
                <TabsTrigger value="notes"><FileText className="h-4 w-4 mr-2" /> Ghi chú</TabsTrigger>

                <TabsTrigger value="messages"><MessageSquare className="h-4 w-4 mr-2" /> Tin nhắn</TabsTrigger>
                <TabsTrigger value="related"><LinkIcon className="h-4 w-4 mr-2" /> Khác</TabsTrigger>
              </TabsList>
            </div>

            {/* Tab Contents */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:items-start">
              {/* Main Content Area */}
              <div className="md:col-span-2">
                <TabsContent value="overview" className="mt-0 space-y-4">
                  <AccountOverview account={account} getStatusBadge={getStatusBadge} />
                </TabsContent>

                <TabsContent value="activity" className="mt-0 space-y-4">
                  <ActivityFeed />
                </TabsContent>

                <TabsContent value="tasks" className="mt-0 space-y-4">
                  <TasksList />
                </TabsContent>

                <TabsContent value="notes" className="mt-0 space-y-4">
                  <NotesList />
                </TabsContent>



                <TabsContent value="messages" className="mt-0 space-y-4">
                  <OmnichannelMessages account={account} />
                </TabsContent>

                <TabsContent value="related" className="mt-0 space-y-4">
                  <RelatedItems account={account} />
                </TabsContent>
              </div>

              {/* Right Sidebar - Contact Info */}
              <div className="md:col-span-1">
                <ContactInfoPanel account={account} />
              </div>
            </div>
          </Tabs>
        </div>
      </Main>
    </>
  )
}

function AccountOverview({ account, getStatusBadge }: { account: Account; getStatusBadge: (status: string) => React.ReactNode }) {
  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="text-base font-medium">Thông tin tổng quan</CardTitle>
          <CardDescription>Thông tin tổng quan về tài khoản</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <div className="text-sm font-medium text-muted-foreground">Trạng thái</div>
              <div className="mt-1">{getStatusBadge(account.status)}</div>
            </div>
            
            <div>
              <div className="text-sm font-medium text-muted-foreground">Loại tài khoản</div>
              <div className="mt-1 text-sm">
                {account.type === AccountType.BUSINESS ? 'Doanh nghiệp' : 'Cá nhân'}
              </div>
            </div>
            
            <div>
              <div className="text-sm font-medium text-muted-foreground">Giá trị trọn đời</div>
              <div className="mt-1 text-sm font-medium">
                {new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(account.lifetimeValue)}
              </div>
            </div>
            
            <div>
              <div className="text-sm font-medium text-muted-foreground">Tổng đơn hàng</div>
              <div className="mt-1 text-sm">{account.totalOrders || 0} đơn</div>
            </div>
            
            {account.industry && (
              <div>
                <div className="text-sm font-medium text-muted-foreground">Ngành nghề</div>
                <div className="mt-1 text-sm">{account.industry}</div>
              </div>
            )}
            
            {account.customerSince && (
              <div>
                <div className="text-sm font-medium text-muted-foreground">Khách hàng từ</div>
                <div className="mt-1 text-sm">
                  {new Date(account.customerSince).toLocaleDateString('vi-VN')}
                </div>
              </div>
            )}
          </div>
          
          {account.notes && (
            <div className="pt-4 border-t">
              <div className="text-sm font-medium text-muted-foreground mb-2">Ghi chú</div>
              <div className="p-3 bg-muted/30 rounded-md text-sm">{account.notes}</div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

function ContactInfoPanel({ account }: { account: Account }) {
  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="text-base font-medium">Thông tin liên hệ</CardTitle>
          <CardDescription>Chi tiết liên hệ của tài khoản</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {account.email && (
            <div className="flex items-center gap-3">
              <Mail className="h-4 w-4 text-muted-foreground" />
              <div>
                <div className="text-sm font-medium">Email</div>
                <div className="text-sm text-muted-foreground">{account.email}</div>
              </div>
            </div>
          )}
          
          {account.phone && (
            <div className="flex items-center gap-3">
              <Phone className="h-4 w-4 text-muted-foreground" />
              <div>
                <div className="text-sm font-medium">Điện thoại</div>
                <div className="text-sm text-muted-foreground">{account.phone}</div>
              </div>
            </div>
          )}
          
          {account.website && (
            <div className="flex items-center gap-3">
              <Globe className="h-4 w-4 text-muted-foreground" />
              <div>
                <div className="text-sm font-medium">Website</div>
                <div className="text-sm text-muted-foreground">{account.website}</div>
              </div>
            </div>
          )}
          
          {account.industry && (
            <div className="flex items-center gap-3">
              <Building className="h-4 w-4 text-muted-foreground" />
              <div>
                <div className="text-sm font-medium">Ngành nghề</div>
                <div className="text-sm text-muted-foreground">{account.industry}</div>
              </div>
            </div>
          )}
          
          {account.address && (
            <div className="flex items-center gap-3">
              <MapPin className="h-4 w-4 text-muted-foreground" />
              <div>
                <div className="text-sm font-medium">Địa chỉ</div>
                <div className="text-sm text-muted-foreground">{account.address}</div>
              </div>
            </div>
          )}
          
          {account.tags && account.tags.length > 0 && (
            <div>
              <div className="text-sm font-medium mb-2">Tags</div>
              <div className="flex flex-wrap gap-1">
                {account.tags.map((tag, index) => (
                  <Badge key={index} variant="secondary" className="capitalize">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}
          
          {account.notes && (
            <div>
              <div className="text-sm font-medium mb-2">Ghi chú</div>
              <div className="p-3 bg-muted/30 rounded-md">
                <div className="text-sm">{account.notes}</div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

function ActivityFeed() {
  const activities = [
    {
      id: '1',
      type: 'email',
      title: 'Đã gửi email giới thiệu sản phẩm',
      timestamp: '2023-05-15T10:30:00',
      user: 'John Doe',
      content: 'Email giới thiệu sản phẩm mới đã được gửi đến khách hàng.',
      icon: <Mail className="h-4 w-4" />
    },
    {
      id: '2',
      type: 'call',
      title: 'Cuộc gọi tư vấn',
      timestamp: '2023-05-14T14:45:00',
      user: 'Jane Smith',
      content: 'Đã gọi điện tư vấn về sản phẩm. Khách hàng quan tâm và yêu cầu thêm thông tin.',
      icon: <Phone className="h-4 w-4" />
    }
  ]

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="text-base font-medium">Hoạt động gần đây</CardTitle>
          <CardDescription>Lịch sử hoạt động của account</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="relative space-y-4 before:absolute before:inset-0 before:left-4 before:h-full before:w-0.5 before:bg-border">
            {activities.map((activity) => (
              <div key={activity.id} className="relative pl-10">
                <div className="absolute left-0 flex h-8 w-8 items-center justify-center rounded-full border bg-background">
                  {activity.icon}
                </div>
                <div className="rounded-lg border bg-card p-3 text-card-foreground shadow-sm">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 mb-2">
                    <h4 className="font-medium">{activity.title}</h4>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Clock className="h-3.5 w-3.5" />
                      <span>{formatDateTime(activity.timestamp)}</span>
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground">{activity.content}</p>
                  <div className="mt-2 flex items-center gap-2">
                    <Avatar className="h-5 w-5">
                      <AvatarFallback className="text-[10px]">{activity.user.substring(0, 2)}</AvatarFallback>
                    </Avatar>
                    <span className="text-xs text-muted-foreground">{activity.user}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

function TasksList() {
  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-3">
          <div>
            <CardTitle className="text-base font-medium">Nhiệm vụ</CardTitle>
            <CardDescription>Danh sách nhiệm vụ</CardDescription>
          </div>
          <Button variant="outline" size="sm">
            <Plus className="h-3.5 w-3.5 mr-2" />
            Thêm nhiệm vụ
          </Button>
        </CardHeader>
        <CardContent className="p-0">
          <div className="divide-y">
            <TaskItem
              title="Gọi điện giới thiệu sản phẩm"
              dueDate="Ngày mai, 10:00"
              assignee="John Doe"
              status="pending"
            />
            <TaskItem
              title="Gửi email giới thiệu demo"
              dueDate="Hôm nay, 15:00"
              assignee="John Doe"
              status="pending"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

function TaskItem({ title, dueDate, assignee, status }: { title: string; dueDate: string; assignee: string; status: string }) {
  return (
    <div className="flex items-center justify-between p-4">
      <div className="flex gap-4 items-center">
        <div className="flex flex-col">
          <div className="font-medium">{title}</div>
          <div className="text-sm text-muted-foreground flex items-center gap-1">
            <Clock className="h-3 w-3" /> {dueDate}
          </div>
        </div>
      </div>
      <div className="flex gap-2 items-center">
        <Avatar className="h-8 w-8">
          <AvatarFallback>{assignee.charAt(0)}</AvatarFallback>
        </Avatar>
        <Badge variant={status === "completed" ? "default" : "outline"}>
          {status === "completed" ? "Hoàn thành" : "Đang chờ"}
        </Badge>
      </div>
    </div>
  )
}

function NotesList() {
  const [showAddNote, setShowAddNote] = useState(false)
  const [noteContent, setNoteContent] = useState('')

  const notes = [
    {
      id: '1',
      content: 'Khách hàng quan tâm đến gói Premium. Cần gửi thêm thông tin về giá và tính năng.',
      createdBy: 'John Doe',
      createdAt: '2023-05-14T15:00:00'
    }
  ]

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-3">
          <div>
            <CardTitle className="text-base font-medium">Ghi chú</CardTitle>
            <CardDescription>Danh sách ghi chú</CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAddNote(!showAddNote)}
          >
            {showAddNote ? (
              <>
                <X className="h-3.5 w-3.5 mr-2" />
                Hủy
              </>
            ) : (
              <>
                <Plus className="h-3.5 w-3.5 mr-2" />
                Thêm ghi chú
              </>
            )}
          </Button>
        </CardHeader>
        <CardContent>
          {showAddNote && (
            <div className="mb-4 border rounded-md p-3">
              <div className="mb-2">
                <textarea
                  className="w-full min-h-[120px] p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  placeholder="Nhập nội dung ghi chú..."
                  value={noteContent}
                  onChange={(e) => setNoteContent(e.target.value)}
                />
              </div>
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <FileText className="h-4 w-4" />
                  </Button>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={() => setShowAddNote(false)}>
                    Hủy
                  </Button>
                  <Button size="sm" onClick={() => { setNoteContent(''); setShowAddNote(false); }}>
                    Lưu ghi chú
                  </Button>
                </div>
              </div>
            </div>
          )}

          {notes.length > 0 ? (
            <div className="space-y-3">
              {notes.map((note) => (
                <div key={note.id} className="border rounded-md p-3">
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex items-center gap-2">
                      <Avatar className="h-6 w-6">
                        <AvatarFallback className="text-[10px]">{note.createdBy.substring(0, 2)}</AvatarFallback>
                      </Avatar>
                      <span className="text-sm font-medium">{note.createdBy}</span>
                    </div>
                    <span className="text-xs text-muted-foreground">{formatDateTime(note.createdAt)}</span>
                  </div>
                  <p className="text-sm">{note.content}</p>
                </div>
              ))}
            </div>
          ) : (
            <div className="py-8 text-center">
              <FileText className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
              <p className="text-sm font-medium">Không có ghi chú nào</p>
              <p className="text-sm text-muted-foreground mt-1">
                Thêm ghi chú đầu tiên để theo dõi thông tin quan trọng.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}



function OmnichannelMessages({ account: _account }: { account: Account }) {
  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-3">
          <div>
            <CardTitle className="text-base font-medium">Tin nhắn</CardTitle>
            <CardDescription>Tin nhắn từ các kênh omnichannel</CardDescription>
          </div>
          <Button variant="outline" size="sm">
            <Plus className="h-3.5 w-3.5 mr-2" />
            Gửi tin nhắn
          </Button>
        </CardHeader>
        <CardContent>
          <div className="py-8 text-center">
            <MessageSquare className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
            <p className="text-sm font-medium">Chưa có tin nhắn nào</p>
            <p className="text-sm text-muted-foreground mt-1">
              Tin nhắn từ Messenger, Zalo sẽ hiển thị tại đây.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

function RelatedItems({ account }: { account: Account }) {
  return (
    <div className="space-y-6">

      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-3">
          <div>
            <CardTitle className="text-base font-medium">Deals</CardTitle>
            <CardDescription>Các cơ hội bán hàng với tài khoản</CardDescription>
          </div>
          <Button variant="outline" size="sm">
            <Plus className="h-3.5 w-3.5 mr-2" />
            Tạo deal
          </Button>
        </CardHeader>
        <CardContent className="p-0">
          <div className="p-6 text-center">
            <DollarSign className="mx-auto h-8 w-8 text-muted-foreground mb-3" />
            <h3 className="text-base font-medium">Không có deal</h3>
            <p className="text-sm text-muted-foreground mt-1 mb-4">
              Account này chưa được chuyển đổi thành deal
            </p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-3">
          <div>
            <CardTitle className="text-base font-medium">Đơn hàng</CardTitle>
            <CardDescription>Lịch sử đơn hàng của tài khoản</CardDescription>
          </div>
          <Button variant="outline" size="sm">
            <Plus className="h-3.5 w-3.5 mr-2" />
            Tạo đơn hàng
          </Button>
        </CardHeader>
        <CardContent className="p-0">
          {account.orderIds && account.orderIds.length > 0 ? (
            <div className="divide-y">
              {account.orderIds.slice(0, 3).map((orderId) => (
                <div key={orderId} className="p-4 flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="flex-shrink-0 w-9 h-9 rounded-md bg-green-100 flex items-center justify-center">
                      <ShoppingCart className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <div className="font-medium">Đơn hàng #{orderId}</div>
                      <div className="text-sm text-muted-foreground">Đã hoàn thành</div>
                    </div>
                  </div>
                  <Button variant="ghost" size="icon">
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          ) : (
            <div className="p-6 text-center">
              <ShoppingCart className="mx-auto h-8 w-8 text-muted-foreground mb-3" />
              <h3 className="text-base font-medium">Không có đơn hàng</h3>
              <p className="text-sm text-muted-foreground mt-1 mb-4">
                Tài khoản này chưa có đơn hàng nào
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default AccountDetail