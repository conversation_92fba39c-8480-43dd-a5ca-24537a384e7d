import { PlusIcon, DownloadIcon } from '@radix-ui/react-icons'
import { Upload } from 'lucide-react'
import { PrimaryButtonsGroup } from '@/components/ui/primary-buttons-group'
import { useAccountsContext } from '../context/use-accounts-context'

export function AccountsPrimaryButtons() {
  const { setOpenDialog } = useAccountsContext()

  const buttons = [
    {
      label: 'Tạo tài khoản',
      icon: PlusIcon,
      onClick: () => setOpenDialog('createAccount'),
    },
    {
      label: 'Nhập',
      icon: Upload,
      onClick: () => setOpenDialog('importAccounts'),
      variant: 'outline' as const,
    },
    {
      label: 'Xuất',
      icon: DownloadIcon,
      onClick: () => setOpenDialog('exportAccounts'),
      variant: 'outline' as const,
    },
  ]

  return <PrimaryButtonsGroup buttons={buttons} />
}
