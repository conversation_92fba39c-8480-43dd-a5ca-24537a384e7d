import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'

import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useAccountsContext } from '../context/use-accounts-context'
import { useState } from 'react'
import { AccountStatus, AccountType, AccountSize } from '../data/schema'
import { v4 as uuidv4 } from 'uuid'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'

export function AccountsDialogs() {
  const { openDialog, setOpenDialog, accounts, setAccounts, selectedAccountIds } = useAccountsContext()
  
  // Form state for creating/editing account
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    address: '',
    industry: '',
    size: AccountSize.SMALL,
    type: AccountType.BUSINESS,
    status: AccountStatus.ACTIVE,
    notes: ''
  })
  
  // Reset form data
  const resetForm = () => {
    setFormData({
      name: '',
      email: '',
      phone: '',
      company: '',
      address: '',
      industry: '',
      size: AccountSize.SMALL,
      type: AccountType.BUSINESS,
      status: AccountStatus.ACTIVE,
      notes: ''
    })
  }
  
  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }
  
  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }))
  }
  
  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    const newAccount = {
      id: uuidv4(),
      name: formData.name,
      email: formData.email,
      phone: formData.phone,
      company: formData.company,
      address: formData.address,
      industry: formData.industry,
      size: formData.size,
      type: formData.type,
      status: formData.status,
      notes: formData.notes,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      totalOrders: 0,
      totalRevenue: 0,
      tags: []
    }
    
    setAccounts([newAccount, ...accounts])
    setOpenDialog('')
    resetForm()
  }
  
  // Handle account deletion
  const handleDeleteAccount = () => {
    setAccounts(accounts.filter(account => !selectedAccountIds.includes(account.id)))
    setOpenDialog('')
  }
  
  return (
    <>
      {/* Create Account Dialog */}
      <Dialog open={openDialog === 'createAccount'} onOpenChange={() => setOpenDialog('')}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Tạo tài khoản mới</DialogTitle>
            <DialogDescription>
              Nhập thông tin để tạo tài khoản mới. Nhấn Lưu khi hoàn tất.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Tên</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">Điện thoại</Label>
                  <Input
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="company">Công ty</Label>
                  <Input
                    id="company"
                    name="company"
                    value={formData.company}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="type">Loại tài khoản</Label>
                  <Select
                    value={formData.type}
                    onValueChange={(value) => handleSelectChange('type', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Chọn loại tài khoản" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={AccountType.BUSINESS}>Doanh nghiệp</SelectItem>
                      <SelectItem value={AccountType.INDIVIDUAL}>Cá nhân</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="status">Trạng thái</Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value) => handleSelectChange('status', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Chọn trạng thái" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={AccountStatus.ACTIVE}>Đang hoạt động</SelectItem>
                      <SelectItem value={AccountStatus.INACTIVE}>Không hoạt động</SelectItem>
                      <SelectItem value={AccountStatus.POTENTIAL}>Tiềm năng</SelectItem>
                      <SelectItem value={AccountStatus.VIP}>VIP</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="col-span-2 space-y-2">
                  <Label htmlFor="notes">Ghi chú</Label>
                  <Textarea
                    id="notes"
                    name="notes"
                    value={formData.notes}
                    onChange={handleInputChange}
                    className="resize-none"
                  />
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button type="submit">Lưu</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
      
      {/* Import Accounts Dialog */}
      <Dialog open={openDialog === 'importAccounts'} onOpenChange={() => setOpenDialog('')}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Nhập danh sách tài khoản</DialogTitle>
            <DialogDescription>
              Tải lên file Excel hoặc CSV chứa danh sách tài khoản.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="file">Chọn file</Label>
              <Input id="file" type="file" />
            </div>
            <div className="text-sm text-muted-foreground">
              <p>Hỗ trợ định dạng .xlsx, .xls và .csv</p>
              <p>Kích thước tối đa: 5MB</p>
            </div>
          </div>
          <DialogFooter>
            <Button type="submit">Nhập</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Export Accounts Dialog */}
      <Dialog open={openDialog === 'exportAccounts'} onOpenChange={() => setOpenDialog('')}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Xuất danh sách tài khoản</DialogTitle>
            <DialogDescription>
              Chọn định dạng và các trường dữ liệu để xuất.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="format">Định dạng</Label>
              <Select defaultValue="excel">
                <SelectTrigger>
                  <SelectValue placeholder="Chọn định dạng" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="excel">Excel (.xlsx)</SelectItem>
                  <SelectItem value="csv">CSV (.csv)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button type="submit">Xuất</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Delete Account Dialog */}
      <AlertDialog open={openDialog === 'delete'} onOpenChange={() => setOpenDialog('')}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Bạn có chắc chắn muốn xóa?</AlertDialogTitle>
            <AlertDialogDescription>
              Hành động này không thể hoàn tác. Dữ liệu sẽ bị xóa vĩnh viễn khỏi hệ thống.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Hủy</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteAccount} className="bg-destructive text-destructive-foreground">
              Xóa
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
