import { ColumnDef } from '@tanstack/react-table'
import { formatCurrency, getInitials, accountStatusConfig, accountTypeConfig } from '../data/data'
import { Account, AccountStatusValue, AccountTypeValue } from '../data/schema'
import { DataTableColumnHeader } from '@/components/ui/data-table'
import { DataTableRowActions, NameLink } from '.'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { cn } from '@/lib/utils'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'

export const columns: ColumnDef<Account>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Chọn tất cả"
        className="translate-y-[2px]"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Chọn hàng"
        className="translate-y-[2px]"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Tên" />
    ),
    cell: ({ row }) => <NameLink row={row} />,
    enableSorting: true,
    enableHiding: false,
  },
  {
    accessorKey: 'status',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Trạng thái" />
    ),
    cell: ({ row }) => {
      const status = row.getValue('status') as AccountStatusValue
      const statusInfo = accountStatusConfig[status]
      
      return (
        <Badge className={cn("font-medium rounded-md", statusInfo.color)}>
          {statusInfo.label}
        </Badge>
      )
    },
    enableSorting: true,
    enableHiding: true,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    accessorKey: 'type',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Loại" />
    ),
    cell: ({ row }) => {
      const type = row.getValue('type') as AccountTypeValue
      const typeInfo = accountTypeConfig[type]
      
      return (
        <Badge className={cn("font-medium rounded-md", typeInfo.color)}>
          {typeInfo.label}
        </Badge>
      )
    },
    enableSorting: true,
    enableHiding: true,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    accessorKey: 'email',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Email" />
    ),
    cell: ({ row }) => {
      return (
        <div className="font-medium">{row.getValue('email') as string || '-'}</div>
      )
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'phone',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Điện thoại" />
    ),
    cell: ({ row }) => {
      return (
        <div className="font-medium">{row.getValue('phone') as string || '-'}</div>
      )
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'totalOrders',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Số đơn hàng" />
    ),
    cell: ({ row }) => {
      return (
        <div className="font-medium text-center">{row.getValue('totalOrders') as number}</div>
      )
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'totalRevenue',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Doanh số" />
    ),
    cell: ({ row }) => {
      const amount = row.getValue('totalRevenue') as number
      
      return (
        <div className="font-medium">{formatCurrency(amount)}</div>
      )
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'assignedTo',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Người phụ trách" />
    ),
    cell: ({ row }) => {
      const assignedTo = row.getValue('assignedTo') as string
      if (!assignedTo) return <div>-</div>
      
      const initials = getInitials(assignedTo)
      
      return (
        <div className="flex items-center gap-2">
          <Avatar className="h-8 w-8">
            <AvatarFallback>{initials}</AvatarFallback>
          </Avatar>
          <div className="font-medium">{assignedTo}</div>
        </div>
      )
    },
    enableSorting: true,
    enableHiding: true,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => <DataTableRowActions row={row} />,
  },
]
