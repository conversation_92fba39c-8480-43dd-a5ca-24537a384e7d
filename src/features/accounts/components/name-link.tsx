import { Row } from '@tanstack/react-table'
import { <PERSON> } from '@tanstack/react-router'
import { Account } from '../data/schema'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { getInitials } from '../data/data'

interface NameLinkProps {
  row: Row<Account>
}

export function NameLink({ row }: NameLinkProps) {
  const account = row.original
  const initials = getInitials(account.name)
  
  return (
    <div className="flex items-center gap-2">
      <Avatar className="h-8 w-8">
        {account.avatar ? (
          <AvatarImage src={account.avatar} alt={account.name} />
        ) : null}
        <AvatarFallback className="bg-primary/10 text-foreground text-xs">
          {initials}
        </AvatarFallback>
      </Avatar>
      <Link
        to="/accounts/$accountId"
        params={{ accountId: account.id }}
        className="font-medium hover:underline"
      >
        {account.name}
      </Link>
    </div>
  )
}
