import { Button } from '@/components/ui/button'
import { 
  <PERSON>per, 
  StepperItem, 
  StepperSeparator, 
  StepperTrigger, 
  StepperTitle, 
  StepperDescription 
} from '@/components/ui/stepper'
import { cn } from '@/lib/utils'
import { Circle, Phone, Star, XCircle, Check } from 'lucide-react'
import { Lead, LeadStatus } from '../data/schema'

interface SalesPathProps {
  lead: Lead
  onStatusChange?: (status: string) => void
}

// Các giai đoạn trong lead lifecycle
const stages = [
  { 
    value: LeadStatus.OPEN, 
    label: 'Mở', 
    icon: Circle,
    description: 'Lead mới được tạo'
  },
  { 
    value: LeadStatus.CONTACTED, 
    label: 'Đã liên hệ', 
    icon: Phone,
    description: 'Đã liên hệ với lead'
  },
  { 
    value: LeadStatus.QUALIFIED, 
    label: 'Đủ điều kiện', 
    icon: Star,
    description: 'Lead đủ điều kiện chuyển đổi'
  },
  { 
    value: LeadStatus.UNQUALIFIED, 
    label: '<PERSON>hông phù hợp', 
    icon: XCircle,
    description: 'Lead không phù hợp'
  }
]

export function SalesPath({ lead, onStatusChange }: SalesPathProps) {
  const currentStageIndex = stages.findIndex(s => s.value === lead.status)
  const currentStep = currentStageIndex + 1

  const handleStatusChange = (status: string) => {
    if (onStatusChange) {
      onStatusChange(status)
    }
  }
  
  return (
    <div className="space-y-4">
      <div className="text-sm font-medium text-muted-foreground">
        Tiến trình Lead
      </div>
      
      <Stepper currentStep={currentStep} className="flex w-full items-start gap-2">
        {stages.map((stage, index) => {
          const stepNumber = index + 1
          const Icon = stage.icon
          
          return (
            <StepperItem
              key={stage.value}
              step={stepNumber}
              className="relative flex w-full flex-col items-center justify-center"
            >
              {({ state }) => (
                <>
                  {index < stages.length - 1 && (
                    <StepperSeparator />
                  )}
                  
                  <StepperTrigger>
                    <Button
                      variant={state === 'completed' || state === 'active' ? 'default' : 'outline'}
                      size="icon"
                      className={cn(
                        'z-10 rounded-full shrink-0',
                        onStatusChange && 'cursor-pointer hover:opacity-80'
                      )}
                      onClick={() => handleStatusChange(stage.value)}
                      disabled={!onStatusChange}
                    >
                      {state === 'completed' && <Check className="h-5 w-5" />}
                      {(state === 'active' || state === 'inactive') && <Icon className="h-5 w-5" />}
                    </Button>
                  </StepperTrigger>
                  
                  <div className="mt-5 flex flex-col items-center text-center">
                    <StepperTitle
                      className={cn(
                        'text-sm font-semibold transition lg:text-base',
                        state === 'active' && 'text-primary'
                      )}
                    >
                      {stage.label}
                    </StepperTitle>
                    <StepperDescription
                      className={cn(
                        'text-xs text-muted-foreground transition md:text-sm',
                        state === 'active' && 'text-primary'
                      )}
                    >
                      {stage.description}
                    </StepperDescription>
                  </div>
                </>
              )}
            </StepperItem>
          )
        })}
      </Stepper>
      
      {/* Status summary */}
      <div className="flex items-center justify-between text-sm text-muted-foreground bg-muted/50 rounded-lg p-3">
        <div className="flex items-center gap-2">
          <div className={cn(
            'w-2 h-2 rounded-full',
            lead.status === LeadStatus.OPEN && 'bg-blue-500',
            lead.status === LeadStatus.CONTACTED && 'bg-orange-500',
            lead.status === LeadStatus.QUALIFIED && 'bg-green-500',
            lead.status === LeadStatus.UNQUALIFIED && 'bg-red-500'
          )} />
          <span>
            Trạng thái hiện tại: <strong>{stages.find(s => s.value === lead.status)?.label}</strong>
          </span>
        </div>
        
        {lead.lastContactedAt && (
          <span>
            Liên hệ cuối: {new Date(lead.lastContactedAt).toLocaleDateString('vi-VN')}
          </span>
        )}
      </div>
    </div>
  )
}

export default SalesPath