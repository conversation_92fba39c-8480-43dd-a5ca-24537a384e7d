import { Fragment, useEffect, useState } from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm, type SubmitHandler } from 'react-hook-form'
import * as z from 'zod'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { ConvertLeadWizard } from './convert-lead-wizard'
import { LeadsImportDialog } from './leads-import-dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { useLeadsContext } from '../context/use-leads-context'
import { sourceTypes } from '../data/data'
import { leads } from '../data/leads'
import { type Lead } from '../data/schema'

const formSchema = z.object({
  name: z.string().min(2, {
    message: '<PERSON><PERSON>n phải có ít nhất 2 ký tự.',
  }),
  status: z.string(),
  email: z.string().email({
    message: 'Email không hợp lệ.',
  }).optional().or(z.literal('')),
  mobileNo: z.string().optional(),
  assignedTo: z.string(),
  source: z.string().optional(),
})

type FormValues = z.infer<typeof formSchema>

export function LeadsDialogs() {
  const { openDialog, setOpenDialog, selectedLeadIds, leads: contextLeads } = useLeadsContext()
  const [isOpen, setIsOpen] = useState(false)
  const [dialogType, setDialogType] = useState('')
  const [selectedLead, setSelectedLead] = useState<Lead | null>(null)

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      status: 'new',
      email: '',
      mobileNo: '',
      assignedTo: 'John Doe',
      source: '',
    },
  })

  useEffect(() => {
    setIsOpen(!!openDialog)
    setDialogType(openDialog)

    if ((openDialog === 'edit-lead' || openDialog === 'convert-lead') && selectedLeadIds.length > 0) {
      const foundLead = contextLeads.find((lead) => lead.id === selectedLeadIds[0])
      setSelectedLead(foundLead || null)

      if (foundLead && openDialog === 'edit-lead') {
        form.reset({
          name: foundLead.name,
          status: foundLead.status,
          email: foundLead.email || '',
          mobileNo: foundLead.mobileNo || '',
          assignedTo: foundLead.assignedTo || 'John Doe',
          source: foundLead.source || '',
        })
      }
    } else if (openDialog === 'add-lead') {
      setSelectedLead(null)
      form.reset({
        name: '',
        status: 'new',
        email: '',
        mobileNo: '',
        assignedTo: 'John Doe',
        source: '',
      })
    }
  }, [openDialog, selectedLeadIds, contextLeads, form])

  const onSubmit: SubmitHandler<FormValues> = (_values) => {
    setOpenDialog('')
  }

  const handleClose = () => {
    setOpenDialog('')
  }

  return (
    <Fragment>
      {/* Dialog thêm/sửa Lead */}
      <Dialog open={isOpen && ['add-lead', 'edit-lead'].includes(dialogType)} onOpenChange={handleClose}>
        <DialogContent className='sm:max-w-[425px]'>
          <DialogHeader>
            <DialogTitle>
              {dialogType === 'add-lead' ? 'Thêm Lead Mới' : 'Chỉnh Sửa Lead'}
            </DialogTitle>
            <DialogDescription>
              {dialogType === 'add-lead'
                ? 'Thêm thông tin lead mới vào hệ thống.'
                : 'Cập nhật thông tin lead.'}
            </DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
              <FormField
                control={form.control}
                name='name'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tên</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className='grid grid-cols-2 gap-4'>
                <FormField
                  control={form.control}
                  name='status'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Trạng thái</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Chọn trạng thái' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value='new'>New</SelectItem>
                          <SelectItem value='nurture'>Nurture</SelectItem>
                          <SelectItem value='contacted'>Contacted</SelectItem>
                          <SelectItem value='unqualified'>Unqualified</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name='source'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nguồn</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Chọn nguồn' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {sourceTypes.map((source) => (
                            <SelectItem key={source.value} value={source.value}>
                              {source.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <FormField
                control={form.control}
                name='email'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='mobileNo'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Số điện thoại</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='assignedTo'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Người phụ trách</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder='Chọn người phụ trách' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value='John Doe'>John Doe</SelectItem>
                        <SelectItem value='Ankush Menat'>Ankush Menat</SelectItem>
                        <SelectItem value='Adil Shaikh'>Adil Shaikh</SelectItem>
                        <SelectItem value='Suraj Shetty'>Suraj Shetty</SelectItem>
                        <SelectItem value='Faris Ansari'>Faris Ansari</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <DialogFooter>
                <Button type='submit'>Lưu</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Dialog xóa Lead */}
      <Dialog open={isOpen && dialogType === 'delete-lead'} onOpenChange={handleClose}>
        <DialogContent className='sm:max-w-[425px]'>
          <DialogHeader>
            <DialogTitle>Xóa Lead</DialogTitle>
            <DialogDescription>
              Bạn có chắc chắn muốn xóa lead này? Hành động này không thể hoàn tác.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant='outline' onClick={handleClose}>
              Hủy
            </Button>
            <Button variant='destructive' onClick={() => {
              setOpenDialog('')
            }}>
              Xóa
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Wizard chuyển đổi Lead thành Opportunity */}
      <ConvertLeadWizard
        open={isOpen && dialogType === 'convert-lead'}
        onOpenChange={handleClose}
        lead={selectedLead}
      />

      {/* Dialog nhập leads từ file */}
      <LeadsImportDialog
        open={isOpen && dialogType === 'import-leads'}
        onOpenChange={handleClose}
      />

      {/* Dialog xuất leads ra file */}
      <Dialog open={isOpen && dialogType === 'export-leads'} onOpenChange={handleClose}>
        <DialogContent className='sm:max-w-[425px]'>
          <DialogHeader>
            <DialogTitle>Xuất Leads</DialogTitle>
            <DialogDescription>
              Chọn định dạng file để xuất danh sách leads.
            </DialogDescription>
          </DialogHeader>
          <div className="flex flex-col gap-4 py-4">
            <div className="flex items-center gap-2">
              <Button variant="outline" className="w-full" onClick={() => {
                // Giả lập xuất file CSV
                setTimeout(() => {
                  setOpenDialog('')
                }, 500)
              }}>
                Xuất file CSV
              </Button>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" className="w-full" onClick={() => {
                // Giả lập xuất file Excel
                setTimeout(() => {
                  setOpenDialog('')
                }, 500)
              }}>
                Xuất file Excel
              </Button>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={handleClose}>
              Hủy
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Fragment>
  )
}