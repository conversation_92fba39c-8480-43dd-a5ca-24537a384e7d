import {
  <PERSON><PERSON><PERSON>tR<PERSON>,
  <PERSON><PERSON>,
  MoreHorizontal,
  Pen,
  Star,
  Tags,
  Trash,
} from 'lucide-react'
import { Row } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useLeadsContext } from '../context/use-leads-context'
import { Lead } from '../data/schema'

interface DataTableRowActionsProps<TData> {
  row: Row<TData>
}

export function DataTableRowActions<TData>({
  row,
}: DataTableRowActionsProps<TData>) {
  const lead = row.original as Lead
  const { setOpenDialog, setSelectedLeadIds } = useLeadsContext()

  const handleEditLead = () => {
    setSelectedLeadIds([lead.id])
    setOpenDialog('edit-lead')
  }

  const handleDeleteLead = () => {
    setSelectedLeadIds([lead.id])
    setOpenDialog('delete-lead')
  }

  const handleConvertToOpportunity = () => {
    setSelectedLeadIds([lead.id])
    setOpenDialog('convert-lead')
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant='ghost'
          className='flex h-8 w-8 p-0 data-[state=open]:bg-muted'
        >
          <MoreHorizontal className='h-4 w-4' />
          <span className='sr-only'>Mở menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end' className='w-[160px]'>
        <DropdownMenuItem onClick={handleEditLead}>
          <Pen className='mr-2 h-3.5 w-3.5 text-muted-foreground/70' />
          Chỉnh sửa
        </DropdownMenuItem>
        <DropdownMenuItem>
          <Copy className='mr-2 h-3.5 w-3.5 text-muted-foreground/70' />
          Sao chép
          <DropdownMenuShortcut>⌘C</DropdownMenuShortcut>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuSub>
          <DropdownMenuSubTrigger>
            <Tags className='mr-2 h-3.5 w-3.5 text-muted-foreground/70' />
            Đổi trạng thái
          </DropdownMenuSubTrigger>
          <DropdownMenuSubContent>
            <DropdownMenuRadioGroup value={lead.status}>
              <DropdownMenuRadioItem value='nurture'>
                Nurture
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem value='new'>
                New
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem value='contacted'>
                Contacted
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem value='unqualified'>
                Unqualified
              </DropdownMenuRadioItem>
            </DropdownMenuRadioGroup>
          </DropdownMenuSubContent>
        </DropdownMenuSub>
        <DropdownMenuSub>
          <DropdownMenuSubTrigger>
            <Star className='mr-2 h-3.5 w-3.5 text-muted-foreground/70' />
            Gán cho
          </DropdownMenuSubTrigger>
          <DropdownMenuSubContent>
            <DropdownMenuRadioGroup value={lead.assignedTo}>
              <DropdownMenuRadioItem value='John Doe'>
                John Doe
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem value='Ankush Menat'>
                Ankush Menat
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem value='Adil Shaikh'>
                Adil Shaikh
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem value='Suraj Shetty'>
                Suraj Shetty
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem value='Faris Ansari'>
                Faris Ansari
              </DropdownMenuRadioItem>
            </DropdownMenuRadioGroup>
          </DropdownMenuSubContent>
        </DropdownMenuSub>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleConvertToOpportunity}>
          <ArrowLeftRight className='mr-2 h-3.5 w-3.5 text-muted-foreground/70' />
          Chuyển thành Deal
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleDeleteLead} className='text-red-600'>
          <Trash className='mr-2 h-3.5 w-3.5 text-red-600' />
          Xóa
          <DropdownMenuShortcut>⌘⌫</DropdownMenuShortcut>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}