import { useState } from 'react'
import { Filter, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Separator } from '@/components/ui/separator'
import { useLeadsContext } from '../context/use-leads-context'
import { sourceTypes, leadScoreTypes, bantCriteria } from '../data/data'

export function FilterToggleButton() {
  const { showFilterPanel, setShowFilterPanel } = useLeadsContext()

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={() => setShowFilterPanel(!showFilterPanel)}
      className="h-9"
    >
      <Filter className="mr-2 h-4 w-4" />
      Bộ lọc
      {showFilterPanel && <X className="ml-2 h-4 w-4" />}
    </Button>
  )
}

export function FilterPanel() {
  const { showFilterPanel } = useLeadsContext()
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([])
  const [selectedSources, setSelectedSources] = useState<string[]>([])
  const [selectedOwners, setSelectedOwners] = useState<string[]>([])
  const [selectedScores, setSelectedScores] = useState<string[]>([])
  const [selectedCampaigns, setSelectedCampaigns] = useState<string[]>([])
  const [dateRange, setDateRange] = useState<string>('all')

  if (!showFilterPanel) return null

  return (
    <div className="w-full md:w-64 lg:w-72 flex-shrink-0 transition-all">
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base font-medium flex justify-between items-center">
            Bộ lọc nâng cao
            <Button
              variant="ghost"
              size="sm"
              className="h-8 px-2 text-xs"
              onClick={() => {
                setSelectedStatuses([])
                setSelectedSources([])
                setSelectedOwners([])
                setSelectedScores([])
                setSelectedCampaigns([])
                setDateRange('all')
              }}
            >
              Xóa tất cả
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Trạng thái */}
          <div className="space-y-2">
            <h4 className="font-medium text-sm">Trạng thái</h4>
            <div className="grid gap-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="status-open"
                  checked={selectedStatuses.includes('open')}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setSelectedStatuses([...selectedStatuses, 'open'])
                    } else {
                      setSelectedStatuses(selectedStatuses.filter(s => s !== 'open'))
                    }
                  }}
                />
                <Label htmlFor="status-open">Mới</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="status-contacted"
                  checked={selectedStatuses.includes('contacted')}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setSelectedStatuses([...selectedStatuses, 'contacted'])
                    } else {
                      setSelectedStatuses(selectedStatuses.filter(s => s !== 'contacted'))
                    }
                  }}
                />
                <Label htmlFor="status-contacted">Đã liên hệ</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="status-qualified"
                  checked={selectedStatuses.includes('qualified')}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setSelectedStatuses([...selectedStatuses, 'qualified'])
                    } else {
                      setSelectedStatuses(selectedStatuses.filter(s => s !== 'qualified'))
                    }
                  }}
                />
                <Label htmlFor="status-qualified">Đủ điều kiện</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="status-unqualified"
                  checked={selectedStatuses.includes('unqualified')}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setSelectedStatuses([...selectedStatuses, 'unqualified'])
                    } else {
                      setSelectedStatuses(selectedStatuses.filter(s => s !== 'unqualified'))
                    }
                  }}
                />
                <Label htmlFor="status-unqualified">Không phù hợp</Label>
              </div>
            </div>
          </div>

          <Separator />

          {/* Nguồn */}
          <div className="space-y-2">
            <h4 className="font-medium text-sm">Nguồn</h4>
            <div className="grid gap-2">
              {sourceTypes.map((source) => (
                <div key={source.value} className="flex items-center space-x-2">
                  <Checkbox
                    id={`source-${source.value}`}
                    checked={selectedSources.includes(source.value)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setSelectedSources([...selectedSources, source.value])
                      } else {
                        setSelectedSources(selectedSources.filter(s => s !== source.value))
                      }
                    }}
                  />
                  <Label htmlFor={`source-${source.value}`}>{source.label}</Label>
                </div>
              ))}
            </div>
          </div>

          <Separator />

          {/* Thời gian */}
          <div className="space-y-2">
            <h4 className="font-medium text-sm">Thời gian tạo</h4>
            <RadioGroup
              value={dateRange}
              onValueChange={setDateRange}
              className="grid gap-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="all" id="date-all" />
                <Label htmlFor="date-all">Tất cả thời gian</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="today" id="date-today" />
                <Label htmlFor="date-today">Hôm nay</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="yesterday" id="date-yesterday" />
                <Label htmlFor="date-yesterday">Hôm qua</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="week" id="date-week" />
                <Label htmlFor="date-week">Tuần này</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="month" id="date-month" />
                <Label htmlFor="date-month">Tháng này</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="custom" id="date-custom" />
                <Label htmlFor="date-custom">Tùy chỉnh</Label>
              </div>
            </RadioGroup>

            {dateRange === 'custom' && (
              <div className="grid gap-2 pt-2">
                <div className="grid gap-1.5">
                  <Label htmlFor="date-from">Từ ngày</Label>
                  <Input type="date" id="date-from" />
                </div>
                <div className="grid gap-1.5">
                  <Label htmlFor="date-to">Đến ngày</Label>
                  <Input type="date" id="date-to" />
                </div>
              </div>
            )}
          </div>

          <Separator />

          {/* Điểm số lead */}
          <div className="space-y-2">
            <h4 className="font-medium text-sm">Điểm số lead</h4>
            <div className="grid gap-2">
              {leadScoreTypes.map((score) => (
                <div key={score.value} className="flex items-center space-x-2">
                  <Checkbox
                    id={`score-${score.value}`}
                    checked={selectedScores.includes(score.value)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setSelectedScores([...selectedScores, score.value])
                      } else {
                        setSelectedScores(selectedScores.filter(s => s !== score.value))
                      }
                    }}
                  />
                  <Label htmlFor={`score-${score.value}`}>{score.label}</Label>
                </div>
              ))}
            </div>
          </div>

          <Separator />

          {/* Người phụ trách */}
          <div className="space-y-2">
            <h4 className="font-medium text-sm">Người phụ trách</h4>
            <div className="grid gap-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="owner-john"
                  checked={selectedOwners.includes('John Doe')}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setSelectedOwners([...selectedOwners, 'John Doe'])
                    } else {
                      setSelectedOwners(selectedOwners.filter(s => s !== 'John Doe'))
                    }
                  }}
                />
                <Label htmlFor="owner-john">John Doe</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="owner-ankush"
                  checked={selectedOwners.includes('Ankush Menat')}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setSelectedOwners([...selectedOwners, 'Ankush Menat'])
                    } else {
                      setSelectedOwners(selectedOwners.filter(s => s !== 'Ankush Menat'))
                    }
                  }}
                />
                <Label htmlFor="owner-ankush">Ankush Menat</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="owner-adil"
                  checked={selectedOwners.includes('Adil Shaikh')}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setSelectedOwners([...selectedOwners, 'Adil Shaikh'])
                    } else {
                      setSelectedOwners(selectedOwners.filter(s => s !== 'Adil Shaikh'))
                    }
                  }}
                />
                <Label htmlFor="owner-adil">Adil Shaikh</Label>
              </div>
            </div>
          </div>

          <div className="pt-2">
            <Button className="w-full">Áp dụng bộ lọc</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
