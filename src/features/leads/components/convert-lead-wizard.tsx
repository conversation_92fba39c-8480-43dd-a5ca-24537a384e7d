import { useState } from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import * as z from 'zod'
import { ArrowLeft, ArrowRight, Check, ChevronsUpDown, Loader2 } from 'lucide-react'
import { format } from 'date-fns'
import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { cn } from '@/lib/utils'
import { useLeadsContext } from '../context/use-leads-context'
import { Lead } from '../data/schema'

// Các giai đoạn của opportunity
const stages = [
  { label: 'Tiềm năng', value: 'prospect' },
  { label: 'Xác định nhu cầu', value: 'needs_analysis' },
  { label: 'Đề xuất', value: 'proposal' },
  { label: 'Đàm phán', value: 'negotiation' },
  { label: 'Đóng (Thành công)', value: 'closed_won' },
  { label: 'Đóng (Thất bại)', value: 'closed_lost' },
]

// Schema cho form
const formSchema = z.object({
  name: z.string().min(2, {
    message: 'Tên cơ hội phải có ít nhất 2 ký tự.',
  }),
  accountName: z.string().min(2, {
    message: 'Tên khách hàng phải có ít nhất 2 ký tự.',
  }),
  stage: z.string({
    required_error: 'Vui lòng chọn giai đoạn.',
  }),
  amount: z.coerce.number().min(0, {
    message: 'Giá trị phải lớn hơn hoặc bằng 0.',
  }).optional(),
  probability: z.coerce.number().min(0, {
    message: 'Xác suất phải từ 0 đến 100.',
  }).max(100, {
    message: 'Xác suất phải từ 0 đến 100.',
  }).optional(),
  expectedCloseDate: z.date({
    required_error: 'Vui lòng chọn ngày dự kiến đóng.',
  }),
  assignedTo: z.string().optional(),
  source: z.string().optional(),
  description: z.string().optional(),
})

type FormValues = z.infer<typeof formSchema>

interface ConvertLeadWizardProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  lead: Lead | null
}

export function ConvertLeadWizard({ open, onOpenChange, lead }: ConvertLeadWizardProps) {
  const [step, setStep] = useState(1)
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: lead ? `Cơ hội từ ${lead.name}` : '',
      accountName: lead?.name || '',
      stage: 'prospect',
      amount: undefined,
      probability: 20,
      expectedCloseDate: new Date(),
      assignedTo: lead?.assignedTo || '',
      source: lead?.source || '',
      description: '',
    },
  })
  
  const onSubmit = (values: FormValues) => {
    if (step < 3) {
      setStep(step + 1)
      return
    }
    
    setIsSubmitting(true)
    
    // Giả lập API call
    setTimeout(() => {
      console.log('Converted lead to opportunity:', values)
      setIsSubmitting(false)
      onOpenChange(false)
      setStep(1)
      form.reset()
    }, 1500)
  }
  
  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1)
    }
  }
  
  const handleClose = () => {
    onOpenChange(false)
    setStep(1)
    form.reset()
  }
  
  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Chuyển đổi Lead thành Opportunity</DialogTitle>
          <DialogDescription>
            Chuyển đổi lead thành cơ hội kinh doanh để tiếp tục quy trình bán hàng.
          </DialogDescription>
        </DialogHeader>
        
        <div className="relative">
          {/* Stepper */}
          <div className="mb-8 mt-2">
            <div className="flex justify-between">
              <div className="flex flex-col items-center">
                <div className={cn(
                  "w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",
                  step >= 1 ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground"
                )}>
                  {step > 1 ? <Check className="h-4 w-4" /> : "1"}
                </div>
                <span className="text-xs mt-1">Thông tin</span>
              </div>
              <div className="flex-1 flex items-center">
                <div className={cn(
                  "h-1 w-full",
                  step >= 2 ? "bg-primary" : "bg-muted"
                )} />
              </div>
              <div className="flex flex-col items-center">
                <div className={cn(
                  "w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",
                  step >= 2 ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground"
                )}>
                  {step > 2 ? <Check className="h-4 w-4" /> : "2"}
                </div>
                <span className="text-xs mt-1">Chi tiết</span>
              </div>
              <div className="flex-1 flex items-center">
                <div className={cn(
                  "h-1 w-full",
                  step >= 3 ? "bg-primary" : "bg-muted"
                )} />
              </div>
              <div className="flex flex-col items-center">
                <div className={cn(
                  "w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",
                  step >= 3 ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground"
                )}>
                  3
                </div>
                <span className="text-xs mt-1">Xác nhận</span>
              </div>
            </div>
          </div>
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              {/* Step 1: Thông tin cơ bản */}
              {step === 1 && (
                <>
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tên cơ hội</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormDescription>
                          Tên cơ hội kinh doanh sẽ được hiển thị trong danh sách.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="accountName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tên khách hàng</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="stage"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Giai đoạn</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Chọn giai đoạn" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {stages.map((stage) => (
                              <SelectItem key={stage.value} value={stage.value}>
                                {stage.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </>
              )}
              
              {/* Step 2: Chi tiết */}
              {step === 2 && (
                <>
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="amount"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Giá trị (VNĐ)</FormLabel>
                          <FormControl>
                            <Input type="number" {...field} value={field.value || ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="probability"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Xác suất (%)</FormLabel>
                          <FormControl>
                            <Input type="number" min={0} max={100} {...field} value={field.value || ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <FormField
                    control={form.control}
                    name="expectedCloseDate"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Ngày dự kiến đóng</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant={"outline"}
                                className={cn(
                                  "w-full pl-3 text-left font-normal",
                                  !field.value && "text-muted-foreground"
                                )}
                              >
                                {field.value ? (
                                  format(field.value, "dd/MM/yyyy")
                                ) : (
                                  <span>Chọn ngày</span>
                                )}
                                <ChevronsUpDown className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Mô tả</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Mô tả chi tiết về cơ hội kinh doanh này"
                            className="resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </>
              )}
              
              {/* Step 3: Xác nhận */}
              {step === 3 && (
                <div className="space-y-4">
                  <div className="rounded-md border p-4">
                    <div className="grid grid-cols-2 gap-y-2">
                      <div className="font-medium">Tên cơ hội:</div>
                      <div>{form.getValues("name")}</div>
                      
                      <div className="font-medium">Tên khách hàng:</div>
                      <div>{form.getValues("accountName")}</div>
                      
                      <div className="font-medium">Giai đoạn:</div>
                      <div>{stages.find(s => s.value === form.getValues("stage"))?.label}</div>
                      
                      <div className="font-medium">Giá trị:</div>
                      <div>{form.getValues("amount") ? `${form.getValues("amount").toLocaleString()} VNĐ` : "Chưa xác định"}</div>
                      
                      <div className="font-medium">Xác suất:</div>
                      <div>{form.getValues("probability")}%</div>
                      
                      <div className="font-medium">Ngày dự kiến đóng:</div>
                      <div>{format(form.getValues("expectedCloseDate"), "dd/MM/yyyy")}</div>
                      
                      <div className="font-medium">Người phụ trách:</div>
                      <div>{form.getValues("assignedTo") || "Chưa phân công"}</div>
                      
                      <div className="font-medium">Nguồn:</div>
                      <div>{form.getValues("source") || "Không xác định"}</div>
                    </div>
                    
                    {form.getValues("description") && (
                      <>
                        <div className="font-medium mt-2">Mô tả:</div>
                        <div className="mt-1">{form.getValues("description")}</div>
                      </>
                    )}
                  </div>
                  
                  <div className="text-center text-sm text-muted-foreground">
                    Vui lòng kiểm tra lại thông tin trước khi xác nhận chuyển đổi.
                  </div>
                </div>
              )}
              
              <DialogFooter className="flex justify-between">
                {step > 1 ? (
                  <Button type="button" variant="outline" onClick={handleBack}>
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Quay lại
                  </Button>
                ) : (
                  <Button type="button" variant="outline" onClick={handleClose}>
                    Hủy
                  </Button>
                )}
                
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Đang xử lý
                    </>
                  ) : step < 3 ? (
                    <>
                      Tiếp tục
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </>
                  ) : (
                    "Hoàn tất chuyển đổi"
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  )
}
