import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useState } from 'react'
import { IconUpload, IconFile, IconX } from '@tabler/icons-react'
import { showSubmittedData } from '@/utils/show-submitted-data'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { useLeadsContext } from '../context/use-leads-context'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { <PERSON><PERSON>, AlertDescription } from '@/components/ui/alert'

const formSchema = z.object({
  file: z
    .instanceof(FileList)
    .refine((files) => files.length > 0, {
      message: 'Vui lòng tải lên một tệp',
    })
    .refine(
      (files) => ['text/csv', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'].includes(files?.[0]?.type),
      'Vui lòng tải lên tệp định dạng CSV hoặc Excel (.csv, .xls, .xlsx)'
    ),
})

interface LeadsImportDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function LeadsImportDialog({ open, onOpenChange }: LeadsImportDialogProps) {
  const [activeTab, setActiveTab] = useState('upload')
  const [uploadProgress, setUploadProgress] = useState(0)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadedFileName, setUploadedFileName] = useState('')
  const { setLeads } = useLeadsContext()

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: { file: undefined },
  })

  const fileRef = form.register('file')

  const onSubmit = () => {
    const file = form.getValues('file')

    if (file && file[0]) {
      setIsUploading(true)
      setUploadedFileName(file[0].name)
      
      // Giả lập quá trình tải lên
      let progress = 0
      const interval = setInterval(() => {
        progress += 10
        setUploadProgress(progress)
        
        if (progress >= 100) {
          clearInterval(interval)
          setIsUploading(false)
          
          const fileDetails = {
            name: file[0].name,
            size: `${(file[0].size / 1024).toFixed(2)} KB`,
            type: file[0].type,
            status: 'Đã tải lên thành công',
            leadsCount: Math.floor(Math.random() * 50) + 10, // Giả lập số lượng leads
          }
          
          showSubmittedData(fileDetails, 'Bạn đã nhập dữ liệu từ tệp:')
          setActiveTab('mapping')
        }
      }, 300)
    }
  }

  const handleFinish = () => {
    // Giả lập việc thêm leads mới
    showSubmittedData({
      message: 'Đã nhập thành công dữ liệu leads từ tệp',
      fileName: uploadedFileName,
      leadsAdded: Math.floor(Math.random() * 50) + 10
    })
    
    onOpenChange(false)
    form.reset()
    setUploadProgress(0)
    setActiveTab('upload')
  }

  const handleCancel = () => {
    onOpenChange(false)
    form.reset()
    setUploadProgress(0)
    setActiveTab('upload')
  }

  return (
    <Dialog
      open={open}
      onOpenChange={(val) => {
        onOpenChange(val)
        if (!val) {
          form.reset()
          setUploadProgress(0)
          setActiveTab('upload')
        }
      }}
    >
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Nhập Leads từ tệp</DialogTitle>
          <DialogDescription>
            Tải lên tệp CSV hoặc Excel chứa danh sách leads để nhập vào hệ thống.
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="upload" disabled={isUploading}>Tải lên</TabsTrigger>
            <TabsTrigger value="mapping" disabled={!uploadedFileName}>Ánh xạ trường</TabsTrigger>
            <TabsTrigger value="preview" disabled={!uploadedFileName}>Xem trước</TabsTrigger>
          </TabsList>

          <TabsContent value="upload" className="space-y-4 py-4">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="file"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tệp CSV/Excel</FormLabel>
                      <FormControl>
                        <div className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-lg p-6 cursor-pointer hover:border-primary/50 transition-colors">
                          {!uploadedFileName ? (
                            <>
                              <IconUpload className="h-10 w-10 text-muted-foreground mb-2" />
                              <p className="text-sm text-muted-foreground mb-1">Kéo thả tệp vào đây hoặc click để chọn</p>
                              <p className="text-xs text-muted-foreground">Hỗ trợ: .CSV, .XLS, .XLSX</p>
                              <Input
                                type="file"
                                {...fileRef}
                                className="hidden"
                                accept=".csv,.xls,.xlsx,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/csv"
                              />
                            </>
                          ) : (
                            <div className="flex items-center gap-2 w-full">
                              <IconFile className="h-8 w-8 text-primary" />
                              <div className="flex-1">
                                <p className="text-sm font-medium truncate">{uploadedFileName}</p>
                                {isUploading && (
                                  <Progress value={uploadProgress} className="h-2 mt-1" />
                                )}
                              </div>
                              {!isUploading && (
                                <Button 
                                  type="button" 
                                  variant="ghost" 
                                  size="sm"
                                  onClick={() => {
                                    form.reset()
                                    setUploadedFileName('')
                                  }}
                                >
                                  <IconX className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          )}
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Alert>
                  <AlertDescription>
                    Tệp CSV/Excel cần có các cột: Tên, Email, Số điện thoại, Công ty, Nguồn.
                  </AlertDescription>
                </Alert>

                <div className="flex justify-end gap-2">
                  <Button type="button" variant="outline" onClick={handleCancel}>
                    Hủy
                  </Button>
                  <Button type="submit" disabled={isUploading || !form.formState.isDirty}>
                    {isUploading ? 'Đang tải lên...' : 'Tiếp tục'}
                  </Button>
                </div>
              </form>
            </Form>
          </TabsContent>

          <TabsContent value="mapping" className="space-y-4 py-4">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Ánh xạ trường dữ liệu</h3>
              <p className="text-sm text-muted-foreground">Chọn cách ánh xạ các cột trong tệp với các trường trong hệ thống.</p>
              
              <Separator />
              
              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Cột trong tệp</label>
                    <p className="text-sm text-muted-foreground">Name</p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Trường trong hệ thống</label>
                    <p className="text-sm">Tên</p>
                  </div>
                </div>
                
                <Separator />
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">Email</p>
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm">Email</p>
                  </div>
                </div>
                
                <Separator />
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">Phone</p>
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm">Số điện thoại</p>
                  </div>
                </div>
                
                <Separator />
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">Company</p>
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm">Công ty</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="flex justify-end gap-2 pt-4">
              <Button variant="outline" onClick={() => setActiveTab('upload')}>
                Quay lại
              </Button>
              <Button onClick={() => setActiveTab('preview')}>
                Tiếp tục
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="preview" className="space-y-4 py-4">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Xem trước dữ liệu</h3>
              <p className="text-sm text-muted-foreground">Kiểm tra dữ liệu trước khi nhập vào hệ thống.</p>
              
              <div className="border rounded-md overflow-hidden">
                <table className="w-full">
                  <thead>
                    <tr className="bg-muted/50">
                      <th className="text-left p-2 text-sm font-medium">Tên</th>
                      <th className="text-left p-2 text-sm font-medium">Email</th>
                      <th className="text-left p-2 text-sm font-medium">Số điện thoại</th>
                      <th className="text-left p-2 text-sm font-medium">Công ty</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-t">
                      <td className="p-2 text-sm">Trần Văn B</td>
                      <td className="p-2 text-sm"><EMAIL></td>
                      <td className="p-2 text-sm">0912345678</td>
                      <td className="p-2 text-sm">Công ty XYZ</td>
                    </tr>
                    <tr className="border-t">
                      <td className="p-2 text-sm">Lê Thị C</td>
                      <td className="p-2 text-sm"><EMAIL></td>
                      <td className="p-2 text-sm">0987123456</td>
                      <td className="p-2 text-sm">Công ty DEF</td>
                    </tr>
                    <tr className="border-t">
                      <td className="p-2 text-sm">Phạm Văn D</td>
                      <td className="p-2 text-sm"><EMAIL></td>
                      <td className="p-2 text-sm">0909123456</td>
                      <td className="p-2 text-sm">Công ty GHI</td>
                    </tr>
                  </tbody>
                </table>
              </div>
              
              <Alert>
                <AlertDescription>
                  Tổng cộng: 15 leads sẽ được nhập vào hệ thống.
                </AlertDescription>
              </Alert>
            </div>
            
            <div className="flex justify-end gap-2 pt-4">
              <Button variant="outline" onClick={() => setActiveTab('mapping')}>
                Quay lại
              </Button>
              <Button onClick={handleFinish}>
                Hoàn tất nhập dữ liệu
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}
