import { DataTable } from '@/components/ui/data-table'
import { Lead } from '../data/schema'
import { statusTypes, sourceTypes } from '../data/data'
import { ColumnDef, RowData } from '@tanstack/react-table'

declare module '@tanstack/react-table' {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  interface ColumnMeta<TData extends RowData, TValue> {
    className: string
  }
}

interface LeadsTableProps {
  columns: ColumnDef<Lead>[]
  data: Lead[]
}

export function LeadsTable({ columns, data }: LeadsTableProps) {
  const filters = [
    {
      column: 'status',
      title: 'Trạng thái',
      options: statusTypes.map((status) => ({
        label: status.label,
        value: status.value,
      })),
    },
    {
      column: 'source',
      title: 'Nguồn',
      options: sourceTypes.map((source) => ({
        label: source.label,
        value: source.value,
      })),
    },
  ]

  return (
    <DataTable
      columns={columns}
      data={data}
      searchKey="name"
      searchPlaceholder="Tìm kiếm lead..."
      filters={filters}
    />
  )
}