import { useState } from 'react'
import { LayoutGrid, LayoutList, ListFilter } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { LeadsTable } from './leads-table'
import { KanbanBoard } from './kanban-board'
import { Lead } from '../data/schema'
import { columns } from './leads-columns'
import { FilterPanel } from './filter-panel'

interface LeadsViewProps {
  data: Lead[]
}

export function LeadsView({ data }: LeadsViewProps) {
  const [viewMode, setViewMode] = useState<'table' | 'kanban'>('table')
  const [showFilters, setShowFilters] = useState(false)

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <Tabs 
          defaultValue="table" 
          value={viewMode}
          onValueChange={(value) => setViewMode(value as 'table' | 'kanban')}
          className="w-full sm:w-auto"
        >
          <TabsList className="grid grid-cols-2 w-full sm:w-auto">
            <TabsTrigger value="table" className="flex items-center gap-2">
              <LayoutList className="h-4 w-4" />
              <span className="hidden sm:inline">Bảng</span>
            </TabsTrigger>
            <TabsTrigger value="kanban" className="flex items-center gap-2">
              <LayoutGrid className="h-4 w-4" />
              <span className="hidden sm:inline">Kanban</span>
            </TabsTrigger>
          </TabsList>
        </Tabs>

        <Button 
          variant="outline" 
          size="sm" 
          className="flex items-center gap-2"
          onClick={() => setShowFilters(!showFilters)}
        >
          <ListFilter className="h-4 w-4" />
          Bộ lọc
          {showFilters ? ' (Đang mở)' : ''}
        </Button>
      </div>

      {showFilters && (
        <FilterPanel />
      )}

      <div>
        {viewMode === 'table' && (
          <LeadsTable columns={columns} data={data} />
        )}
        
        {viewMode === 'kanban' && (
          <KanbanBoard />
        )}
      </div>
    </div>
  )
}
