import React from 'react'
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { StatusBadge } from '@/components/ui/status-badge'
import { cn } from '@/lib/utils'
import { useLeadsContext } from '../context/use-leads-context'
import { Lead } from '../data/schema'

// Định nghĩa các trạng thái cho Kanban
const leadStatuses = [
  {
    value: 'open',
    label: 'Mới',
  },
  {
    value: 'contacted',
    label: 'Đã liên hệ',
  },
  {
    value: 'qualified',
    label: 'Đủ điều kiện',
  },
  {
    value: 'unqualified',
    label: 'Không phù hợp',
  },
]

// Helper function để sắp xếp lại các lead trong cùng một cột
const reorder = (list: Lead[], startIndex: number, endIndex: number): Lead[] => {
  const result = Array.from(list)
  const [removed] = result.splice(startIndex, 1)
  result.splice(endIndex, 0, removed)
  return result
}

// Helper function để di chuyển lead giữa các cột
const move = (
  source: Lead[],
  destination: Lead[],
  droppableSource: { index: number; droppableId: string },
  droppableDestination: { index: number; droppableId: string }
): { [key: string]: Lead[] } => {
  const sourceClone = Array.from(source)
  const destClone = Array.from(destination)
  const [removed] = sourceClone.splice(droppableSource.index, 1)

  // Tạo lead mới với trạng thái đã cập nhật
  const updatedLead = {
    ...removed,
    status: droppableDestination.droppableId
  }

  destClone.splice(droppableDestination.index, 0, updatedLead)

  const result: { [key: string]: Lead[] } = {}
  result[droppableSource.droppableId] = sourceClone
  result[droppableDestination.droppableId] = destClone

  return result
}

// Hàm lấy chữ cái đầu của tên
const getInitials = (name: string) => {
  return name
    .split(' ')
    .map(part => part[0])
    .join('')
    .toUpperCase()
    .substring(0, 2)
}

// Component hiển thị card lead
const LeadCard = ({ lead }: { lead: Lead }) => {
  return (
    <div className="flex-1 bg-card rounded-md p-3 shadow-sm border border-border hover:border-border/80 transition-colors">
      <div className="space-y-2.5">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Avatar className="h-6 w-6">
              <AvatarImage src={lead.avatar} alt={lead.name} />
              <AvatarFallback className="bg-primary/10 text-primary text-xs">
                {getInitials(lead.name)}
              </AvatarFallback>
            </Avatar>
            <h4 className="text-sm font-medium text-foreground line-clamp-1">
              {lead.name}
            </h4>
          </div>
          <StatusBadge
            status={lead.status}
            variant={
              lead.status === 'open' ? 'done' :
              lead.status === 'contacted' || lead.status === 'qualified' ? 'inProgress' :
              'default'
            }
            className="h-5 px-1.5 text-[10px]"
          />
        </div>

        <div className="grid grid-cols-2 gap-y-1 gap-x-2 text-xs text-muted-foreground">
          {lead.mobileNo && (
            <div className="truncate">
              {lead.mobileNo}
            </div>
          )}
          {lead.source && (
            <div className="truncate capitalize">
              {lead.source}
            </div>
          )}
        </div>

        {lead.assignedTo && (
          <div className="flex items-center justify-between pt-1">
            <Badge variant="outline" className="text-[10px] font-normal px-1.5 py-0 h-5">
              {lead.assignedTo}
            </Badge>
            <div className="text-[10px] text-muted-foreground">
              {lead.lastModified}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

// Component chính KanbanBoard
export function KanbanBoard() {
  const { leads, setLeads, updateLeadStatus } = useLeadsContext()

  // Nhóm leads theo trạng thái
  const leadsByStatus = leadStatuses.reduce<Record<string, Lead[]>>((acc, status) => {
    acc[status.value] = leads.filter(lead => lead.status === status.value)
    return acc
  }, {})

  // Xử lý khi kéo thả kết thúc
  const onDragEnd = (result: DropResult) => {
    const { source, destination } = result

    // Nếu thả ra ngoài vùng cho phép
    if (!destination) {
      return
    }

    // Nếu vị trí không thay đổi
    if (
      source.droppableId === destination.droppableId &&
      source.index === destination.index
    ) {
      return
    }

    // Di chuyển trong cùng một cột
    if (source.droppableId === destination.droppableId) {
      const reorderedLeads = reorder(
        leadsByStatus[source.droppableId],
        source.index,
        destination.index
      )

      // Cập nhật state
      const newLeadsByStatus = {
        ...leadsByStatus,
        [source.droppableId]: reorderedLeads
      }

      // Làm phẳng và cập nhật leads
      const updatedLeads = Object.values(newLeadsByStatus).flat()
      setLeads(updatedLeads)
    }
    // Di chuyển giữa các cột
    else {
      const result = move(
        leadsByStatus[source.droppableId],
        leadsByStatus[destination.droppableId],
        source,
        destination
      )

      // Cập nhật state
      const newLeadsByStatus = {
        ...leadsByStatus,
        [source.droppableId]: result[source.droppableId],
        [destination.droppableId]: result[destination.droppableId]
      }

      // Làm phẳng và cập nhật leads
      const updatedLeads = Object.values(newLeadsByStatus).flat()
      setLeads(updatedLeads)

      // Cập nhật trạng thái của lead cụ thể
      const leadId = leadsByStatus[source.droppableId][source.index].id
      updateLeadStatus(leadId, destination.droppableId)
    }
  }

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <div className="flex overflow-x-auto pb-4 gap-6 h-[calc(100vh-8rem)] -mx-4 px-4">
        {leadStatuses.map(status => (
          <div key={status.value} className="flex-none w-[350px] h-full">
            <div className="bg-background rounded-lg p-4 border border-border h-full flex flex-col">
              {/* Column header */}
              <div className="mb-3 flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="flex items-center space-x-1">
                    <h3 className="font-medium text-sm">{status.label}</h3>
                  </div>
                  <div className="rounded-md bg-muted px-1.5 py-0.5 text-xs text-muted-foreground">
                    {leadsByStatus[status.value]?.length || 0}
                  </div>
                </div>
              </div>

              {/* Droppable area for leads */}
              <Droppable droppableId={status.value}>
                {(provided, snapshot) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    className={cn(
                      "flex-1 flex flex-col gap-2 p-1 overflow-y-auto",
                      snapshot.isDraggingOver && "bg-primary/5 rounded-md"
                    )}
                    style={{
                      // Custom scrollbar styling
                      scrollbarWidth: 'thin',
                      scrollbarColor: 'hsl(var(--muted-foreground)) transparent',
                    }}
                  >
                    {leadsByStatus[status.value]?.map((lead, index) => (
                      <Draggable key={lead.id} draggableId={lead.id} index={index}>
                        {(provided, snapshot) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            className={cn(
                              "flex items-start group",
                              snapshot.isDragging && "opacity-80"
                            )}
                            style={{
                              ...provided.draggableProps.style,
                            }}
                          >
                            <LeadCard lead={lead} />
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </div>
          </div>
        ))}
      </div>
    </DragDropContext>
  )
}
