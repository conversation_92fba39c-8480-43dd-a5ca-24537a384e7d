import { PlusIcon } from 'lucide-react'
import { IconFileImport } from '@tabler/icons-react'
import { PrimaryButtonsGroup } from '@/components/ui/primary-buttons-group'
import { useLeadsContext } from '../context/use-leads-context'

export function LeadsPrimaryButtons() {
  const { setOpenDialog } = useLeadsContext()

  const buttons = [
    {
      label: 'Thêm Lead',
      icon: PlusIcon,
      onClick: () => setOpenDialog('add-lead'),
    },
    {
      label: 'Nhập từ file',
      icon: IconFileImport,
      onClick: () => setOpenDialog('import-leads'),
      variant: 'outline' as const,
    },
  ]

  return <PrimaryButtonsGroup buttons={buttons} />
}