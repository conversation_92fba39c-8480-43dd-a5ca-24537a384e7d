// Import will be used when we implement routing
import { Link } from '@tanstack/react-router'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'

type NameLinkProps = {
  id: string // Will be used for routing in the future
  name: string
  avatar?: string // Thêm prop avatar (optional)
}

export function NameLink({ id, name, avatar }: NameLinkProps) {
  return (
    <div className="flex items-center gap-2">
      <Avatar className="h-8 w-8">
        <AvatarImage src={avatar} alt={name} />
        <AvatarFallback>{name.charAt(0).toUpperCase()}</AvatarFallback>
      </Avatar>
      <Link
        to="/leads/$id"
        params={{ id }}
        className="hover:text-foreground hover:underline cursor-pointer font-medium"
      >
        {name}
      </Link>
    </div>
  )
} 