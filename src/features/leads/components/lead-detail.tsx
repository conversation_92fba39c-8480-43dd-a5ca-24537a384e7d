import { useEffect } from "react";
import { useState } from 'react'
import { <PERSON> } from '@tanstack/react-router'
import { toast } from 'sonner'
import { useLeadsContext } from '../context/use-leads-context'
import {
  Mail,
  Pencil,
  Calendar,
  Phone,
  MessageSquare,
  Globe,
  Building,
  MapPin,
  Briefcase,
  Info,
  Plus,
  Clock,
  CheckCircle2,
  Star,
  MoreVertical,
  ArrowLeftRight,
  Activity,
  FileText,
  CheckCircle,
  ListTodo,
  Users,
  Send,
  X,
  Bold,
  Italic,
  List,
  Trash
} from 'lucide-react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui/tooltip'
import { SelectDropdown } from '@/components/select-dropdown'
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card'
import { Breadcrumb, BreadcrumbList, BreadcrumbItem, BreadcrumbLink, BreadcrumbSeparator, BreadcrumbPage } from '@/components/ui/breadcrumb'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { type Lead } from '../data/schema'

interface LeadDetailProps {
  lead: Lead
}

function LeadDetail({ lead }: LeadDetailProps) {
  // Move all hooks to the top - BEFORE any early returns
  const { setOpenDialog, setSelectedLeadIds, updateLeadStatus } = useLeadsContext()
  const [activeTab, setActiveTab] = useState('overview')
  
  // Patch optional fields with defaults to prevent runtime errors
  const safeLead = {
    ...lead,
    status: typeof lead.status === 'string' && lead.status.length > 0 ? lead.status : 'new',
    tags: Array.isArray(lead.tags) ? lead.tags : [],
    scoreBasedOn: Array.isArray(lead.scoreBasedOn) ? lead.scoreBasedOn : [],
    notes: typeof lead.notes === 'string' ? lead.notes : '',
  }
  const [currentLead, setCurrentLead] = useState(safeLead)
  // State cho favorite, loading và dialogs
  const [isFavorite, setIsFavorite] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [showCreateTaskDialog, setShowCreateTaskDialog] = useState(false)
  const [showSendMessageDialog, setShowSendMessageDialog] = useState(false)
  const [showChangeOwnerDialog, setShowChangeOwnerDialog] = useState(false)

  // DEBUG LOG: Log lead prop and context
  useEffect(() => {
    // eslint-disable-next-line no-console
    console.log("LeadDetail received lead prop:", lead);
  }, [lead]);

  // Guard against missing or invalid lead data - AFTER all hooks
  if (!lead || typeof lead !== 'object' || !lead.id) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[300px]">
        <h2 className="text-xl font-semibold mb-2">Không tìm thấy thông tin Lead</h2>
        <p className="text-muted-foreground">Vui lòng kiểm tra lại hoặc thử tải lại trang.</p>
      </div>
    )
  }

  const handleConvertToDeal = async () => {
    try {
      setIsLoading(true)
      setSelectedLeadIds([currentLead.id])
      setOpenDialog('convert-lead')
      toast.success('Mở wizard chuyển đổi lead thành deal')
    } catch (_error) {
      toast.error('Có lỗi xảy ra khi chuyển đổi lead')
    } finally {
      setIsLoading(false)
    }
  }

  const handleStatusChange = (newStatus: string) => {
    const updatedLead = { ...currentLead, status: newStatus }
    setCurrentLead(updatedLead)
    
    // Update lead in context/store
    updateLeadStatus(currentLead.id, newStatus)
    
    // Show success toast
    toast.success(`Đã cập nhật trạng thái thành "${newStatus}"`)
  }

  // Quick action handlers
  const handleQuickAction = (action: string, lead: Lead) => {
    switch (action) {
      case 'call':
        if (lead.mobileNo) {
          window.open(`tel:${lead.mobileNo}`, '_self')
          toast.success(`Đang gọi ${lead.name} (${lead.mobileNo})`)
        } else {
          toast.error('Không có số điện thoại để gọi')
        }
        break
      case 'email':
        if (lead.email) {
          window.open(`mailto:${lead.email}?subject=Liên hệ về Lead CRM&body=Xin chào ${lead.name},`, '_blank')
          toast.success(`Đã mở email để gửi cho ${lead.name}`)
        } else {
          toast.error('Không có email để gửi')
        }
        break
      case 'message':
        if (lead.omnichannelId) {
          // Navigate to chat with this lead
          toast.info(`Mở chat với ${lead.name} (ID: ${lead.omnichannelId})`)
        } else {
          toast.error('Lead này chưa có kênh chat')
        }
        break
      case 'schedule':
        // Open calendar scheduling
        toast.info(`Đặt lịch hẹn với ${lead.name}`)
        break
      default:
        break
    }
  }

  // More actions handler
  const handleMoreAction = (action: string) => {
    switch (action) {
      case 'edit':
        setSelectedLeadIds([currentLead.id])
        setOpenDialog('edit-lead')
        toast.info('Mở dialog chỉnh sửa lead')
        break
      case 'changeOwner':
        setShowChangeOwnerDialog(true)
        break
      case 'markUnqualified':
        if (window.confirm(`Bạn có chắc chắn muốn đánh dấu "${currentLead.name}" là không phù hợp?`)) {
          handleStatusChange('unqualified')
        }
        break
      default:
        break
    }
  }

  // Tạo avatar fallback từ tên
  const getInitials = (name: string) => {
    if (!name) return "NA"
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
  }

  const getStatusBadge = (status: string) => {
    const statusMap: Record<string, { color: string, icon: React.ReactNode, bg: string }> = {
      'open': {
        color: 'text-emerald-700',
        bg: 'bg-emerald-50',
        icon: <CheckCircle2 className="h-3.5 w-3.5 mr-1" />
      },
      'contacted': {
        color: 'text-blue-700',
        bg: 'bg-blue-50',
        icon: <MessageSquare className="h-3.5 w-3.5 mr-1" />
      },
      'qualified': {
        color: 'text-amber-700',
        bg: 'bg-amber-50',
        icon: <CheckCircle className="h-3.5 w-3.5 mr-1" />
      },
      'unqualified': {
        color: 'text-rose-700',
        bg: 'bg-rose-50',
        icon: <Info className="h-3.5 w-3.5 mr-1" />
      },
      // Giữ lại các trạng thái cũ để tương thích ngược
      'new': {
        color: 'text-emerald-700',
        bg: 'bg-emerald-50',
        icon: <CheckCircle2 className="h-3.5 w-3.5 mr-1" />
      },
      'contacting': {
        color: 'text-blue-700',
        bg: 'bg-blue-50',
        icon: <MessageSquare className="h-3.5 w-3.5 mr-1" />
      },
      'nurture': {
        color: 'text-amber-700',
        bg: 'bg-amber-50',
        icon: <Clock className="h-3.5 w-3.5 mr-1" />
      },
      'success': {
        color: 'text-emerald-700',
        bg: 'bg-emerald-50',
        icon: <CheckCircle className="h-3.5 w-3.5 mr-1" />
      },
      'failed': {
        color: 'text-rose-700',
        bg: 'bg-rose-50',
        icon: <Info className="h-3.5 w-3.5 mr-1" />
      }
    }

    if (!status) return null

    const lowerStatus = status.toLowerCase()
    const { color, icon, bg } = statusMap[lowerStatus] || {
      color: 'text-gray-700',
      bg: 'bg-gray-50',
      icon: null
    }

    return (
      <Badge variant="outline" className={`flex items-center gap-1 px-2 py-1 ${bg} ${color} border-transparent`}>
        {icon}
        <span className="capitalize font-medium">{status}</span>
      </Badge>
    )
  }

  const getSourceBadge = (source: string | undefined) => {
    if (!source) return null

    const sourceMap: Record<string, { icon: React.ReactNode, color: string, bg: string }> = {
      'website': {
        icon: <Globe className="h-3 w-3 mr-1" />,
        color: 'text-indigo-700',
        bg: 'bg-indigo-50'
      },
      'excel': {
        icon: <FileText className="h-3 w-3 mr-1" />,
        color: 'text-green-700',
        bg: 'bg-green-50'
      },
      'csv': {
        icon: <FileText className="h-3 w-3 mr-1" />,
        color: 'text-orange-700',
        bg: 'bg-orange-50'
      },
      'webform': {
        icon: <Globe className="h-3 w-3 mr-1" />,
        color: 'text-violet-700',
        bg: 'bg-violet-50'
      },
      'campaign': {
        icon: <Send className="h-3 w-3 mr-1" />,
        color: 'text-cyan-700',
        bg: 'bg-cyan-50'
      },
      'referral': {
        icon: <Users className="h-3 w-3 mr-1" />,
        color: 'text-teal-700',
        bg: 'bg-teal-50'
      },
      'facebook': {
        icon: <MessageSquare className="h-3 w-3 mr-1" />,
        color: 'text-blue-700',
        bg: 'bg-blue-50'
      },
      'linkedin': {
        icon: <Briefcase className="h-3 w-3 mr-1" />,
        color: 'text-sky-700',
        bg: 'bg-sky-50'
      },
      'google': {
        icon: <Globe className="h-3 w-3 mr-1" />,
        color: 'text-emerald-700',
        bg: 'bg-emerald-50'
      },
      'messenger': {
        icon: <MessageSquare className="h-3 w-3 mr-1" />,
        color: 'text-blue-700',
        bg: 'bg-blue-50'
      },
      'zalo': {
        icon: <MessageSquare className="h-3 w-3 mr-1" />,
        color: 'text-blue-700',
        bg: 'bg-blue-50'
      },
      'event': {
        icon: <Calendar className="h-3 w-3 mr-1" />,
        color: 'text-purple-700',
        bg: 'bg-purple-50'
      },
      'other': {
        icon: <Globe className="h-3 w-3 mr-1" />,
        color: 'text-gray-700',
        bg: 'bg-gray-50'
      }
    }

    const { icon, color, bg } = sourceMap[source.toLowerCase()] || sourceMap['others']

    return (
      <Badge variant="outline" className={`flex items-center gap-1 px-2 py-1 ${bg} ${color} border-transparent`}>
        {icon}
        <span className="capitalize font-medium">{source}</span>
      </Badge>
    )
  }

  return (
    <>
      <Header fixed>
        <Breadcrumb>
          <BreadcrumbList className="mb-0">
            <BreadcrumbItem>
              <BreadcrumbLink asChild><Link to="/" className="text-sm">Leads</Link></BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-sm">{currentLead.name}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </Header>
      <Main>
        <div className="container py-6 px-4 max-w-7xl mx-auto">
          {/* Breadcrumb & Header */}
          <div className="mb-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="flex items-center gap-3">
                <Avatar className="h-12 w-12 border-2 border-background shadow-sm">
                  <AvatarFallback className="bg-primary/90 text-white">
                    {getInitials(currentLead.name)}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <div className="flex items-center gap-2 flex-wrap">
                    <h1 className="text-2xl font-bold tracking-tight">{currentLead.name}</h1>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button 
                            variant="ghost" 
                            size="icon" 
                            className="h-8 w-8 rounded-full"
                            onClick={() => setIsFavorite(!isFavorite)}
                          >
                            <Star 
                              className={`h-[18px] w-[18px] ${
                                isFavorite 
                                  ? 'text-yellow-500 fill-yellow-500' 
                                  : 'text-muted-foreground'
                              }`} 
                            />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          {isFavorite ? 'Bỏ đánh dấu quan trọng' : 'Đánh dấu quan trọng'}
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <div className="flex items-center gap-2 mt-1">
                    {getStatusBadge(currentLead.status)}
                    {getSourceBadge(currentLead.source)}
                    <Badge variant="outline" className="flex items-center gap-1 px-2 py-1 bg-gray-50 text-gray-700 border-transparent">
                      <Calendar className="h-3.5 w-3.5 mr-1" />
                      <span>{currentLead.lastModified}</span>
                    </Badge>
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <HoverCard>
                  <HoverCardTrigger asChild>
                    <Button variant="outline" className="gap-1.5 h-10">
                      <Avatar className="h-5 w-5">
                        <AvatarFallback className="text-[10px] bg-primary/90 text-white">
                          {currentLead.leadOwner?.substring(0, 2) || 'JD'}
                        </AvatarFallback>
                      </Avatar>
                      <span>{currentLead.leadOwner || 'Chưa phân công'}</span>
                    </Button>
                  </HoverCardTrigger>
                  <HoverCardContent className="w-64">
                    <div className="flex justify-between space-x-4">
                      <Avatar>
                        <AvatarFallback className="bg-primary/90 text-white">
                          {lead.leadOwner?.substring(0, 2) || 'JD'}
                        </AvatarFallback>
                      </Avatar>
                      <div className="space-y-1">
                        <h4 className="text-sm font-semibold">{lead.leadOwner || 'Chưa phân công'}</h4>
                        <p className="text-sm text-muted-foreground">Người phụ trách lead</p>
                        <div className="flex items-center pt-1">
                          <Button variant="link" size="sm" className="px-0 h-auto text-xs">
                            Đổi người phụ trách
                          </Button>
                        </div>
                      </div>
                    </div>
                  </HoverCardContent>
                </HoverCard>
                
                
                
                {/* Status Dropdown */}
                <div className="flex items-center">
                  <SelectDropdown
                    items={[
                      { label: 'Mới', value: 'new' },
                      { label: 'Đang liên hệ', value: 'contacting' },
                      { label: 'Đã liên hệ', value: 'contacted' },
                      { label: 'Đủ điều kiện', value: 'qualified' },
                      { label: 'Không đủ điều kiện', value: 'unqualified' },
                      { label: 'Nuôi dưỡng', value: 'nurture' },
                      { label: 'Thành công', value: 'success' },
                      { label: 'Thất bại', value: 'failed' },
                      { label: 'Mở', value: 'open' }
                    ]}
                    defaultValue={currentLead.status}
                    onValueChange={handleStatusChange}
                    placeholder="Trạng thái"
                    className="lead-detail-button-height min-w-[140px]"
                  />
                </div>
                <Button 
                  className="gap-2 h-10" 
                  onClick={handleConvertToDeal}
                  disabled={isLoading}
                >
                  <ArrowLeftRight className="h-4 w-4" />
                  <span>{isLoading ? 'Đang xử lý...' : 'Chuyển thành Deal'}</span>
                </Button>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="icon" className="h-10 w-10">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleMoreAction('edit')}>
                      <Pencil className="h-4 w-4 mr-2" />
                      <span>Chỉnh sửa lead</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleMoreAction('changeOwner')}>
                      <Users className="h-4 w-4 mr-2" />
                      <span>Thay đổi người phụ trách</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      className="text-destructive focus:text-destructive"
                      onClick={() => handleMoreAction('markUnqualified')}
                    >
                      <div className="flex items-center">
                        <Info className="h-4 w-4 mr-2" />
                        <span>Đánh dấu không phù hợp</span>
                      </div>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>

          {/* Main content */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <div className="w-full overflow-x-auto pb-2">
              <TabsList>
                <TabsTrigger value="overview"><Info className="h-4 w-4 mr-2" /> Tổng quan</TabsTrigger>
                <TabsTrigger value="activity"><Activity className="h-4 w-4 mr-2" /> Hoạt động</TabsTrigger>
                <TabsTrigger value="tasks"><ListTodo className="h-4 w-4 mr-2" /> Nhiệm vụ</TabsTrigger>
                <TabsTrigger value="notes"><FileText className="h-4 w-4 mr-2" /> Ghi chú</TabsTrigger>
                <TabsTrigger value="messages"><MessageSquare className="h-4 w-4 mr-2" /> Tin nhắn</TabsTrigger>

              </TabsList>
            </div>

            {/* Tab Contents */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:items-start">
              {/* Main Content Area */}
              <div className="md:col-span-2">
                <TabsContent value="overview" className="mt-0 space-y-4">
                  <LeadOverview
                    lead={currentLead}
                    getStatusBadge={getStatusBadge}
                  />
                </TabsContent>

                <TabsContent value="activity" className="mt-0 space-y-4">
                  <ActivityFeed />
                </TabsContent>

                <TabsContent value="tasks" className="mt-0 space-y-4">
                  <TasksList />
                </TabsContent>

                <TabsContent value="notes" className="mt-0 space-y-4">
                  <NotesList />
                </TabsContent>

                <TabsContent value="messages" className="mt-0 space-y-4">
                  <OmnichannelMessages lead={currentLead} />
                </TabsContent>


              </div>

              {/* Right Sidebar - Contact Info - Hiển thị ở tất cả các tab */}
              <div className="md:col-span-1">
                <ContactInfoPanel lead={currentLead} />
              </div>
            </div>
          </Tabs>
        </div>
      </Main>

      {/* Create Task Dialog */}
      <CreateTaskDialog 
        open={showCreateTaskDialog}
        onOpenChange={setShowCreateTaskDialog}
        lead={currentLead}
      />

      {/* Send Message Dialog */}
      <SendMessageDialog
        open={showSendMessageDialog}
        onOpenChange={setShowSendMessageDialog}
        lead={currentLead}
      />

      {/* Change Owner Dialog */}
      <ChangeOwnerDialog
        open={showChangeOwnerDialog}
        onOpenChange={setShowChangeOwnerDialog}
        lead={currentLead}
        onOwnerChange={(newOwner) => {
          setCurrentLead({...currentLead, leadOwner: newOwner})
          toast.success(`Đã thay đổi người phụ trách thành ${newOwner}`)
        }}
      />
    </>
  )
}

function ContactInfoPanel({ lead }: { lead: Lead }) {
  return (
    <Card className="overflow-hidden border p-0 flex flex-col">
      <div className="bg-muted/50 px-4 py-2 border-b">
        <h3 className="text-base font-medium">Thông tin liên hệ</h3>
      </div>
      <div className="flex-1">
        <div className="divide-y">
          <ContactInfoItem
            icon={<Mail className="h-4 w-4 text-muted-foreground" />}
            label="Email"
            value={
              lead.email ? (
                <a
                  href={`mailto:${lead.email}`}
                  className="text-primary font-medium hover:underline hover:text-primary/80 transition-colors"
                >
                  {lead.email}
                </a>
              ) : (
                <span className="text-muted-foreground">Chưa có thông tin</span>
              )
            }
          />
          <ContactInfoItem
            icon={<Phone className="h-4 w-4 text-muted-foreground" />}
            label="Số điện thoại"
            value={
              lead.mobileNo ? (
                <a
                  href={`tel:${lead.mobileNo}`}
                  className="text-primary font-medium hover:underline hover:text-primary/80 transition-colors"
                >
                  {lead.mobileNo}
                </a>
              ) : (
                <span className="text-muted-foreground">Chưa có thông tin</span>
              )
            }
          />
          <ContactInfoItem
            icon={<Building className="h-4 w-4 text-muted-foreground" />}
            label="Công ty"
            value={lead.company || <span className="text-muted-foreground">Chưa có thông tin</span>}
          />
          <ContactInfoItem
            icon={<Briefcase className="h-4 w-4 text-muted-foreground" />}
            label="Chức vụ"
            value={lead.position || <span className="text-muted-foreground">Chưa có thông tin</span>}
          />
          <ContactInfoItem
            icon={<MapPin className="h-4 w-4 text-muted-foreground" />}
            label="Địa chỉ"
            value={lead.address || <span className="text-muted-foreground">Chưa có thông tin</span>}
          />
          <ContactInfoItem
            icon={<Globe className="h-4 w-4 text-muted-foreground" />}
            label="Website"
            value={<span className="text-muted-foreground">Chưa có thông tin</span>}
          />
        </div>

        {/* Kênh Omnichannel */}
        {lead.omnichannelId && (
          <div className="p-4 border-t">
            <h4 className="text-sm font-medium mb-2">Kênh liên hệ</h4>
            <div className="space-y-2">
              {lead.source === 'messenger' && (
                <div className="flex items-center gap-2 text-sm">
                  <div className="flex-shrink-0 w-5 h-5 flex items-center justify-center">
                    <img src="/images/chat-logos/messenger.svg" alt="Messenger" className="h-4 w-4" />
                  </div>
                  <span>Messenger</span>
                  <Badge variant="outline" className="ml-auto">Kết nối</Badge>
                </div>
              )}
              {lead.source === 'zalo' && (
                <div className="flex items-center gap-2 text-sm">
                  <div className="flex-shrink-0 w-5 h-5 flex items-center justify-center">
                    <img src="/images/chat-logos/zalo.png" alt="Zalo" className="h-4 w-4" />
                  </div>
                  <span>Zalo</span>
                  <Badge variant="outline" className="ml-auto">Kết nối</Badge>
                </div>
              )}
              {!['messenger', 'zalo'].includes(lead.source || '') && (
                <div className="text-sm text-muted-foreground">Không có kênh Omnichannel</div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Các hành động nhanh */}
      <div className="p-3 bg-muted/30 border-t">
        <div className="grid grid-cols-2 gap-2 mb-2">
          <Button 
            variant="outline" 
            size="sm" 
            className="w-full"
            onClick={() => handleQuickAction('call', lead)}
          >
            <Phone className="h-3.5 w-3.5 mr-2" />
            Gọi điện
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            className="w-full"
            onClick={() => handleQuickAction('email', lead)}
          >
            <Mail className="h-3.5 w-3.5 mr-2" />
            Gửi email
          </Button>
        </div>
        <div className="grid grid-cols-2 gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            className="w-full"
            onClick={() => handleQuickAction('message', lead)}
          >
            <MessageSquare className="h-3.5 w-3.5 mr-2" />
            Nhắn tin
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            className="w-full"
            onClick={() => handleQuickAction('schedule', lead)}
          >
            <Calendar className="h-3.5 w-3.5 mr-2" />
            Lịch hẹn
          </Button>
        </div>
      </div>
    </Card>
  )
}

function ContactInfoItem({ icon, label, value }: { icon: React.ReactNode, label: string, value: React.ReactNode }) {
  return (
    <div className="px-4 py-3.5">
      <div className="flex items-start justify-between">
        <div className="flex gap-2 items-center">
          {icon}
          <div className="text-sm font-medium">{label}</div>
        </div>
        <div className="text-sm">{value}</div>
      </div>
    </div>
  )
}



function LeadOverview({
  lead,
  getStatusBadge,
}: {
  lead: Lead,
  getStatusBadge: (status: string) => React.ReactNode,
}) {
  return (
    <div className="space-y-4">

      <Card>
        <CardHeader>
          <CardTitle className="text-base font-medium">Thông tin chi tiết</CardTitle>
        </CardHeader>
        <CardContent className="grid gap-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Thông tin cơ bản - Cột trái */}
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium mb-3">Thông tin liên hệ</h3>
                <div className="space-y-3">
                  <div className="flex items-start justify-between">
                    <span className="text-xs text-muted-foreground w-28">Họ tên:</span>
                    <span className="text-sm font-medium flex-1 text-right">{lead.name}</span>
                  </div>
                  <div className="flex items-start justify-between">
                    <span className="text-xs text-muted-foreground w-28">Email:</span>
                    {lead.email ? (
                      <a href={`mailto:${lead.email}`} className="text-sm font-medium text-primary hover:underline flex-1 text-right">
                        {lead.email}
                      </a>
                    ) : (
                      <span className="text-sm text-muted-foreground flex-1 text-right">N/A</span>
                    )}
                  </div>
                  <div className="flex items-start justify-between">
                    <span className="text-xs text-muted-foreground w-28">Số điện thoại:</span>
                    {lead.mobileNo ? (
                      <a href={`tel:${lead.mobileNo}`} className="text-sm font-medium text-primary hover:underline flex-1 text-right">
                        {lead.mobileNo}
                      </a>
                    ) : (
                      <span className="text-sm text-muted-foreground flex-1 text-right">N/A</span>
                    )}
                  </div>
                  {lead.company && (
                    <div className="flex items-start justify-between">
                      <span className="text-xs text-muted-foreground w-28">Công ty:</span>
                      <span className="text-sm font-medium flex-1 text-right">{lead.company}</span>
                    </div>
                  )}
                  {lead.position && (
                    <div className="flex items-start justify-between">
                      <span className="text-xs text-muted-foreground w-28">Chức vụ:</span>
                      <span className="text-sm font-medium flex-1 text-right">{lead.position}</span>
                    </div>
                  )}
                  {lead.address && (
                    <div className="flex items-start justify-between">
                      <span className="text-xs text-muted-foreground w-28">Địa chỉ:</span>
                      <span className="text-sm font-medium flex-1 text-right">{lead.address}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Thông tin bổ sung - Cột phải */}
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium mb-3">Thông tin Lead</h3>
                <div className="space-y-3">
                  <div className="flex items-start justify-between">
                    <span className="text-xs text-muted-foreground w-28">Trạng thái:</span>
                    <div className="flex-1 text-right">
                      <div className="inline-flex">{getStatusBadge(lead.status)}</div>
                    </div>
                  </div>
                  <div className="flex items-start justify-between">
                    <span className="text-xs text-muted-foreground w-28">Nguồn:</span>
                    <span className="text-sm font-medium capitalize flex-1 text-right">{lead.source || "N/A"}</span>
                  </div>
                  <div className="flex items-start justify-between">
                    <span className="text-xs text-muted-foreground w-28">Người phụ trách:</span>
                    <span className="text-sm font-medium flex-1 text-right">{lead.leadOwner || "Chưa phân công"}</span>
                  </div>
                  {lead.campaignName && (
                    <div className="flex items-start justify-between">
                      <span className="text-xs text-muted-foreground w-28">Chiến dịch:</span>
                      <span className="text-sm font-medium flex-1 text-right">{lead.campaignName}</span>
                    </div>
                  )}
                  <div className="flex items-start justify-between">
                    <span className="text-xs text-muted-foreground w-28">Ngày tạo:</span>
                    <span className="text-sm font-medium flex-1 text-right">{lead.createdAt || lead.lastModified || "N/A"}</span>
                  </div>
                  {lead.lastContactedAt && (
                    <div className="flex items-start justify-between">
                      <span className="text-xs text-muted-foreground w-28">Liên hệ gần nhất:</span>
                      <span className="text-sm font-medium flex-1 text-right">{lead.lastContactedAt}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Tags & Notes */}
          <div className="grid grid-cols-1 gap-4">
            {/* Tags */}
            {lead.tags && lead.tags.length > 0 && (
              <div>
                <h3 className="text-sm font-medium mb-2">Tags</h3>
                <div className="flex flex-wrap gap-1">
                  {lead.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="capitalize">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
            
            {/* Ghi chú */}
            {lead.notes && (
              <div>
                <h3 className="text-sm font-medium mb-2">Ghi chú</h3>
                <div className="p-3 bg-muted/30 rounded-md">
                  <p className="text-sm">{lead.notes}</p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

function ActivityFeed() {
  // Dữ liệu mẫu cho timeline
  const activities = [
    {
      id: '1',
      type: 'email',
      title: 'Đã gửi email giới thiệu sản phẩm',
      timestamp: '2023-05-15T10:30:00',
      user: 'John Doe',
      content: 'Email giới thiệu sản phẩm mới đã được gửi đến khách hàng.',
      icon: <Mail className="h-4 w-4" />
    },
    {
      id: '2',
      type: 'call',
      title: 'Cuộc gọi tư vấn',
      timestamp: '2023-05-14T14:45:00',
      user: 'Jane Smith',
      content: 'Đã gọi điện tư vấn về sản phẩm. Khách hàng quan tâm và yêu cầu thêm thông tin.',
      icon: <Phone className="h-4 w-4" />
    },
    {
      id: '3',
      type: 'meeting',
      title: 'Lịch hẹn demo sản phẩm',
      timestamp: '2023-05-20T09:00:00',
      user: 'John Doe',
      content: 'Đã đặt lịch hẹn demo sản phẩm tại văn phòng khách hàng.',
      icon: <Calendar className="h-4 w-4" />
    },
    {
      id: '4',
      type: 'note',
      title: 'Ghi chú sau cuộc gọi',
      timestamp: '2023-05-14T15:00:00',
      user: 'Jane Smith',
      content: 'Khách hàng quan tâm đến gói Premium. Cần gửi thêm thông tin về giá và tính năng.',
      icon: <FileText className="h-4 w-4" />
    },
    {
      id: '5',
      type: 'system',
      title: 'Cập nhật trạng thái',
      timestamp: '2023-05-13T11:20:00',
      user: 'System',
      content: 'Trạng thái lead đã được cập nhật từ "Open" sang "Contacted".',
      icon: <Activity className="h-4 w-4" />
    }
  ]

  // Format ngày giờ
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <CardTitle className="text-base font-medium">Hoạt động gần đây</CardTitle>
              <CardDescription>Lịch sử hoạt động của lead</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {activities.length > 0 ? (
            <div className="relative space-y-4 before:absolute before:inset-0 before:left-4 before:h-full before:w-0.5 before:bg-border">
              {activities.map((activity) => (
                <div key={activity.id} className="relative pl-10">
                  <div className="absolute left-0 flex h-8 w-8 items-center justify-center rounded-full border bg-background">
                    {activity.icon}
                  </div>
                  <div className="rounded-lg border bg-card p-3 text-card-foreground shadow-sm">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 mb-2">
                      <h4 className="font-medium">{activity.title}</h4>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Clock className="h-3.5 w-3.5" />
                        <span>{formatDateTime(activity.timestamp)}</span>
                      </div>
                    </div>
                    <p className="text-sm text-muted-foreground">{activity.content}</p>
                    <div className="mt-2 flex items-center gap-2">
                      <Avatar className="h-5 w-5">
                        <AvatarFallback className="text-[10px]">{activity.user.substring(0, 2)}</AvatarFallback>
                      </Avatar>
                      <span className="text-xs text-muted-foreground">{activity.user}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="py-8 text-center">
              <Activity className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
              <p className="text-sm font-medium">Không có hoạt động nào</p>
              <p className="text-sm text-muted-foreground mt-1">
                Lead này chưa có hoạt động nào được ghi nhận.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

function TasksList() {
  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-3">
          <div>
            <CardTitle className="text-base font-medium">Nhiệm vụ</CardTitle>
            <CardDescription>Danh sách nhiệm vụ</CardDescription>
          </div>
          <Button variant="outline" size="sm" onClick={() => setShowCreateTaskDialog(true)}>
            <Plus className="h-3.5 w-3.5 mr-2" />
            Thêm nhiệm vụ
          </Button>
        </CardHeader>
        <CardContent className="p-0">
          <div className="divide-y">
            <TaskItem
              title="Gọi điện giới thiệu sản phẩm"
              dueDate="Ngày mai, 10:00"
              assignee="John Doe"
              status="pending"
            />
            <TaskItem
              title="Gửi email giới thiệu demo"
              dueDate="Hôm nay, 15:00"
              assignee="John Doe"
              status="pending"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

function TaskItem({ title, dueDate, assignee, status }: { title: string, dueDate: string, assignee: string, status: string, priority?: string }) {
  return (
    <div className="flex items-center justify-between p-4">
      <div className="flex gap-4 items-center">
        <div className="flex flex-col">
          <div className="font-medium">{title}</div>
          <div className="text-sm text-muted-foreground flex items-center gap-1">
            <Clock className="h-3 w-3" /> {dueDate}
          </div>
        </div>
      </div>
      <div className="flex gap-2 items-center">
        <Avatar className="h-8 w-8">
          <AvatarFallback>{assignee.charAt(0)}</AvatarFallback>
        </Avatar>
        <Badge variant={status === "completed" ? "default" : "outline"}>{status === "completed" ? "Hoàn thành" : "Đang chờ"}</Badge>
      </div>
    </div>
  )
}

function NotesList() {
  const [showAddNote, setShowAddNote] = useState(false)
  const [noteContent, setNoteContent] = useState('')

  // Dữ liệu mẫu cho ghi chú
  const notes = [
    {
      id: '1',
      content: 'Khách hàng quan tâm đến gói Premium. Cần gửi thêm thông tin về giá và tính năng.',
      createdBy: 'John Doe',
      createdAt: '2023-05-14T15:00:00',
      updatedAt: '2023-05-14T15:30:00'
    }
  ]

  // Format ngày giờ
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }

  const handleAddNote = () => {
    if (noteContent.trim()) {
      // In thực tế, sẽ gọi API để save note
      toast.success(`Đã thêm ghi chú: "${noteContent.trim()}"`)
      setNoteContent('')
      setShowAddNote(false)
    } else {
      toast.error('Vui lòng nhập nội dung ghi chú')
    }
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-3">
          <div>
            <CardTitle className="text-base font-medium">Ghi chú</CardTitle>
            <CardDescription>Danh sách ghi chú</CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAddNote(!showAddNote)}
          >
            {showAddNote ? (
              <>
                <X className="h-3.5 w-3.5 mr-2" />
                Hủy
              </>
            ) : (
              <>
                <Plus className="h-3.5 w-3.5 mr-2" />
                Thêm ghi chú
              </>
            )}
          </Button>
        </CardHeader>
        <CardContent>
          {showAddNote && (
            <div className="mb-4 border rounded-md p-3">
              <div className="mb-2">
                <textarea
                  className="w-full min-h-[120px] p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  placeholder="Nhập nội dung ghi chú..."
                  value={noteContent}
                  onChange={(e) => setNoteContent(e.target.value)}
                />
              </div>
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <FileText className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <Bold className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <Italic className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <List className="h-4 w-4" />
                  </Button>
                </div>
                <Button size="sm" onClick={handleAddNote}>
                  Lưu ghi chú
                </Button>
              </div>
            </div>
          )}

          <div className="space-y-4">
            {notes.length > 0 ? (
              notes.map((note) => (
                <div key={note.id} className="border rounded-md p-4">
                  <div className="mb-2 flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Avatar className="h-6 w-6">
                        <AvatarFallback className="text-[10px]">{note.createdBy.substring(0, 2)}</AvatarFallback>
                      </Avatar>
                      <span className="text-sm font-medium">{note.createdBy}</span>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          <Pencil className="h-4 w-4 mr-2" />
                          <span>Chỉnh sửa</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-destructive focus:text-destructive">
                          <Trash className="h-4 w-4 mr-2" />
                          <span>Xóa</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  <div className="text-sm mb-3">{note.content}</div>
                  <div className="text-xs text-muted-foreground flex items-center gap-2">
                    <Clock className="h-3 w-3" />
                    <span>Tạo lúc: {formatDateTime(note.createdAt)}</span>
                    {note.updatedAt && note.updatedAt !== note.createdAt && (
                      <>
                        <span>•</span>
                        <span>Cập nhật lúc: {formatDateTime(note.updatedAt)}</span>
                      </>
                    )}
                  </div>
                </div>
              ))
            ) : (
              <div className="py-8 text-center">
                <FileText className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
                <p className="text-sm font-medium">Chưa có ghi chú nào</p>
                <p className="text-sm text-muted-foreground mt-1">
                  Thêm ghi chú để lưu thông tin quan trọng về lead
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

function OmnichannelMessages({ lead }: { lead: Lead }) {
  const [showSendMessageDialog, setShowSendMessageDialog] = useState(false)
  
  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-3">
          <div>
            <CardTitle className="text-base font-medium">Tin nhắn</CardTitle>
            <CardDescription>Lịch sử tin nhắn</CardDescription>
          </div>
          <Button variant="outline" size="sm" onClick={() => setShowSendMessageDialog(true)}>
            <Plus className="h-3.5 w-3.5 mr-2" />
            Gửi tin nhắn
          </Button>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">Chưa có tin nhắn nào.</p>
          </div>
        </CardContent>
      </Card>
      
      {/* Send Message Dialog for this component */}
      <SendMessageDialog
        open={showSendMessageDialog}
        onOpenChange={setShowSendMessageDialog}
        lead={lead}
      />
    </div>
  )
}

// Create Task Dialog Component
const taskSchema = z.object({
  title: z.string().min(1, 'Tiêu đề là bắt buộc'),
  description: z.string().optional(),
  dueDate: z.string().min(1, 'Ngày hạn là bắt buộc'),
  priority: z.enum(['low', 'medium', 'high']),
})

type TaskFormData = z.infer<typeof taskSchema>

interface CreateTaskDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  lead: Lead
}

function CreateTaskDialog({ open, onOpenChange, lead }: CreateTaskDialogProps) {
  const form = useForm<TaskFormData>({
    resolver: zodResolver(taskSchema),
    defaultValues: {
      title: '',
      description: '',
      dueDate: '',
      priority: 'medium',
    },
  })

  const onSubmit = (data: TaskFormData) => {
    // In thực tế sẽ gọi API để tạo task
    toast.success(`Đã tạo nhiệm vụ "${data.title}" cho ${lead.name}`)
    form.reset()
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Tạo nhiệm vụ mới</DialogTitle>
          <DialogDescription>
            Tạo nhiệm vụ mới cho lead {lead.name}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tiêu đề nhiệm vụ</FormLabel>
                  <FormControl>
                    <Input placeholder="Nhập tiêu đề nhiệm vụ" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Mô tả (tùy chọn)</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Nhập mô tả nhiệm vụ" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="dueDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ngày hạn</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="priority"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Độ ưu tiên</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Chọn độ ưu tiên" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="low">Thấp</SelectItem>
                        <SelectItem value="medium">Trung bình</SelectItem>
                        <SelectItem value="high">Cao</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                Hủy
              </Button>
              <Button type="submit">Tạo nhiệm vụ</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}

// Send Message Dialog Component  
const messageSchema = z.object({
  content: z.string().min(1, 'Nội dung tin nhắn là bắt buộc'),
  channel: z.enum(['email', 'sms', 'messenger', 'zalo']),
})

type MessageFormData = z.infer<typeof messageSchema>

interface SendMessageDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  lead: Lead
}

function SendMessageDialog({ open, onOpenChange, lead }: SendMessageDialogProps) {
  const form = useForm<MessageFormData>({
    resolver: zodResolver(messageSchema),
    defaultValues: {
      content: '',
      channel: 'email',
    },
  })

  const onSubmit = (data: MessageFormData) => {
    // In thực tế sẽ gọi API để gửi tin nhắn
    toast.success(`Đã gửi tin nhắn qua ${data.channel} cho ${lead.name}`)
    form.reset()
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Gửi tin nhắn</DialogTitle>
          <DialogDescription>
            Gửi tin nhắn cho {lead.name}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="channel"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Kênh gửi</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Chọn kênh gửi" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="email">Email</SelectItem>
                      <SelectItem value="sms">SMS</SelectItem>
                      <SelectItem value="messenger">Messenger</SelectItem>
                      <SelectItem value="zalo">Zalo</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="content"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nội dung tin nhắn</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Nhập nội dung tin nhắn"
                      className="min-h-[120px]"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                Hủy
              </Button>
              <Button type="submit">Gửi tin nhắn</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}

// Change Owner Dialog Component
interface ChangeOwnerDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  lead: Lead
  onOwnerChange: (newOwner: string) => void
}

function ChangeOwnerDialog({ open, onOpenChange, lead, onOwnerChange }: ChangeOwnerDialogProps) {
  const [selectedOwner, setSelectedOwner] = useState(lead.leadOwner || '')

  const owners = [
    'John Doe',
    'Ankush Menat', 
    'Adil Shaikh',
    'Suraj Shetty',
    'Faris Ansari',
    'Hussain Nagaria',
    'Shariq Ansari',
    'Asif Mulani'
  ]

  const handleSubmit = () => {
    if (selectedOwner && selectedOwner !== lead.leadOwner) {
      onOwnerChange(selectedOwner)
      onOpenChange(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Thay đổi người phụ trách</DialogTitle>
          <DialogDescription>
            Chọn người phụ trách mới cho lead {lead.name}
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <label className="text-sm font-medium">Người phụ trách hiện tại</label>
            <p className="text-sm text-muted-foreground">{lead.leadOwner || 'Chưa phân công'}</p>
          </div>
          <div>
            <label className="text-sm font-medium">Người phụ trách mới</label>
            <Select value={selectedOwner} onValueChange={setSelectedOwner}>
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Chọn người phụ trách" />
              </SelectTrigger>
              <SelectContent>
                {owners.map((owner) => (
                  <SelectItem key={owner} value={owner}>
                    {owner}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Hủy
          </Button>
          <Button 
            onClick={handleSubmit}
            disabled={!selectedOwner || selectedOwner === lead.leadOwner}
          >
            Thay đổi
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default LeadDetail
