import { useState } from 'react'
import { X, Plus, Tag as TagIcon } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Lead } from '../data/schema'
import { useLeadsContext } from '../context/use-leads-context'

interface LeadTagsProps {
  lead: Lead
}

export function LeadTags({ lead }: LeadTagsProps) {
  const [tags, setTags] = useState<string[]>(lead.tags || [])
  const [isAddingTag, setIsAddingTag] = useState(false)
  const [newTag, setNewTag] = useState('')
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const { updateLeadTags } = useLeadsContext()

  // Danh sách các thẻ gợi ý
  const suggestedTags = [
    'Khách hàng tiềm năng',
    'Ưu tiên cao',
    '<PERSON>ần liên hệ',
    'VIP',
    'Đã liên hệ',
    'Quan tâm sản phẩm A',
    'Quan tâm sản phẩm B',
    'Đã gửi báo giá',
    'Đã demo',
    'Đang đàm phán',
    'Chờ phản hồi',
    'Khách hàng mới',
    'Khách hàng cũ',
    'Đối tác',
    'Nhà cung cấp'
  ]

  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      const updatedTags = [...tags, newTag.trim()]
      setTags(updatedTags)
      updateLeadTags(lead.id, updatedTags)
      setNewTag('')
      setIsAddingTag(false)
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    const updatedTags = tags.filter(tag => tag !== tagToRemove)
    setTags(updatedTags)
    updateLeadTags(lead.id, updatedTags)
  }

  const handleSelectSuggestedTag = (tag: string) => {
    if (!tags.includes(tag)) {
      const updatedTags = [...tags, tag]
      setTags(updatedTags)
      updateLeadTags(lead.id, updatedTags)
    }
    setIsDialogOpen(false)
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <div className="text-sm font-medium">Thẻ</div>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0 rounded-full"
          onClick={() => setIsDialogOpen(true)}
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>

      <div className="flex flex-wrap gap-1.5">
        {tags.length > 0 ? (
          tags.map((tag) => (
            <Badge
              key={tag}
              variant="secondary"
              className="flex items-center gap-1 px-2 py-1"
            >
              {tag}
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 rounded-full hover:bg-muted"
                onClick={() => handleRemoveTag(tag)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))
        ) : (
          <div className="text-sm text-muted-foreground">
            Chưa có thẻ nào được gắn
          </div>
        )}

        {isAddingTag && (
          <div className="flex items-center gap-1">
            <Input
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              className="h-8 text-xs"
              placeholder="Nhập tên thẻ"
              autoFocus
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleAddTag()
                } else if (e.key === 'Escape') {
                  setIsAddingTag(false)
                  setNewTag('')
                }
              }}
            />
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 rounded-full"
              onClick={() => {
                setIsAddingTag(false)
                setNewTag('')
              }}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Quản lý thẻ</DialogTitle>
            <DialogDescription>
              Thêm hoặc xóa thẻ cho lead này.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <div className="text-sm font-medium">Thẻ hiện tại</div>
              <div className="flex flex-wrap gap-1.5">
                {tags.length > 0 ? (
                  tags.map((tag) => (
                    <Badge
                      key={tag}
                      variant="secondary"
                      className="flex items-center gap-1 px-2 py-1"
                    >
                      {tag}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-4 w-4 p-0 rounded-full hover:bg-muted"
                        onClick={() => handleRemoveTag(tag)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  ))
                ) : (
                  <div className="text-sm text-muted-foreground">
                    Chưa có thẻ nào được gắn
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <div className="text-sm font-medium">Thêm thẻ mới</div>
              <div className="flex items-center gap-2">
                <Input
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  className="flex-1"
                  placeholder="Nhập tên thẻ"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleAddTag()
                    }
                  }}
                />
                <Button onClick={handleAddTag} disabled={!newTag.trim()}>
                  Thêm
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <div className="text-sm font-medium">Thẻ gợi ý</div>
              <ScrollArea className="h-[200px] rounded-md border p-2">
                <div className="flex flex-wrap gap-1.5">
                  {suggestedTags.map((tag) => (
                    <Badge
                      key={tag}
                      variant={tags.includes(tag) ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => handleSelectSuggestedTag(tag)}
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>
              </ScrollArea>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              Đóng
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
