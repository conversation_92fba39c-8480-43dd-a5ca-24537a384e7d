import {
  useState,
  type ReactNode,
} from 'react'
import { LeadsContext, ViewMode } from './leads-context-type'
import { Lead } from '../data/schema'

interface LeadsProviderProps {
  children: ReactNode
  initialLeads: Lead[]
}

export default function LeadsProvider({
  children,
  initialLeads,
}: LeadsProviderProps) {
  const [openDialog, setOpenDialog] = useState('')
  const [selectedLeadIds, setSelectedLeadIds] = useState<string[]>([])
  const [viewMode, setViewMode] = useState<ViewMode>('table')
  const [showFilterPanel, setShowFilterPanel] = useState(false)
  const [leads, setLeads] = useState<Lead[]>(initialLeads)

  const updateLeadStatus = (leadId: string, newStatus: string) => {
    setLeads((prevLeads) =>
      prevLeads.map((lead) =>
        lead.id === leadId ? { ...lead, status: newStatus } : lead
      )
    )
  }

  const updateLeadTags = (leadId: string, tags: string[]) => {
    setLeads((prevLeads) =>
      prevLeads.map((lead) =>
        lead.id === leadId ? { ...lead, tags } : lead
      )
    )
  }

  return (
    <LeadsContext.Provider
      value={{
        openDialog,
        setOpenDialog,
        selectedLeadIds,
        setSelectedLeadIds,
        viewMode,
        setViewMode,
        showFilterPanel,
        setShowFilterPanel,
        leads,
        setLeads,
        updateLeadStatus,
        updateLeadTags,
      }}
    >
      {children}
    </LeadsContext.Provider>
  )
}