import { createContext } from 'react'
import { Lead } from '../data/schema'

export type ViewMode = 'table' | 'kanban' | 'salespath'

export type LeadsContextType = {
  openDialog: string
  setOpenDialog: (dialog: string) => void
  selectedLeadIds: string[]
  setSelectedLeadIds: (ids: string[]) => void
  viewMode: ViewMode
  setViewMode: (mode: ViewMode) => void
  showFilterPanel: boolean
  setShowFilterPanel: (show: boolean) => void
  leads: Lead[]
  setLeads: (leads: Lead[]) => void
  updateLeadStatus: (leadId: string, newStatus: string) => void
  updateLeadTags: (leadId: string, tags: string[]) => void
}

export const LeadsContext = createContext<LeadsContextType | null>(null)