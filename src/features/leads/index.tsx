import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { LeadsDialogs } from './components/leads-dialogs.tsx'
import { LeadsPrimaryButtons } from './components/leads-primary-buttons.tsx'
import { LeadsView } from './components/leads-view'
import LeadsProvider from './context/leads-context'
import { useLeadsContext } from './context/use-leads-context'
import { leadListSchema } from './data/schema'
import { leads } from './data/leads.ts'

// Tạo component LeadsContent để có thể sử dụng useLeadsContext hook
function LeadsContent() {
  const { leads } = useLeadsContext()

  return (
    <>
      <Header fixed>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='mb-2 flex flex-wrap items-center justify-between space-y-2'>
          <div>
            <h2 className='text-2xl font-bold tracking-tight'>Leads</h2>
            <p className='text-muted-foreground'>
              Quản lý và theo dõi leads của bạn tại đây.
            </p>
          </div>
          <div className="flex items-center gap-2">
            <LeadsPrimaryButtons />
          </div>
        </div>

        <div className='mb-4 flex items-center justify-between gap-2 overflow-x-auto'>
          <div className='flex items-center gap-2 flex-shrink-0'>
            <div className="rounded-md bg-muted px-3 py-1.5 text-sm text-muted-foreground">
              {leads.length} leads
            </div>
          </div>
        </div>

        <LeadsView data={leads} />
      </Main>

      <LeadsDialogs />
    </>
  )
}

export default function Leads() {
  // Parse lead list
  const leadList = leadListSchema.parse(leads)

  return (
    <LeadsProvider initialLeads={leadList}>
      <LeadsContent />
    </LeadsProvider>
  )
}