import { type ClassValue } from 'clsx'

export const statusTypes = [
  { label: 'Open', value: 'open' },
  { label: 'Contacted', value: 'contacted' },
  { label: 'Qualified', value: 'qualified' },
  { label: 'Unqualified', value: 'unqualified' },
]

export const statusTypesMap = new Map<string, ClassValue>([
  ['open', 'text-green-500 dark:text-green-400 border-green-500 dark:border-green-400'],
  ['contacted', 'text-amber-500 dark:text-amber-400 border-amber-500 dark:border-amber-400'],
  ['qualified', 'text-blue-500 dark:text-blue-400 border-blue-500 dark:border-blue-400'],
  ['unqualified', 'text-red-500 dark:text-red-400 border-red-500 dark:border-red-400'],
])

export const sourceTypes = [
  { label: 'Website', value: 'website' },
  { label: 'Excel', value: 'excel' },
  { label: 'CSV', value: 'csv' },
  { label: 'Web Form', value: 'webform' },
  { label: 'Campaign', value: 'campaign' },
  { label: 'Referral', value: 'referral' },
  { label: 'Messenger', value: 'messenger' },
  { label: 'Zalo', value: 'zalo' },
  { label: 'LinkedIn', value: 'linkedin' },
  { label: 'Facebook', value: 'facebook' },
  { label: 'Google', value: 'google' },
  { label: 'Others', value: 'other' },
]

// Thêm các loại điểm số lead
export const leadScoreTypes = [
  { label: 'Cao (80-100)', value: 'high' },
  { label: 'Trung bình (50-79)', value: 'medium' },
  { label: 'Thấp (0-49)', value: 'low' },
]

// Thêm các tiêu chí BANT
export const bantCriteria = [
  { label: 'Ngân sách (Budget)', value: 'budget' },
  { label: 'Thẩm quyền (Authority)', value: 'authority' },
  { label: 'Nhu cầu (Need)', value: 'need' },
  { label: 'Thời gian (Timeline)', value: 'timeline' },
]

export const leadStatusConfig: Record<string, { name: string; color: string }> = {
  open: { name: 'Open', color: 'bg-emerald-50 text-emerald-700 dark:bg-emerald-950 dark:text-emerald-300' },
  contacted: { name: 'Contacted', color: 'bg-blue-50 text-blue-700 dark:bg-blue-950 dark:text-blue-300' },
  qualified: { name: 'Qualified', color: 'bg-purple-50 text-purple-700 dark:bg-purple-950 dark:text-purple-300' },
  unqualified: { name: 'Unqualified', color: 'bg-rose-50 text-rose-700 dark:bg-rose-950 dark:text-rose-300' },
  // Bạn có thể thêm các trạng thái khác nếu cần
}