import { z } from 'zod'

// Định nghĩa các enum cho Lead
export const LeadStatus = {
  OPEN: 'open',
  CONTACTED: 'contacted',
  QUALIFIED: 'qualified',
  UNQUALIFIED: 'unqualified'
} as const

export const LeadSource = {
  WEBSITE: 'website',
  EXCEL: 'excel',
  CSV: 'csv',
  WEBFORM: 'webform',
  CAMPAIGN: 'campaign',
  REFERRAL: 'referral',
  MESSENGER: 'messenger',
  ZALO: 'zalo',
  LINKEDIN: 'linkedin',
  FACEBOOK: 'facebook',
  GOOGLE: 'google',
  OTHER: 'other'
} as const

export const InterestLevel = {
  HIGH: 'high',
  MEDIUM: 'medium',
  LOW: 'low'
} as const

export const leadSchema = z.object({
  id: z.string(),
  name: z.string(),
  status: z.string(), // Sử dụng các giá trị từ LeadStatus
  email: z.string().optional(),
  mobileNo: z.string().optional(),
  assignedTo: z.string().optional(),
  lastModified: z.string(),
  source: z.string().optional(), // Sử dụng các giá trị từ LeadSource
  leadOwner: z.string().optional(),
  avatar: z.string().optional(),
  // Thông tin công ty
  company: z.string().optional(),
  position: z.string().optional(),
  // Thông tin giá trị và đánh giá
  estimatedValue: z.number().optional(),
  leadScore: z.number().optional(), // Điểm số lead từ 0-100
  scoreBasedOn: z.array(z.enum(['behavior', 'budget', 'authority', 'need', 'timeline'])).optional(), // BANT
  // Thông tin địa chỉ và liên hệ
  address: z.string().optional(),
  interestLevel: z.string().optional(), // Sử dụng các giá trị từ InterestLevel
  // Thông tin liên kết và phân loại
  campaignId: z.string().optional(), // ID chiến dịch nguồn
  campaignName: z.string().optional(), // Tên chiến dịch nguồn
  omnichannelId: z.string().optional(), // ID để liên kết với Omnichannel
  // Thông tin bổ sung
  notes: z.string().optional(),
  tags: z.array(z.string()).optional(),
  // Thông tin thời gian
  createdAt: z.string().optional(),
  lastContactedAt: z.string().optional(),
  // Thông tin hành vi
  websiteVisits: z.number().optional(), // Số lần truy cập website
  emailOpens: z.number().optional(), // Số lần mở email
  emailClicks: z.number().optional(), // Số lần nhấp vào link trong email
})

export const leadListSchema = z.array(leadSchema)

export type Lead = z.infer<typeof leadSchema>

// Các loại dữ liệu bổ sung
export type LeadActivity = {
  id: string
  leadId: string
  type: 'note' | 'email' | 'call' | 'meeting' | 'task' | 'message' | 'system'
  content: string
  timestamp: string
  user: string
  metadata?: Record<string, any>
}

export type LeadTask = {
  id: string
  leadId: string
  title: string
  description?: string
  dueDate: string
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled'
  priority: 'low' | 'medium' | 'high'
  assignee?: string
  createdAt: string
  updatedAt?: string
}

export type LeadNote = {
  id: string
  leadId: string
  content: string
  createdBy: string
  createdAt: string
  updatedAt?: string
}

export type LeadEmail = {
  id: string
  leadId: string
  subject: string
  content: string
  sender: string
  recipients: string[]
  timestamp: string
  hasAttachments: boolean
  attachments?: { name: string, url: string }[]
}

export type LeadCall = {
  id: string
  leadId: string
  type: 'incoming' | 'outgoing'
  duration: number // in seconds
  timestamp: string
  notes?: string
  recordingUrl?: string
  caller: string
  status: 'completed' | 'missed' | 'scheduled'
}

export type LeadMeeting = {
  id: string
  leadId: string
  title: string
  description?: string
  startTime: string
  endTime: string
  location?: string
  attendees: string[]
  status: 'scheduled' | 'completed' | 'cancelled'
  notes?: string
}

export type LeadMessage = {
  id: string
  leadId: string
  content: string
  timestamp: string
  sender: 'lead' | 'agent'
  channel: 'messenger' | 'zalo' | 'website' | 'whatsapp'
  agentId?: string
  attachments?: { name: string, url: string }[]
}