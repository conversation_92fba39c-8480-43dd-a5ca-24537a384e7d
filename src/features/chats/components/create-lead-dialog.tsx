import { useState } from 'react'
import { 
  IconUser, 
  IconMail, 
  IconBuilding, 
  IconMapPin, 
  IconTag,
  IconAlertCircle
} from '@tabler/icons-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'

interface Customer {
  id: string;
  name: string;
  email: string | null;
  company?: string;
  city: string | null;
  country: string | null;
}

interface CreateLeadDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  customer: Customer | null;
  onCreateLead: (leadData: any) => void;
  onCreateAccount: (accountData: any) => void;
}

export function CreateLeadDialog({
  open,
  onOpenChange,
  customer,
  onCreateLead,
  onCreateAccount
}: CreateLeadDialogProps) {
  const [formType, setFormType] = useState<'lead' | 'account'>('lead')
  const [formData, setFormData] = useState({
    name: customer?.name || '',
    email: customer?.email || '',
    company: customer?.company || '',
    address: '',
    city: customer?.city || '',
    country: customer?.country || '',
    source: 'chat',
    status: formType === 'lead' ? 'new' : 'active',
    description: '',
    tags: ['Chat']
  })
  const [newTag, setNewTag] = useState('')
  const [formErrors, setFormErrors] = useState<Record<string, string>>({})

  // Reset form when customer changes
  useState(() => {
    if (customer) {
      setFormData({
        name: customer.name || '',
        email: customer.email || '',
        company: customer.company || '',
        address: '',
        city: customer.city || '',
        country: customer.country || '',
        source: 'chat',
        status: formType === 'lead' ? 'new' : 'active',
        description: '',
        tags: ['Chat']
      })
    }
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    
    // Clear error for this field if it exists
    if (formErrors[name]) {
      setFormErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[name]
        return newErrors
      })
    }
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleAddTag = () => {
    if (newTag && !formData.tags.includes(newTag)) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag]
      }))
      setNewTag('')
    }
  }

  const handleRemoveTag = (tag: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(t => t !== tag)
    }))
  }

  const validateForm = () => {
    const errors: Record<string, string> = {}
    
    if (!formData.name.trim()) {
      errors.name = 'Tên không được để trống'
    }
    
    if (formData.email && !/^\S+@\S+\.\S+$/.test(formData.email)) {
      errors.email = 'Email không hợp lệ'
    }
    
    if (formType === 'account' && !formData.company) {
      errors.company = 'Tên công ty không được để trống'
    }
    
    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = () => {
    if (!validateForm()) return
    
    if (formType === 'lead') {
      onCreateLead(formData)
    } else {
      onCreateAccount(formData)
    }
    
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>
            {formType === 'lead' ? 'Tạo Lead mới' : 'Tạo Account mới'} từ cuộc hội thoại
          </DialogTitle>
          <DialogDescription>
            Tạo {formType === 'lead' ? 'lead' : 'account'} mới từ thông tin khách hàng trong cuộc hội thoại.
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex space-x-4 mb-4">
          <Button 
            variant={formType === 'lead' ? 'default' : 'outline'} 
            onClick={() => setFormType('lead')}
            className="flex-1"
          >
            <IconUser className="mr-2 h-4 w-4" />
            Lead
          </Button>
          <Button 
            variant={formType === 'account' ? 'default' : 'outline'} 
            onClick={() => setFormType('account')}
            className="flex-1"
          >
            <IconBuilding className="mr-2 h-4 w-4" />
            Account
          </Button>
        </div>
        
        {Object.keys(formErrors).length > 0 && (
          <Alert variant="destructive" className="mb-4">
            <IconAlertCircle className="h-4 w-4" />
            <AlertTitle>Lỗi</AlertTitle>
            <AlertDescription>
              Vui lòng kiểm tra lại thông tin đã nhập
            </AlertDescription>
          </Alert>
        )}
        
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">
                Tên {formType === 'lead' ? 'Lead' : 'Account'} <span className="text-red-500">*</span>
              </Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className={formErrors.name ? 'border-red-500' : ''}
              />
              {formErrors.name && (
                <p className="text-xs text-red-500">{formErrors.name}</p>
              )}
            </div>
            
            {formType === 'account' && (
              <div className="space-y-2">
                <Label htmlFor="company">
                  Tên công ty <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="company"
                  name="company"
                  value={formData.company}
                  onChange={handleInputChange}
                  className={formErrors.company ? 'border-red-500' : ''}
                />
                {formErrors.company && (
                  <p className="text-xs text-red-500">{formErrors.company}</p>
                )}
              </div>
            )}
            
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email || ''}
                onChange={handleInputChange}
                className={formErrors.email ? 'border-red-500' : ''}
              />
              {formErrors.email && (
                <p className="text-xs text-red-500">{formErrors.email}</p>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="source">Nguồn</Label>
              <Select
                value={formData.source}
                onValueChange={(value) => handleSelectChange('source', value)}
              >
                <SelectTrigger id="source">
                  <SelectValue placeholder="Chọn nguồn" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="chat">Chat</SelectItem>
                  <SelectItem value="website">Website</SelectItem>
                  <SelectItem value="social">Mạng xã hội</SelectItem>
                  <SelectItem value="referral">Giới thiệu</SelectItem>
                  <SelectItem value="other">Khác</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="status">Trạng thái</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleSelectChange('status', value)}
              >
                <SelectTrigger id="status">
                  <SelectValue placeholder="Chọn trạng thái" />
                </SelectTrigger>
                <SelectContent>
                  {formType === 'lead' ? (
                    <>
                      <SelectItem value="new">Mới</SelectItem>
                      <SelectItem value="contacted">Đã liên hệ</SelectItem>
                      <SelectItem value="qualified">Đủ điều kiện</SelectItem>
                      <SelectItem value="unqualified">Không đủ điều kiện</SelectItem>
                    </>
                  ) : (
                    <>
                      <SelectItem value="active">Hoạt động</SelectItem>
                      <SelectItem value="inactive">Không hoạt động</SelectItem>
                    </>
                  )}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <Separator />
          
          <div className="space-y-2">
            <Label htmlFor="description">Mô tả</Label>
            <Textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={3}
            />
          </div>
          
          <div className="space-y-2">
            <Label>Tags</Label>
            <div className="flex flex-wrap gap-2 mb-2">
              {formData.tags.map(tag => (
                <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                  {tag}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 rounded-full hover:bg-muted"
                    onClick={() => handleRemoveTag(tag)}
                  >
                    <IconTag className="h-3 w-3" />
                  </Button>
                </Badge>
              ))}
            </div>
            <div className="flex gap-2">
              <Input
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                placeholder="Thêm tag mới"
                className="flex-1"
              />
              <Button type="button" onClick={handleAddTag} disabled={!newTag}>
                Thêm
              </Button>
            </div>
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Hủy
          </Button>
          <Button onClick={handleSubmit}>
            Tạo {formType === 'lead' ? 'Lead' : 'Account'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
