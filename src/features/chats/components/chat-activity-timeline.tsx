import { useState } from 'react'
import { format } from 'date-fns'
import { 
  Calendar, 
  Clock, 
  Filter as FilterIcon, 
  MessageSquare, 
  User, 
  Mail, 
  Tag, 
  Check, 
  X, 
  AlertTriangle, 
  UserPlus, 
  ArrowsExchange, 
  Link, 
  Plus
} from 'lucide-react'
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Badge } from '@/components/ui/badge'

interface Activity {
  id: string;
  customerId: string;
  actorName: string;
  action: string;
  field?: string;
  value?: string;
  timestamp: number;
}

interface ChatActivityTimelineProps {
  activities: Activity[];
  onAddNote: () => void;
}

export function ChatActivityTimeline({ activities, onAddNote }: ChatActivityTimelineProps) {
  const [filter, setFilter] = useState<string | null>(null)

  // Nhóm hoạt động theo ngày
  const groupActivitiesByDate = () => {
    const groups: Record<string, Activity[]> = {}
    
    activities.forEach(activity => {
      const date = new Date(activity.timestamp).toISOString().split('T')[0]
      if (!groups[date]) {
        groups[date] = []
      }
      groups[date].push(activity)
    })
    
    // Sắp xếp các nhóm theo ngày giảm dần (mới nhất lên đầu)
    return Object.entries(groups)
      .sort(([dateA], [dateB]) => new Date(dateB).getTime() - new Date(dateA).getTime())
      .map(([date, activities]) => ({
        date,
        activities: activities.sort((a, b) => 
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        )
      }))
  }

  const filteredActivities = filter 
    ? activities.filter(activity => activity.action === filter)
    : activities

  const activityGroups = groupActivitiesByDate()

  // Hàm lấy icon cho loại hoạt động
  const getActivityIcon = (action: string) => {
    switch (action) {
      case 'created':
        return <MessageSquare className="h-3 w-3 text-primary" />
      case 'sent_message':
        return <Mail className="h-3 w-3 text-primary" />
      case 'received_message':
        return <MessageSquare className="h-3 w-3 text-primary" />
      case 'assigned':
        return <UserPlus className="h-3 w-3 text-primary" />
      case 'status_changed':
        return <ArrowsExchange className="h-3 w-3 text-primary" />
      case 'tagged':
        return <Tag className="h-3 w-3 text-primary" />
      case 'note_added':
        return <MessageSquare className="h-3 w-3 text-primary" />
      case 'linked':
        return <Link className="h-3 w-3 text-primary" />
      case 'resolved':
        return <Check className="h-3 w-3 text-primary" />
      case 'reopened':
        return <AlertTriangle className="h-3 w-3 text-primary" />
      default:
        return <Calendar className="h-3 w-3 text-primary" />
    }
  }

  // Hàm định dạng ngày
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)
    
    if (date.toDateString() === today.toDateString()) {
      return 'Hôm nay'
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Hôm qua'
    } else {
      return format(date, 'dd/MM/yyyy')
    }
  }

  // Hàm định dạng nội dung hoạt động
  const formatActivityContent = (activity: Activity) => {
    switch (activity.action) {
      case 'created':
        return 'đã tạo cuộc hội thoại'
      case 'sent_message':
        return 'đã gửi tin nhắn'
      case 'received_message':
        return 'đã nhận tin nhắn'
      case 'assigned':
        return `đã phân công cho ${activity.value}`
      case 'status_changed':
        return `đã thay đổi trạng thái thành ${activity.value}`
      case 'tagged':
        return `đã gắn thẻ ${activity.value}`
      case 'note_added':
        return 'đã thêm ghi chú'
      case 'linked':
        return `đã liên kết với ${activity.value}`
      case 'resolved':
        return 'đã giải quyết cuộc hội thoại'
      case 'reopened':
        return 'đã mở lại cuộc hội thoại'
      default:
        return activity.action
    }
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base font-medium">Lịch sử hoạt động</CardTitle>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Calendar className="h-3.5 w-3.5 mr-2" />
              Lọc theo ngày
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <FilterIcon className="h-3.5 w-3.5 mr-2" />
                  {filter ? `Lọc: ${filter}` : 'Lọc hoạt động'}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => setFilter(null)}>
                  Tất cả hoạt động
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setFilter('sent_message')}>
                  Tin nhắn đã gửi
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setFilter('received_message')}>
                  Tin nhắn đã nhận
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setFilter('assigned')}>
                  Phân công
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setFilter('status_changed')}>
                  Thay đổi trạng thái
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setFilter('note_added')}>
                  Ghi chú
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <Button variant="outline" size="sm" onClick={onAddNote}>
              <Plus className="h-3.5 w-3.5 mr-2" />
              Thêm ghi chú
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <ScrollArea className="h-[400px]">
          <div className="px-4 py-2 space-y-6">
            {activityGroups.length > 0 ? (
              activityGroups.map(group => (
                <div key={group.date} className="relative pl-6 pb-2">
                  <div className="absolute top-0 left-0 w-px h-full bg-border"></div>
                  <div className="absolute top-0 left-0 w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center -translate-x-1/2">
                    <Calendar className="h-3 w-3 text-primary" />
                  </div>
                  <div className="font-medium text-sm text-muted-foreground mb-4">
                    {formatDate(group.date)}
                  </div>

                  <div className="space-y-4">
                    {group.activities
                      .filter(activity => !filter || activity.action === filter)
                      .map(activity => (
                        <ActivityItem
                          key={activity.id}
                          avatar={activity.actorName}
                          name={activity.actorName}
                          content={formatActivityContent(activity)}
                          description={activity.value}
                          icon={getActivityIcon(activity.action)}
                          time={format(new Date(activity.timestamp), 'HH:mm')}
                          action={activity.action}
                        />
                      ))
                    }
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-20" />
                <p>Chưa có hoạt động nào</p>
              </div>
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  )
}

interface ActivityItemProps {
  avatar: string
  name: string
  content: string
  description?: string
  icon: React.ReactNode
  time: string
  action: string
}

function ActivityItem({
  avatar,
  name,
  content,
  description,
  icon,
  time,
  action
}: ActivityItemProps) {
  // Generate initials from name
  const getInitials = (name: string) => {
    if (!name) return "NA"
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
  }

  const isSystem = name.toLowerCase() === 'hệ thống'

  return (
    <div className="flex items-start gap-3 py-4 border-b border-border last:border-none">
      <Avatar className="h-8 w-8 mt-0.5">
        <AvatarFallback className={`text-xs ${isSystem ? 'bg-blue-100 text-blue-600' : 'bg-primary/10 text-primary'}`}>
          {getInitials(avatar)}
        </AvatarFallback>
      </Avatar>
      <div className="flex-1 min-w-0">
        <div className="text-sm">
          <span className="font-medium">{name}</span>
          <span className="text-muted-foreground ml-1">{content}</span>
          {action === 'note_added' && description && (
            <div className="mt-1 text-sm bg-muted/50 rounded-md p-1.5 break-words">
              {description}
            </div>
          )}
          {action === 'status_changed' && (
            <Badge variant="outline" className="ml-2">
              {description}
            </Badge>
          )}
        </div>
        <div className="text-xs text-muted-foreground mt-1 flex items-center">
          <Clock className="h-3 w-3 mr-1 inline-block" />
          {time}
        </div>
      </div>
    </div>
  )
}
