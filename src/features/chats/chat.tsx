import { useState } from 'react'
import { format } from 'date-fns'
import { IconSearch, IconSend } from '@tabler/icons-react'
import { cn } from '@/lib/utils'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Input } from '@/components/ui/input'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'

// Simplified data models
interface Contact {
  id: string
  name: string
  avatar: string
  lastMessage: string
  timestamp: number
  channel: 'messenger' | 'zalo'
  unread?: boolean
}

interface Message {
  id: string
  sender: 'user' | 'agent'
  content: string
  timestamp: number
}

// Sample data
const contacts: Contact[] = [
  {
    id: '1',
    name: 'Đỗ Nam',
    avatar: '/avatars/01.png',
    lastMessage: 'Chào bạn',
    timestamp: new Date().getTime(),
    channel: 'messenger',
    unread: true
  },
  {
    id: '2',
    name: 'Nguyễn Văn A',
    avatar: '/avatars/02.png',
    lastMessage: 'Tôi có thắc mắc về sản phẩm',
    timestamp: new Date().getTime() - 1000 * 60 * 5,
    channel: 'zalo'
  },
  {
    id: '3',
    name: 'Trần Thị B',
    avatar: '/avatars/03.png',
    lastMessage: 'Sản phẩm có vấn đề',
    timestamp: new Date().getTime() - 1000 * 60 * 30,
    channel: 'messenger'
  },
]

// Sample messages
const messages: Record<string, Message[]> = {
  '1': [
    { id: '1', sender: 'user', content: 'Chào bạn', timestamp: new Date().getTime() - 1000 * 60 * 10 },
    { id: '2', sender: 'agent', content: 'Chào bạn, tôi có thể giúp gì cho bạn?', timestamp: new Date().getTime() - 1000 * 60 * 8 },
    { id: '3', sender: 'user', content: 'Tôi muốn hỏi về sản phẩm mới của công ty', timestamp: new Date().getTime() - 1000 * 60 * 6 },
    { id: '4', sender: 'agent', content: 'Vâng, chúng tôi vừa ra mắt dòng sản phẩm mới. Bạn quan tâm đến loại sản phẩm nào?', timestamp: new Date().getTime() - 1000 * 60 * 4 },
    { id: '5', sender: 'user', content: 'Tôi quan tâm đến điện thoại thông minh', timestamp: new Date().getTime() - 1000 * 60 * 2 },
  ],
  '2': [
    { id: '1', sender: 'user', content: 'Tôi có thắc mắc về sản phẩm', timestamp: new Date().getTime() - 1000 * 60 * 60 },
    { id: '2', sender: 'agent', content: 'Vâng, bạn muốn hỏi gì về sản phẩm ạ?', timestamp: new Date().getTime() - 1000 * 60 * 55 },
    { id: '3', sender: 'user', content: 'Sản phẩm Y-Series có bảo hành mấy năm?', timestamp: new Date().getTime() - 1000 * 60 * 50 },
    { id: '4', sender: 'agent', content: 'Dòng sản phẩm Y-Series của chúng tôi được bảo hành chính hãng 24 tháng.', timestamp: new Date().getTime() - 1000 * 60 * 45 },
  ],
  '3': [
    { id: '1', sender: 'user', content: 'Sản phẩm có vấn đề', timestamp: new Date().getTime() - 1000 * 60 * 60 },
    { id: '2', sender: 'agent', content: 'Rất tiếc về sự cố này. Bạn vui lòng mô tả cụ thể vấn đề để chúng tôi hỗ trợ nhé.', timestamp: new Date().getTime() - 1000 * 60 * 55 },
    { id: '3', sender: 'user', content: 'Sản phẩm bị lỗi màn hình sau 1 tuần sử dụng, xuất hiện nhiều đốm sáng.', timestamp: new Date().getTime() - 1000 * 60 * 50 },
  ]
}

export default function Chat() {
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [messageInput, setMessageInput] = useState('')

  // Filter contacts based on search term
  const filteredContacts = contacts.filter(contact => {
    return contact.name.toLowerCase().includes(searchTerm.toLowerCase())
  })

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault()
    if (!messageInput.trim() || !selectedContact) return

    // In a real app, you would send this to an API
    console.log('Sending message:', messageInput, 'to contact:', selectedContact.id)
    setMessageInput('')
  }

  return (
    <>
      <Header fixed>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className="flex h-[calc(100vh-6.5rem)] border rounded-md">
          {/* Left sidebar for contacts */}
          <div className="border-r w-80 flex flex-col h-full overflow-hidden">
            {/* Search and tabs */}
            <div className="p-4 border-b flex-shrink-0">
              <div className="flex items-center justify-between mb-4">
                <h2 className="font-semibold text-lg">Tin nhắn</h2>
              </div>

              <div className="relative mb-4">
                <IconSearch className="absolute left-3 top-2.5 text-muted-foreground" size={16} />
                <input
                  type="text"
                  placeholder="Tìm kiếm..."
                  className="w-full pl-10 h-9 rounded-md border border-input bg-background px-3 py-2 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              <Tabs defaultValue="all" className="w-full">
                <TabsList className="w-full grid grid-cols-2 mb-2">
                  <TabsTrigger value="all">Tất cả</TabsTrigger>
                  <TabsTrigger value="unread">Chưa đọc</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>

            {/* Contact list */}
            <ScrollArea className="flex-grow h-0">
              <div className="p-2">
                {filteredContacts.map((contact) => (
                  <div
                    key={contact.id}
                    className={cn(
                      "flex items-start gap-3 p-3 rounded-md mb-2 cursor-pointer hover:bg-muted/50 transition-colors",
                      selectedContact?.id === contact.id && "bg-muted"
                    )}
                    onClick={() => setSelectedContact(contact)}
                  >
                    <Avatar>
                      <AvatarImage src={contact.avatar} alt={contact.name} />
                      <AvatarFallback>{contact.name.substring(0, 2)}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <div className="flex justify-between items-center">
                        <p className={cn("font-medium truncate", contact.unread && "font-bold")}>
                          {contact.name}
                        </p>
                        <span className="text-xs text-muted-foreground">
                          {format(contact.timestamp, 'HH:mm')}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground truncate">{contact.lastMessage}</p>
                      <Badge variant="secondary" className="h-5 text-xs px-2 py-0.5 rounded-sm mt-1">
                        {contact.channel === 'messenger' ?
                          <img src="/images/chat-logos/messenger.svg" alt="Messenger" className="h-3 w-3 inline mr-1" /> :
                          <img src="/images/chat-logos/zalo.png" alt="Zalo" className="h-3 w-3 inline mr-1" />
                        }
                        {contact.channel === 'messenger' ? 'Messenger' : 'Zalo'}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>

          {/* Main chat area */}
          <div className="flex-1 flex flex-col h-full overflow-hidden">
            {selectedContact ? (
              <>
                {/* Chat header */}
                <div className="border-b p-4 flex items-center gap-3 flex-shrink-0">
                  <Avatar>
                    <AvatarImage src={selectedContact.avatar} alt={selectedContact.name} />
                    <AvatarFallback>{selectedContact.name.substring(0, 2)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-medium">{selectedContact.name}</h3>
                    <Badge variant="secondary" className="h-5 text-xs px-2 py-0.5 rounded-sm">
                      {selectedContact.channel === 'messenger' ?
                        <img src="/images/chat-logos/messenger.svg" alt="Messenger" className="h-3 w-3 inline mr-1" /> :
                        <img src="/images/chat-logos/zalo.png" alt="Zalo" className="h-3 w-3 inline mr-1" />
                      }
                      {selectedContact.channel === 'messenger' ? 'Messenger' : 'Zalo'}
                    </Badge>
                  </div>
                </div>

                {/* Messages area */}
                <ScrollArea className="flex-grow p-4">
                  {messages[selectedContact.id]?.map((message) => (
                    <div
                      key={message.id}
                      className={cn(
                        "flex mb-4",
                        message.sender === 'user' ? "justify-start" : "justify-end"
                      )}
                    >
                      {message.sender === 'user' && (
                        <Avatar className="h-8 w-8 mr-2">
                          <AvatarImage src={selectedContact.avatar} alt={selectedContact.name} />
                          <AvatarFallback>{selectedContact.name.substring(0, 2)}</AvatarFallback>
                        </Avatar>
                      )}
                      <div
                        className={cn(
                          "max-w-[70%] px-3 py-2 rounded-lg",
                          message.sender === 'user'
                            ? "bg-muted"
                            : "bg-primary text-primary-foreground"
                        )}
                      >
                        <p className="text-sm">{message.content}</p>
                        <p className="text-xs opacity-70 mt-1 text-right">
                          {format(message.timestamp, 'HH:mm')}
                        </p>
                      </div>
                      {message.sender === 'agent' && (
                        <Avatar className="h-8 w-8 ml-2">
                          <AvatarImage src="/avatars/agent.png" alt="Agent" />
                          <AvatarFallback>AG</AvatarFallback>
                        </Avatar>
                      )}
                    </div>
                  ))}
                </ScrollArea>

                {/* Message input */}
                <form
                  onSubmit={handleSendMessage}
                  className="p-4 border-t flex items-center gap-2"
                >
                  <Input
                    placeholder="Nhập tin nhắn..."
                    value={messageInput}
                    onChange={(e) => setMessageInput(e.target.value)}
                    className="flex-1"
                  />
                  <Button type="submit" size="icon">
                    <IconSend size={18} />
                  </Button>
                </form>
              </>
            ) : (
              <div className="flex items-center justify-center h-full text-muted-foreground">
                <p>Chọn một cuộc trò chuyện để bắt đầu</p>
              </div>
            )}
          </div>
        </div>
      </Main>
    </>
  )
}
