import { ProductStatus, ProductType } from './schema'

// <PERSON><PERSON><PERSON> lo<PERSON> sản phẩm
export const productTypes = [
  {
    value: ProductType.PHYSICAL,
    label: 'Sản phẩm vật lý',
    description: '<PERSON>ản phẩm hữu hình cần vận chuyển'
  },
  {
    value: ProductType.DIGITAL,
    label: 'Sản phẩm số',
    description: 'Sản phẩm kỹ thuật số có thể tải xuống'
  },
  {
    value: ProductType.SERVICE,
    label: 'Dịch vụ',
    description: 'Dịch vụ được cung cấp'
  },
  {
    value: ProductType.SUBSCRIPTION,
    label: 'Đăng ký',
    description: 'Sản phẩm theo mô hình đăng ký định kỳ'
  },
  {
    value: ProductType.BUNDLE,
    label: 'Gói sản phẩm',
    description: 'Bộ sản phẩm kết hợp'
  }
]

// Trạng thái sản phẩm
export const productStatuses = [
  {
    value: ProductStatus.ACTIVE,
    label: 'Đang hoạt động',
    color: 'success'
  },
  {
    value: ProductStatus.INACTIVE,
    label: 'Không hoạt động',
    color: 'secondary'
  },
  {
    value: ProductStatus.OUT_OF_STOCK,
    label: 'Hết hàng',
    color: 'warning'
  },
  {
    value: ProductStatus.DISCONTINUED,
    label: 'Ngừng kinh doanh',
    color: 'destructive'
  }
]

// Danh mục sản phẩm
export const productCategories = [
  {
    value: 'software',
    label: 'Phần mềm'
  },
  {
    value: 'hardware',
    label: 'Phần cứng'
  },
  {
    value: 'services',
    label: 'Dịch vụ'
  },
  {
    value: 'training',
    label: 'Đào tạo'
  },
  {
    value: 'consulting',
    label: 'Tư vấn'
  }
]

// Danh mục con
export const productSubcategories = {
  software: [
    { value: 'crm', label: 'Phần mềm CRM' },
    { value: 'erp', label: 'Phần mềm ERP' },
    { value: 'accounting', label: 'Phần mềm kế toán' },
    { value: 'hr', label: 'Phần mềm nhân sự' },
    { value: 'ecommerce', label: 'Phần mềm thương mại điện tử' }
  ],
  hardware: [
    { value: 'servers', label: 'Máy chủ' },
    { value: 'networking', label: 'Thiết bị mạng' },
    { value: 'storage', label: 'Thiết bị lưu trữ' },
    { value: 'peripherals', label: 'Thiết bị ngoại vi' }
  ],
  services: [
    { value: 'implementation', label: 'Triển khai' },
    { value: 'support', label: 'Hỗ trợ kỹ thuật' },
    { value: 'maintenance', label: 'Bảo trì' },
    { value: 'cloud', label: 'Dịch vụ đám mây' }
  ],
  training: [
    { value: 'onsite', label: 'Đào tạo tại chỗ' },
    { value: 'online', label: 'Đào tạo trực tuyến' },
    { value: 'certification', label: 'Chứng chỉ' }
  ],
  consulting: [
    { value: 'business', label: 'Tư vấn doanh nghiệp' },
    { value: 'technical', label: 'Tư vấn kỹ thuật' },
    { value: 'strategy', label: 'Tư vấn chiến lược' }
  ]
}
