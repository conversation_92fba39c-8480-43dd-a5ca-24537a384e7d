import { z } from 'zod'

// Enum for product status
export const ProductStatus = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  OUT_OF_STOCK: 'out_of_stock',
  DISCONTINUED: 'discontinued'
} as const

// Enum for product type
export const ProductType = {
  PHYSICAL: 'physical',
  DIGITAL: 'digital',
  SERVICE: 'service',
  SUBSCRIPTION: 'subscription',
  BUNDLE: 'bundle'
} as const

// Schema for product variant
export const productVariantSchema = z.object({
  id: z.string(),
  name: z.string(),
  sku: z.string().optional(),
  barcode: z.string().optional(),
  price: z.number(),
  costPrice: z.number().optional(),
  stockQuantity: z.number().optional(),
  attributes: z.record(z.string()).optional(), // e.g., { color: 'red', size: 'XL' }
  image: z.string().optional()
})

// Schema for product
export const productSchema = z.object({
  id: z.string(),
  name: z.string().min(1, "Tên sản phẩm không được để trống"),
  sku: z.string().optional(),
  barcode: z.string().optional(),
  description: z.string().optional(),
  shortDescription: z.string().optional(),
  type: z.string(), // Sử dụng các giá trị từ ProductType
  status: z.string(), // Sử dụng các giá trị từ ProductStatus
  category: z.string().optional(),
  subcategory: z.string().optional(),
  price: z.number(),
  costPrice: z.number().optional(),
  salePrice: z.number().optional(),
  stockQuantity: z.number().optional(),
  lowStockThreshold: z.number().optional(),
  weight: z.number().optional(),
  dimensions: z.object({
    length: z.number().optional(),
    width: z.number().optional(),
    height: z.number().optional(),
    unit: z.string().optional()
  }).optional(),
  images: z.array(z.string()).optional(),
  thumbnailImage: z.string().optional(),
  tags: z.array(z.string()).optional(),
  attributes: z.record(z.string()).optional(), // e.g., { brand: 'Apple', color: 'Silver' }
  variants: z.array(productVariantSchema).optional(),
  relatedProductIds: z.array(z.string()).optional(),
  createdAt: z.string(),
  updatedAt: z.string().optional(),
  createdBy: z.string().optional(),
  taxRate: z.number().optional(),
  taxable: z.boolean().optional(),
  
  // Metrics
  salesCount: z.number().optional(),
  viewCount: z.number().optional(),
  rating: z.number().optional(),
  reviewCount: z.number().optional(),
  
  // For digital products
  downloadLink: z.string().optional(),
  licenseType: z.string().optional(),
  
  // For subscriptions
  billingPeriod: z.string().optional(), // monthly, yearly, etc.
  trialPeriod: z.number().optional(), // in days
  
  // For bundles
  bundledProducts: z.array(
    z.object({
      productId: z.string(),
      quantity: z.number(),
      discount: z.number().optional() // percentage discount
    })
  ).optional()
})

export const productListSchema = z.array(productSchema)

// Types
export type ProductStatusType = typeof ProductStatus[keyof typeof ProductStatus]
export type ProductTypeType = typeof ProductType[keyof typeof ProductType]
export type ProductVariant = z.infer<typeof productVariantSchema>
export type Product = z.infer<typeof productSchema>
