import { ListChecks, Grid } from 'lucide-react'
import { useProductsContext } from '../context/use-products-context'
import { cn } from '@/lib/utils'

export function ViewModeToggle() {
  const { viewMode, setViewMode } = useProductsContext()

  return (
    <div className="inline-flex h-10 items-center justify-center rounded-lg bg-muted text-muted-foreground p-[4px]">
      <button
        className={cn(
          "inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
          viewMode === "table" &&
            "bg-background text-foreground shadow-sm"
        )}
        onClick={() => setViewMode("table")}
      >
        <ListChecks className="mr-2.5 h-4 w-4" />
        <span>Bảng</span>
      </button>
      <button
        className={cn(
          "inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
          viewMode === "grid" &&
            "bg-background text-foreground shadow-sm"
        )}
        onClick={() => setViewMode("grid")}
      >
        <Grid className="mr-2.5 h-4 w-4" />
        <span>Lưới</span>
      </button>
    </div>
  )
}
