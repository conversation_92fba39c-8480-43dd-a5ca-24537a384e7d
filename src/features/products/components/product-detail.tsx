import { Product } from '../data/schema'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { formatCurrency } from '@/utils/format'
import { 
  Package, 
  Tag, 
  Truck, 
  Info, 
  BarChart
} from 'lucide-react'

interface ProductDetailProps {
  product: Product
}

export function ProductDetail({ product }: ProductDetailProps) {
  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row gap-6">
        {/* Hình ảnh sản phẩm */}
        <div className="w-full md:w-1/3">
          <Card>
            <CardContent className="p-4">
              {product.thumbnailImage ? (
                <img 
                  src={product.thumbnailImage} 
                  alt={product.name} 
                  className="w-full h-auto rounded-md"
                />
              ) : (
                <div className="w-full aspect-square bg-muted flex items-center justify-center rounded-md">
                  <Package className="h-16 w-16 text-muted-foreground/50" />
                </div>
              )}
              {product.images && product.images.length > 0 && (
                <div className="grid grid-cols-4 gap-2 mt-2">
                  {product.images.slice(0, 4).map((image, index) => (
                    <div key={index} className="aspect-square bg-muted rounded-md overflow-hidden">
                      <img src={image} alt={`${product.name} ${index + 1}`} className="w-full h-full object-cover" />
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Thông tin cơ bản */}
        <div className="w-full md:w-2/3">
          <Card>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <CardTitle>{product.name}</CardTitle>
                <Badge variant={
                  product.status === 'active' ? 'default' :
                  product.status === 'out_of_stock' ? 'outline' :
                  product.status === 'discontinued' ? 'destructive' : 'secondary'
                }>
                  {product.status === 'active' ? 'Hoạt động' :
                   product.status === 'inactive' ? 'Không hoạt động' :
                   product.status === 'out_of_stock' ? 'Hết hàng' : 'Ngừng kinh doanh'}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Mã SKU</p>
                    <p>{product.sku || 'N/A'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Mã Barcode</p>
                    <p>{product.barcode || 'N/A'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Loại sản phẩm</p>
                    <Badge variant="outline" className="mt-1">
                      {product.type === 'physical' ? 'Vật lý' :
                       product.type === 'digital' ? 'Số' :
                       product.type === 'service' ? 'Dịch vụ' :
                       product.type === 'subscription' ? 'Đăng ký' :
                       product.type === 'bundle' ? 'Gói' : product.type}
                    </Badge>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Danh mục</p>
                    <div className="flex gap-2 mt-1">
                      {product.category && (
                        <Badge variant="outline">
                          {product.category === 'software' ? 'Phần mềm' :
                           product.category === 'hardware' ? 'Phần cứng' :
                           product.category === 'services' ? 'Dịch vụ' :
                           product.category === 'training' ? 'Đào tạo' :
                           product.category === 'consulting' ? 'Tư vấn' : product.category}
                        </Badge>
                      )}
                      {product.subcategory && (
                        <Badge variant="outline">
                          {product.subcategory}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>

                <div className="pt-2 border-t">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Giá bán</p>
                      <p className="text-2xl font-bold">
                        {formatCurrency(product.price)}
                      </p>
                      {product.salePrice && (
                        <div className="flex items-center gap-2">
                          <Badge variant="destructive">
                            Giảm {Math.round((1 - product.salePrice / product.price) * 100)}%
                          </Badge>
                        </div>
                      )}
                    </div>
                    {product.type === 'physical' && (
                      <div className="text-right">
                        <p className="text-sm font-medium text-muted-foreground">Tồn kho</p>
                        <p className="text-xl font-semibold">
                          {product.stockQuantity !== null && product.stockQuantity !== undefined
                            ? product.stockQuantity
                            : 'N/A'}
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                {product.shortDescription && (
                  <div className="pt-2 border-t">
                    <p className="text-sm font-medium text-muted-foreground">Mô tả ngắn</p>
                    <p>{product.shortDescription}</p>
                  </div>
                )}

                {product.tags && product.tags.length > 0 && (
                  <div className="pt-2 border-t">
                    <p className="text-sm font-medium text-muted-foreground mb-1">Tags</p>
                    <div className="flex flex-wrap gap-1">
                      {product.tags.map((tag, index) => (
                        <Badge key={index} variant="secondary">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Tabs defaultValue="details">
        <TabsList className="grid grid-cols-4">
          <TabsTrigger value="details" className="flex items-center gap-2">
            <Info className="h-4 w-4" />
            <span>Chi tiết</span>
          </TabsTrigger>
          <TabsTrigger value="attributes" className="flex items-center gap-2">
            <Tag className="h-4 w-4" />
            <span>Thuộc tính</span>
          </TabsTrigger>
          <TabsTrigger value="inventory" className="flex items-center gap-2">
            <Truck className="h-4 w-4" />
            <span>Kho hàng</span>
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart className="h-4 w-4" />
            <span>Phân tích</span>
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="details" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Mô tả chi tiết</CardTitle>
            </CardHeader>
            <CardContent>
              {product.description ? (
                <div className="prose max-w-none dark:prose-invert">
                  <p>{product.description}</p>
                </div>
              ) : (
                <p className="text-muted-foreground">Không có mô tả chi tiết.</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="attributes" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Thuộc tính sản phẩm</CardTitle>
            </CardHeader>
            <CardContent>
              {product.attributes && Object.keys(product.attributes).length > 0 ? (
                <div className="grid grid-cols-2 gap-4">
                  {Object.entries(product.attributes).map(([key, value]) => (
                    <div key={key}>
                      <p className="text-sm font-medium text-muted-foreground capitalize">{key}</p>
                      <p>{value}</p>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground">Không có thuộc tính nào.</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="inventory" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Thông tin kho hàng</CardTitle>
            </CardHeader>
            <CardContent>
              {product.type === 'physical' ? (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Số lượng tồn kho</p>
                    <p>{product.stockQuantity !== null && product.stockQuantity !== undefined ? product.stockQuantity : 'N/A'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Ngưỡng cảnh báo hết hàng</p>
                    <p>{product.lowStockThreshold || 'N/A'}</p>
                  </div>
                  {product.weight && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Trọng lượng</p>
                      <p>{product.weight} kg</p>
                    </div>
                  )}
                  {product.dimensions && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Kích thước</p>
                      <p>
                        {product.dimensions.length || '?'} x {product.dimensions.width || '?'} x {product.dimensions.height || '?'} {product.dimensions.unit || 'cm'}
                      </p>
                    </div>
                  )}
                </div>
              ) : (
                <p className="text-muted-foreground">Không áp dụng cho loại sản phẩm này.</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="analytics" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Phân tích hiệu suất</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-muted/50 p-4 rounded-lg">
                  <p className="text-sm font-medium text-muted-foreground">Lượt bán</p>
                  <p className="text-2xl font-bold">{product.salesCount || 0}</p>
                </div>
                <div className="bg-muted/50 p-4 rounded-lg">
                  <p className="text-sm font-medium text-muted-foreground">Lượt xem</p>
                  <p className="text-2xl font-bold">{product.viewCount || 0}</p>
                </div>
                <div className="bg-muted/50 p-4 rounded-lg">
                  <p className="text-sm font-medium text-muted-foreground">Đánh giá</p>
                  <p className="text-2xl font-bold">{product.rating || 0}/5</p>
                </div>
                <div className="bg-muted/50 p-4 rounded-lg">
                  <p className="text-sm font-medium text-muted-foreground">Số lượng đánh giá</p>
                  <p className="text-2xl font-bold">{product.reviewCount || 0}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
