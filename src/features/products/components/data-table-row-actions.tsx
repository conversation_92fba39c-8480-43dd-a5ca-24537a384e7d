import { Row } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { MoreHorizontal } from 'lucide-react'
import { useProductsContext } from '../context/use-products-context'
import { ProductStatus } from '../data/schema'

interface DataTableRowActionsProps<TData> {
  row: Row<TData>
  actions: {
    label: string
    action: string
    destructive?: boolean
    show?: boolean
  }[]
}

export function DataTableRowActions<TData>({
  row,
  actions,
}: DataTableRowActionsProps<TData>) {
  const {
    setOpenDialog,
    setSelectedProductIds,
    duplicateProduct,
    updateProductStatus,
  } = useProductsContext()

  const product = row.original as any
  const productId = product.id

  const handleAction = (action: string) => {
    switch (action) {
      case 'view':
        setSelectedProductIds([productId])
        setOpenDialog('view')
        break
      case 'edit':
        setSelectedProductIds([productId])
        setOpenDialog('edit')
        break
      case 'duplicate':
        duplicateProduct(productId)
        break
      case 'discontinue':
        updateProductStatus(productId, ProductStatus.DISCONTINUED)
        break
      case 'activate':
        updateProductStatus(productId, ProductStatus.ACTIVE)
        break
      case 'delete':
        setSelectedProductIds([productId])
        setOpenDialog('delete')
        break
      default:
        break
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
        >
          <MoreHorizontal className="h-4 w-4" />
          <span className="sr-only">Mở menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[160px]">
        {actions
          .filter((action) => action.show !== false)
          .map((action, index) => (
            <DropdownMenuItem
              key={index}
              onClick={() => handleAction(action.action)}
              className={action.destructive ? 'text-destructive focus:text-destructive' : ''}
            >
              {action.label}
            </DropdownMenuItem>
          ))}
        {actions.some((action) => action.destructive) && (
          <DropdownMenuSeparator />
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
