import { ColumnDef } from '@tanstack/react-table'
import { DataTable } from '@/components/ui/data-table'
import { useProductsContext } from '../context/use-products-context'

interface ProductsTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  onRowClick?: (row: TData) => void
}

export function ProductsTable<TData, TValue>({
  columns,
  data,
  onRowClick,
}: ProductsTableProps<TData, TValue>) {
  return (
    <DataTable
      columns={columns}
      data={data}
      searchKey="name"
      searchPlaceholder="Tìm kiếm sản phẩm..."
      onRowClick={onRowClick}
    />
  )
}
