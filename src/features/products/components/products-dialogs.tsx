import { useProductsContext } from '../context/use-products-context'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { ProductDetail } from './product-detail'

export function ProductsDialogs() {
  const {
    openDialog,
    setOpenDialog,
    selectedProductIds,
    deleteProduct,
    products,
  } = useProductsContext()

  const handleClose = () => {
    setOpenDialog('')
  }

  const handleDelete = () => {
    selectedProductIds.forEach((id) => {
      deleteProduct(id)
    })
    handleClose()
  }

  // L<PERSON>y thông tin sản phẩm đang được chọn
  const selectedProduct = selectedProductIds.length === 1
    ? products.find((product) => product.id === selectedProductIds[0])
    : null

  return (
    <>
      {/* Dialog xóa sản phẩm */}
      <Dialog open={openDialog === 'delete'} onOpenChange={handleClose}>
        <DialogContent className="max-w-md max-h-[90vh] h-auto">
          <DialogHeader>
            <DialogTitle>Xác nhận xóa</DialogTitle>
            <DialogDescription>
              {selectedProductIds.length > 1
                ? `Bạn có chắc chắn muốn xóa ${selectedProductIds.length} sản phẩm đã chọn không?`
                : 'Bạn có chắc chắn muốn xóa sản phẩm này không?'}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={handleClose}>
              Hủy
            </Button>
            <Button variant="destructive" onClick={handleDelete}>
              Xóa
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog xem chi tiết sản phẩm */}
      <Dialog open={openDialog === 'view'} onOpenChange={handleClose}>
        <DialogContent className="max-w-5xl max-h-[90vh] h-auto overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Chi tiết sản phẩm</DialogTitle>
          </DialogHeader>
          {selectedProduct && <ProductDetail product={selectedProduct} />}
          <DialogFooter>
            <Button variant="outline" onClick={handleClose}>
              Đóng
            </Button>
            <Button onClick={() => {
              handleClose()
              setOpenDialog('edit')
            }}>
              Chỉnh sửa
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog chỉnh sửa sản phẩm */}
      <Dialog open={openDialog === 'edit'} onOpenChange={handleClose}>
        <DialogContent className="max-w-5xl max-h-[90vh] h-auto overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Chỉnh sửa sản phẩm</DialogTitle>
            <DialogDescription>
              Chỉnh sửa thông tin sản phẩm
            </DialogDescription>
          </DialogHeader>
          {/* Form chỉnh sửa sản phẩm sẽ được thêm vào đây */}
          <DialogFooter>
            <Button variant="outline" onClick={handleClose}>
              Hủy
            </Button>
            <Button type="submit" form="edit-product-form">
              Lưu thay đổi
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
