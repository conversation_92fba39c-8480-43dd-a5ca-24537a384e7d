import { Product } from '../data/schema'
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>ooter, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Package, Eye, Copy, Archive, Trash2, Check } from 'lucide-react'
import { formatCurrency } from '@/utils/format'
import { useProductsContext } from '../context/use-products-context'
import { NameLink } from './name-link'

interface ProductsGridProps {
  products: Product[]
  onProductClick?: (product: Product) => void
}

export function ProductsGrid({ products, onProductClick }: ProductsGridProps) {
  const {
    setOpenDialog,
    setSelectedProductIds,
    duplicateProduct,
    updateProductStatus,
  } = useProductsContext()

  const handleView = (product: Product, e: React.MouseEvent) => {
    e.stopPropagation()
    setSelectedProductIds([product.id])
    setOpenDialog('view')
  }

  const handleEdit = (product: Product, e: React.MouseEvent) => {
    e.stopPropagation()
    setSelectedProductIds([product.id])
    setOpenDialog('edit')
  }

  const handleDuplicate = (product: Product, e: React.MouseEvent) => {
    e.stopPropagation()
    duplicateProduct(product.id)
  }

  const handleUpdateStatus = (product: Product, status: string, e: React.MouseEvent) => {
    e.stopPropagation()
    updateProductStatus(product.id, status)
  }

  const handleDelete = (product: Product, e: React.MouseEvent) => {
    e.stopPropagation()
    setSelectedProductIds([product.id])
    setOpenDialog('delete')
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {products.map((product) => (
        <Card
          key={product.id}
          className="cursor-pointer hover:border-primary/50 transition-colors"
          onClick={() => onProductClick && onProductClick(product)}
        >
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <div className="flex items-center gap-2">
                <div className="bg-primary/10 text-primary p-2 rounded-full">
                  <Package className="h-4 w-4" />
                </div>
                <div>
                  <h3 className="font-medium line-clamp-1">
                    <NameLink id={product.id} name={product.name} />
                  </h3>
                  <p className="text-xs text-muted-foreground">
                    SKU: {product.sku || 'N/A'}
                  </p>
                </div>
              </div>
              <Badge variant={
                product.status === 'active' ? 'default' :
                product.status === 'out_of_stock' ? 'outline' :
                product.status === 'discontinued' ? 'destructive' : 'secondary'
              } className="ml-2">
                {product.status === 'active' ? 'Hoạt động' :
                 product.status === 'inactive' ? 'Không hoạt động' :
                 product.status === 'out_of_stock' ? 'Hết hàng' : 'Ngừng kinh doanh'}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="aspect-video bg-muted rounded-md overflow-hidden mb-3">
              {product.thumbnailImage ? (
                <img
                  src={product.thumbnailImage}
                  alt={product.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <Package className="h-12 w-12 text-muted-foreground/50" />
                </div>
              )}
            </div>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm font-medium">Giá bán</p>
                  <p className="text-lg font-bold">
                    {formatCurrency(product.salePrice || product.price)}
                  </p>
                </div>
                {product.type === 'physical' && (
                  <div className="text-right">
                    <p className="text-sm font-medium">Tồn kho</p>
                    <p className="text-lg font-bold">
                      {product.stockQuantity !== null && product.stockQuantity !== undefined
                        ? product.stockQuantity
                        : 'N/A'}
                    </p>
                  </div>
                )}
              </div>
              <div className="flex flex-wrap gap-1 mt-2">
                <Badge variant="outline" className="text-xs">
                  {product.type === 'physical' ? 'Vật lý' :
                   product.type === 'digital' ? 'Số' :
                   product.type === 'service' ? 'Dịch vụ' :
                   product.type === 'subscription' ? 'Đăng ký' :
                   product.type === 'bundle' ? 'Gói' : product.type}
                </Badge>
                {product.category && (
                  <Badge variant="outline" className="text-xs">
                    {product.category}
                  </Badge>
                )}
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between pt-0">
            <div className="flex gap-1">
              <Button variant="ghost" size="icon" onClick={(e) => handleView(product, e)}>
                <Eye className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" onClick={(e) => handleEdit(product, e)}>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-pencil"><path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"/><path d="m15 5 4 4"/></svg>
              </Button>
              <Button variant="ghost" size="icon" onClick={(e) => handleDuplicate(product, e)}>
                <Copy className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex gap-1">
              {product.status !== 'discontinued' ? (
                <Button variant="ghost" size="icon" onClick={(e) => handleUpdateStatus(product, 'discontinued', e)}>
                  <Archive className="h-4 w-4" />
                </Button>
              ) : (
                <Button variant="ghost" size="icon" onClick={(e) => handleUpdateStatus(product, 'active', e)}>
                  <Check className="h-4 w-4" />
                </Button>
              )}
              <Button variant="ghost" size="icon" className="text-destructive" onClick={(e) => handleDelete(product, e)}>
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </CardFooter>
        </Card>
      ))}
    </div>
  )
}
