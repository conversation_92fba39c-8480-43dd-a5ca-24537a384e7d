import { ColumnDef } from '@tanstack/react-table'
import { Checkbox } from '@/components/ui/checkbox'
import { Product } from '../data/schema'
import { DataTableColumnHeader } from '@/components/ui/data-table'
import { DataTableRowActions } from './data-table-row-actions'
import { StatusBadge } from '@/components/ui/status-badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { formatCurrency } from '@/utils/format'
import { Badge } from '@/components/ui/badge'
import { Package } from 'lucide-react'
import { NameLink } from './name-link'

export const columns: ColumnDef<Product>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
        className="translate-y-[2px]"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
        className="translate-y-[2px]"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Tên sản phẩm" />
    ),
    cell: ({ row }) => {
      const product = row.original
      return (
        <div className="flex items-center gap-2">
          <Avatar className="h-8 w-8">
            <AvatarImage src={product.thumbnailImage} alt={product.name} />
            <AvatarFallback className="bg-primary/10 text-primary">
              <Package className="h-4 w-4" />
            </AvatarFallback>
          </Avatar>
          <NameLink id={product.id} name={product.name} />
        </div>
      )
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'sku',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Mã SKU" />
    ),
    cell: ({ row }) => {
      return (
        <div className="text-muted-foreground">
          {row.getValue('sku') || 'N/A'}
        </div>
      )
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'type',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Loại" />
    ),
    cell: ({ row }) => {
      const type = row.getValue('type') as string
      return (
        <Badge variant="outline" className="capitalize">
          {type === 'physical' ? 'Vật lý' :
           type === 'digital' ? 'Số' :
           type === 'service' ? 'Dịch vụ' :
           type === 'subscription' ? 'Đăng ký' :
           type === 'bundle' ? 'Gói' : type}
        </Badge>
      )
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'category',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Danh mục" />
    ),
    cell: ({ row }) => {
      const category = row.getValue('category') as string
      return (
        <div className="text-muted-foreground">
          {category || 'N/A'}
        </div>
      )
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'status',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Trạng thái" />
    ),
    cell: ({ row }) => {
      const status = row.getValue('status') as string
      return (
        <StatusBadge
          status={status === 'active' ? 'Hoạt động' :
                 status === 'inactive' ? 'Không hoạt động' :
                 status === 'out_of_stock' ? 'Hết hàng' :
                 status === 'discontinued' ? 'Ngừng kinh doanh' : status}
          variant={status === 'active' ? 'success' :
                 status === 'inactive' ? 'default' :
                 status === 'out_of_stock' ? 'warning' :
                 status === 'discontinued' ? 'destructive' : 'default'}
        />
      )
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'price',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Giá bán" />
    ),
    cell: ({ row }) => {
      const price = row.getValue('price') as number
      const salePrice = row.original.salePrice

      return (
        <div className="font-medium">
          {salePrice ? (
            <div>
              <span className="text-destructive">
                {formatCurrency(salePrice)}
              </span>
            </div>
          ) : (
            formatCurrency(price)
          )}
        </div>
      )
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'stockQuantity',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Tồn kho" />
    ),
    cell: ({ row }) => {
      const stockQuantity = row.getValue('stockQuantity') as number | null
      const type = row.original.type

      if (type === 'digital' || type === 'service' || type === 'subscription') {
        return <div>—</div>
      }

      return (
        <div>
          {stockQuantity === null ? 'N/A' : stockQuantity}
        </div>
      )
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'updatedAt',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Cập nhật" />
    ),
    cell: ({ row }) => {
      const updatedAt = row.original.updatedAt || row.original.createdAt
      const date = new Date(updatedAt)
      return (
        <div className="text-muted-foreground">
          {date.toLocaleDateString('vi-VN')}
        </div>
      )
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      const product = row.original
      return (
        <DataTableRowActions
          row={row}
          actions={[
            {
              label: 'Xem chi tiết',
              action: 'view',
            },
            {
              label: 'Chỉnh sửa',
              action: 'edit',
            },
            {
              label: 'Nhân bản',
              action: 'duplicate',
            },
            {
              label: 'Ngừng kinh doanh',
              action: 'discontinue',
              show: product.status !== 'discontinued',
            },
            {
              label: 'Kích hoạt',
              action: 'activate',
              show: product.status !== 'active',
            },
            {
              label: 'Xóa',
              action: 'delete',
              destructive: true,
            },
          ]}
        />
      )
    },
  },
]
