import { useState } from 'react'
import { useNavigate } from '@tanstack/react-router'
import { 
  IconArrowLeft, 
  IconEdit, 
  IconTrash, 
  IconCopy,
  IconPackage
} from '@tabler/icons-react'
import { Button } from '@/components/ui/button'
import { Product } from '../data/schema'
import { ProductDetail } from './product-detail'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { showSubmittedData } from '@/utils/show-submitted-data'

interface ProductDetailPageProps {
  product: Product
}

export function ProductDetailPage({ product }: ProductDetailPageProps) {
  const navigate = useNavigate()
  const [activeTab, setActiveTab] = useState('overview')

  const handleBack = () => {
    navigate({ to: '/products/' })
  }

  const handleEdit = () => {
    showSubmittedData({ message: 'Chỉnh sửa sản phẩm', product })
  }

  const handleDuplicate = () => {
    showSubmittedData({ message: '<PERSON>hân bản sản phẩm', product })
  }

  const handleDelete = () => {
    showSubmittedData({ message: 'Xóa sản phẩm', product })
  }

  return (
    <>
      <Header fixed>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <Button variant="outline" size="icon" onClick={handleBack}>
              <IconArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-xl font-bold">Chi tiết sản phẩm</h1>
          </div>

          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={handleDuplicate}>
              <IconCopy className="h-4 w-4 mr-2" />
              Nhân bản
            </Button>
            <Button variant="outline" onClick={handleEdit}>
              <IconEdit className="h-4 w-4 mr-2" />
              Chỉnh sửa
            </Button>
            <Button variant="destructive" onClick={handleDelete}>
              <IconTrash className="h-4 w-4 mr-2" />
              Xóa
            </Button>
          </div>
        </div>

        {/* Product Detail */}
        <ProductDetail product={product} />
      </Main>
    </>
  )
}
