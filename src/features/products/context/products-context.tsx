import { createContext, useState, ReactNode } from 'react'
import { Product } from '../data/schema'

// <PERSON><PERSON><PERSON> nghĩa kiểu dữ liệu cho view mode
export type ViewMode = 'table' | 'grid'

// Đ<PERSON>nh nghĩa kiểu dữ liệu cho context
export interface ProductsContextType {
  // Quản lý dialog
  openDialog: string
  setOpenDialog: (dialog: string) => void
  
  // Quản lý sản phẩm đ<PERSON><PERSON> chọn
  selectedProductIds: string[]
  setSelectedProductIds: (ids: string[]) => void
  
  // Quản lý chế độ xem
  viewMode: ViewMode
  setViewMode: (mode: ViewMode) => void
  
  // Quản lý bộ lọc
  showFilterPanel: boolean
  setShowFilterPanel: (show: boolean) => void
  
  // Quản lý dữ liệu sản phẩm
  products: Product[]
  setProducts: (products: Product[]) => void
  
  // Quản lý sản phẩm yêu thích
  favoriteProductIds: string[]
  toggleFavoriteProduct: (id: string) => void
  
  // Các hàm xử lý sản phẩm
  addProduct: (product: Product) => void
  updateProduct: (id: string, product: Partial<Product>) => void
  deleteProduct: (id: string) => void
  duplicateProduct: (id: string) => void
  updateProductStatus: (id: string, status: string) => void
}

// Tạo context
export const ProductsContext = createContext<ProductsContextType | undefined>(undefined)

// Props cho provider
interface ProductsProviderProps {
  children: ReactNode
  initialProducts: Product[]
}

// Provider component
export default function ProductsProvider({
  children,
  initialProducts,
}: ProductsProviderProps) {
  // State cho dialog
  const [openDialog, setOpenDialog] = useState('')
  
  // State cho sản phẩm được chọn
  const [selectedProductIds, setSelectedProductIds] = useState<string[]>([])
  
  // State cho chế độ xem
  const [viewMode, setViewMode] = useState<ViewMode>('table')
  
  // State cho bộ lọc
  const [showFilterPanel, setShowFilterPanel] = useState(false)
  
  // State cho dữ liệu sản phẩm
  const [products, setProducts] = useState<Product[]>(initialProducts)
  
  // State cho sản phẩm yêu thích
  const [favoriteProductIds, setFavoriteProductIds] = useState<string[]>([])
  
  // Toggle sản phẩm yêu thích
  const toggleFavoriteProduct = (id: string) => {
    setFavoriteProductIds((prev) => {
      if (prev.includes(id)) {
        return prev.filter((productId) => productId !== id)
      } else {
        return [...prev, id]
      }
    })
  }
  
  // Thêm sản phẩm mới
  const addProduct = (product: Product) => {
    setProducts((prev) => [...prev, product])
  }
  
  // Cập nhật sản phẩm
  const updateProduct = (id: string, product: Partial<Product>) => {
    setProducts((prev) =>
      prev.map((item) => (item.id === id ? { ...item, ...product, updatedAt: new Date().toISOString() } : item))
    )
  }
  
  // Xóa sản phẩm
  const deleteProduct = (id: string) => {
    setProducts((prev) => prev.filter((product) => product.id !== id))
  }
  
  // Nhân bản sản phẩm
  const duplicateProduct = (id: string) => {
    const product = products.find((product) => product.id === id)
    if (product) {
      const newProduct: Product = {
        ...product,
        id: `${Date.now()}`,
        name: `${product.name} (Bản sao)`,
        sku: product.sku ? `${product.sku}-COPY` : undefined,
        barcode: undefined,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        salesCount: 0,
        viewCount: 0
      }
      addProduct(newProduct)
    }
  }
  
  // Cập nhật trạng thái sản phẩm
  const updateProductStatus = (id: string, status: string) => {
    updateProduct(id, { status, updatedAt: new Date().toISOString() })
  }
  
  // Giá trị context
  const contextValue: ProductsContextType = {
    openDialog,
    setOpenDialog,
    selectedProductIds,
    setSelectedProductIds,
    viewMode,
    setViewMode,
    showFilterPanel,
    setShowFilterPanel,
    products,
    setProducts,
    favoriteProductIds,
    toggleFavoriteProduct,
    addProduct,
    updateProduct,
    deleteProduct,
    duplicateProduct,
    updateProductStatus
  }
  
  return (
    <ProductsContext.Provider value={contextValue}>
      {children}
    </ProductsContext.Provider>
  )
}
