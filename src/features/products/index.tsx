import { useState } from 'react'
import { useNavigate } from '@tanstack/react-router'
import {
  IconPlus,
  IconFilter,
  IconSearch,
  IconPackages
} from '@tabler/icons-react'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ViewModeToggle } from './components/view-mode-toggle'
import { ProductsTable } from './components/products-table'
import { ProductsGrid } from './components/products-grid'
import { ProductsDialogs } from './components/products-dialogs'
import ProductsProvider from './context/products-context'
import { useProductsContext } from './context/use-products-context'
import { products } from './data/products'
import { columns } from './components/products-columns'
import { Product } from './data/schema'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'

// Component nội dung chính
function ProductsContent() {
  const _navigate = useNavigate()
  const {
    viewMode,
    setViewMode,
    showFilterPanel,
    setShowFilterPanel,
    products,
    setOpenDialog,
    setSelectedProductIds,
  } = useProductsContext()

  const [searchQuery, setSearchQuery] = useState('')
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)

  // Lọc products theo từ khóa tìm kiếm
  const filteredProducts = products.filter((product) => {
    const query = searchQuery.toLowerCase()
    return (
      product.name.toLowerCase().includes(query) ||
      (product.sku && product.sku.toLowerCase().includes(query)) ||
      (product.description && product.description.toLowerCase().includes(query)) ||
      (product.shortDescription && product.shortDescription.toLowerCase().includes(query))
    )
  })

  // Xử lý khi click vào sản phẩm
  const handleProductClick = (product: Product) => {
    setSelectedProduct(product)
    setSelectedProductIds([product.id])
    setOpenDialog('view')
  }

  // Xử lý khi tạo sản phẩm mới
  const handleCreateProduct = () => {
    setOpenDialog('edit')
  }

  return (
    <>
      <Header fixed>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className="mb-4 flex flex-wrap items-center justify-between gap-4">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Sản phẩm</h2>
            <p className="text-muted-foreground">
              Quản lý danh mục sản phẩm và dịch vụ của công ty.
            </p>
          </div>
          <div className="flex items-center gap-2">
            <div className="relative w-64">
              <IconSearch className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Tìm kiếm sản phẩm..."
                className="w-full bg-background pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <ViewModeToggle />
            <Button
              variant="outline"
              size="sm"
              className="h-9 gap-1"
              onClick={() => setShowFilterPanel(!showFilterPanel)}
            >
              <IconFilter className="h-4 w-4" />
              <span className="hidden sm:inline">Bộ lọc</span>
            </Button>
            <Button
              size="sm"
              className="h-9 gap-1"
              onClick={handleCreateProduct}
            >
              <IconPlus className="h-4 w-4" />
              <span className="hidden sm:inline">Thêm sản phẩm</span>
            </Button>
          </div>
        </div>

        {showFilterPanel && (
          <Card className="mb-6">
            <CardHeader className="pb-3">
              <CardTitle>Bộ lọc</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Các bộ lọc sẽ được thêm vào đây */}
              </div>
            </CardContent>
          </Card>
        )}

        <div className="space-y-4">
          {viewMode === 'table' ? (
            <ProductsTable
              columns={columns}
              data={filteredProducts}
              onRowClick={handleProductClick}
            />
          ) : (
            <ProductsGrid
              products={filteredProducts}
              onProductClick={handleProductClick}
            />
          )}
        </div>
      </Main>

      <ProductsDialogs />
    </>
  )
}

// Component chính
export default function Products() {
  const [error, setError] = useState<Error | null>(null)

  // Xử lý lỗi khi parse dữ liệu
  if (error) {
    return (
      <div className="p-4">
        <h1 className="text-xl font-bold text-red-600">Lỗi khi tải dữ liệu</h1>
        <p className="mt-2">{error.message}</p>
      </div>
    )
  }

  return (
    <ProductsProvider initialProducts={products}>
      <ProductsContent />
    </ProductsProvider>
  )
}
