import { useState, useEffect } from 'react'
import { useParams, useNavigate } from '@tanstack/react-router'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'

import { Button } from '@/components/ui/button'
import { IconArrowLeft, IconEdit, IconTrash, IconMail, IconCalendarEvent, IconChartBar } from '@tabler/icons-react'
import { campaigns } from './data/campaigns'
import { Campaign, CampaignStatus } from './data/schema'
import { CampaignDetailView } from './components/campaign-detail-view'

export default function CampaignDetail() {
  const { id } = useParams({ from: '/_authenticated/campaigns/$id' })
  const navigate = useNavigate()
  const [campaign, setCampaign] = useState<Campaign | null>(null)
  const [activeTab, setActiveTab] = useState('overview')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Simulate API call
    setLoading(true)
    try {
      const foundCampaign = campaigns.find(c => c.id === id)
      if (foundCampaign) {
        setCampaign(foundCampaign)
      } else {
        setError('Không tìm thấy chiến dịch')
      }
    } catch (err) {
      setError('Có lỗi xảy ra khi tải dữ liệu')
    } finally {
      setLoading(false)
    }
  }, [id])

  // Xử lý các hành động chiến dịch
  const handleEdit = () => {
    // Xử lý chỉnh sửa chiến dịch
    console.log('Edit campaign:', campaign?.id)
  }

  const handleDelete = () => {
    // Xử lý xóa chiến dịch
    console.log('Delete campaign:', campaign?.id)
    navigate({ to: '/campaigns' })
  }

  const handleStart = () => {
    // Xử lý bắt đầu chiến dịch
    console.log('Start campaign:', campaign?.id)
  }

  const handlePause = () => {
    // Xử lý tạm dừng chiến dịch
    console.log('Pause campaign:', campaign?.id)
  }

  const handleResume = () => {
    // Xử lý tiếp tục chiến dịch
    console.log('Resume campaign:', campaign?.id)
  }

  const handleComplete = () => {
    // Xử lý hoàn thành chiến dịch
    console.log('Complete campaign:', campaign?.id)
  }

  const handleCancel = () => {
    // Xử lý hủy chiến dịch
    console.log('Cancel campaign:', campaign?.id)
  }

  const handleBack = () => {
    navigate({ to: '/campaigns' })
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4">Đang tải...</p>
        </div>
      </div>
    )
  }

  if (error || !campaign) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">{error || 'Không tìm thấy chiến dịch'}</h2>
          <Button onClick={handleBack}>Quay lại danh sách</Button>
        </div>
      </div>
    )
  }

  return (
    <>
      <Header fixed>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <CampaignDetailView
          campaign={campaign}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onBack={handleBack}
          onStart={handleStart}
          onPause={handlePause}
          onResume={handleResume}
          onComplete={handleComplete}
          onCancel={handleCancel}
        />
      </Main>
    </>
  )
}
