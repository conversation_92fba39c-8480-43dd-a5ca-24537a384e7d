import { useState, ReactNode } from 'react'
import { CampaignsContext, ViewMode } from './campaigns-context-type'
import { Campaign, CampaignStatus } from '../data/schema'

interface CampaignsProviderProps {
  children: ReactNode
  initialCampaigns: Campaign[]
}

export default function CampaignsProvider({
  children,
  initialCampaigns,
}: CampaignsProviderProps) {
  // State cho dialog
  const [openDialog, setOpenDialog] = useState('')
  
  // State cho chiến dịch được chọn
  const [selectedCampaignIds, setSelectedCampaignIds] = useState<string[]>([])
  
  // State cho chế độ xem
  const [viewMode, setViewMode] = useState<ViewMode>('table')
  
  // State cho bộ lọc
  const [showFilterPanel, setShowFilterPanel] = useState(false)
  
  // State cho dữ liệu chiến dịch
  const [campaigns, setCampaigns] = useState<Campaign[]>(initialCampaigns)
  
  // State cho chiến dịch yêu thích
  const [favoriteCampaignIds, setFavoriteCampaignIds] = useState<string[]>([])
  
  // Hàm toggle yêu thích
  const toggleFavoriteCampaign = (id: string) => {
    setFavoriteCampaignIds(prev => 
      prev.includes(id) 
        ? prev.filter(campaignId => campaignId !== id) 
        : [...prev, id]
    )
  }
  
  // Hàm cập nhật trạng thái
  const updateCampaignStatus = (campaignId: string, newStatus: string) => {
    setCampaigns(prevCampaigns =>
      prevCampaigns.map(campaign =>
        campaign.id === campaignId 
          ? { 
              ...campaign, 
              status: newStatus,
              updatedAt: new Date().toISOString()
            } 
          : campaign
      )
    )
  }
  
  // Hàm thêm chiến dịch mới
  const addCampaign = (campaign: Campaign) => {
    setCampaigns(prev => [...prev, campaign])
  }
  
  // Hàm cập nhật chiến dịch
  const updateCampaign = (campaign: Campaign) => {
    setCampaigns(prev => 
      prev.map(c => c.id === campaign.id ? campaign : c)
    )
  }
  
  // Hàm xóa chiến dịch
  const deleteCampaign = (campaignId: string) => {
    setCampaigns(prev => 
      prev.filter(c => c.id !== campaignId)
    )
    
    // Xóa khỏi danh sách yêu thích nếu có
    setFavoriteCampaignIds(prev => 
      prev.filter(id => id !== campaignId)
    )
    
    // Xóa khỏi danh sách đã chọn nếu có
    setSelectedCampaignIds(prev => 
      prev.filter(id => id !== campaignId)
    )
  }
  
  // Hàm bắt đầu chiến dịch
  const startCampaign = (campaignId: string) => {
    setCampaigns(prev => 
      prev.map(campaign => {
        if (campaign.id === campaignId) {
          return {
            ...campaign,
            status: CampaignStatus.RUNNING,
            updatedAt: new Date().toISOString(),
            activities: [
              ...(campaign.activities || []),
              {
                id: `act-${Date.now()}`,
                type: 'system',
                title: 'Chiến dịch đã bắt đầu',
                date: new Date().toISOString(),
              }
            ]
          }
        }
        return campaign
      })
    )
  }
  
  // Hàm tạm dừng chiến dịch
  const pauseCampaign = (campaignId: string) => {
    setCampaigns(prev => 
      prev.map(campaign => {
        if (campaign.id === campaignId) {
          return {
            ...campaign,
            status: CampaignStatus.PAUSED,
            updatedAt: new Date().toISOString(),
            activities: [
              ...(campaign.activities || []),
              {
                id: `act-${Date.now()}`,
                type: 'system',
                title: 'Chiến dịch đã tạm dừng',
                date: new Date().toISOString(),
              }
            ]
          }
        }
        return campaign
      })
    )
  }
  
  // Hàm tiếp tục chiến dịch
  const resumeCampaign = (campaignId: string) => {
    setCampaigns(prev => 
      prev.map(campaign => {
        if (campaign.id === campaignId) {
          return {
            ...campaign,
            status: CampaignStatus.RUNNING,
            updatedAt: new Date().toISOString(),
            activities: [
              ...(campaign.activities || []),
              {
                id: `act-${Date.now()}`,
                type: 'system',
                title: 'Chiến dịch đã tiếp tục',
                date: new Date().toISOString(),
              }
            ]
          }
        }
        return campaign
      })
    )
  }
  
  // Hàm hoàn thành chiến dịch
  const completeCampaign = (campaignId: string) => {
    setCampaigns(prev => 
      prev.map(campaign => {
        if (campaign.id === campaignId) {
          return {
            ...campaign,
            status: CampaignStatus.COMPLETED,
            updatedAt: new Date().toISOString(),
            endDate: new Date().toISOString().split('T')[0],
            activities: [
              ...(campaign.activities || []),
              {
                id: `act-${Date.now()}`,
                type: 'system',
                title: 'Chiến dịch đã hoàn thành',
                date: new Date().toISOString(),
              }
            ]
          }
        }
        return campaign
      })
    )
  }
  
  // Hàm hủy chiến dịch
  const cancelCampaign = (campaignId: string) => {
    setCampaigns(prev => 
      prev.map(campaign => {
        if (campaign.id === campaignId) {
          return {
            ...campaign,
            status: CampaignStatus.CANCELLED,
            updatedAt: new Date().toISOString(),
            activities: [
              ...(campaign.activities || []),
              {
                id: `act-${Date.now()}`,
                type: 'system',
                title: 'Chiến dịch đã bị hủy',
                date: new Date().toISOString(),
              }
            ]
          }
        }
        return campaign
      })
    )
  }
  
  // Giá trị context
  const contextValue = {
    openDialog,
    setOpenDialog,
    selectedCampaignIds,
    setSelectedCampaignIds,
    viewMode,
    setViewMode,
    showFilterPanel,
    setShowFilterPanel,
    campaigns,
    setCampaigns,
    updateCampaignStatus,
    addCampaign,
    updateCampaign,
    deleteCampaign,
    startCampaign,
    pauseCampaign,
    resumeCampaign,
    completeCampaign,
    cancelCampaign,
    favoriteCampaignIds,
    toggleFavoriteCampaign
  }
  
  return (
    <CampaignsContext.Provider value={contextValue}>
      {children}
    </CampaignsContext.Provider>
  )
}
