import { createContext } from 'react'
import { Campaign } from '../data/schema'

export type ViewMode = 'table' | 'kanban' | 'calendar' | 'analytics'

export type CampaignsContextType = {
  // Quản lý dialog
  openDialog: string
  setOpenDialog: (dialog: string) => void
  
  // Quản lý chiến dịch được chọn
  selectedCampaignIds: string[]
  setSelectedCampaignIds: (ids: string[]) => void
  
  // Quản lý chế độ xem
  viewMode: ViewMode
  setViewMode: (mode: ViewMode) => void
  
  // Quản lý bộ lọc
  showFilterPanel: boolean
  setShowFilterPanel: (show: boolean) => void
  
  // Quản lý dữ liệu chiến dịch
  campaigns: Campaign[]
  setCampaigns: (campaigns: Campaign[]) => void
  
  // Các hàm cập nhật chiến dịch
  updateCampaignStatus: (campaignId: string, newStatus: string) => void
  addCampaign: (campaign: Campaign) => void
  updateCampaign: (campaign: Campaign) => void
  deleteCampaign: (campaignId: string) => void
  
  // Cá<PERSON> hàm đặc biệt cho chiến dịch
  startCampaign: (campaignId: string) => void
  pauseCampaign: (campaignId: string) => void
  resumeCampaign: (campaignId: string) => void
  completeCampaign: (campaignId: string) => void
  cancelCampaign: (campaignId: string) => void
  
  // Quản lý chiến dịch yêu thích
  favoriteCampaignIds: string[]
  toggleFavoriteCampaign: (id: string) => void
}

export const CampaignsContext = createContext<CampaignsContextType | null>(null)
