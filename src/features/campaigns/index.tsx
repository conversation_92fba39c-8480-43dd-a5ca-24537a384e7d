import { useState, useEffect } from 'react'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  IconChartBar,
  IconCalendarEvent,
  IconSearch,
  IconFilter,
  IconPlus
} from '@tabler/icons-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { columns } from './components/campaigns-columns'
import { CampaignsTable } from './components/campaigns-table'
import { ViewModeToggle } from './components/view-mode-toggle'
import { CampaignsDialogs } from './components/campaigns-dialogs'
import CampaignsProvider from './context/campaigns-context'
import { useCampaignsContext } from './context/use-campaigns-context'
import { campaignListSchema, CampaignStatus } from './data/schema'
import { campaigns } from './data/campaigns'
import { formatCurrency } from '@/utils/format'

// Component nội dung chính
function CampaignsContent() {
  const {
    viewMode,
    showFilterPanel,
    campaigns,
    setViewMode,
    setOpenDialog,
    setShowFilterPanel
  } = useCampaignsContext()

  const [searchTerm, setSearchTerm] = useState('')

  // Lọc chiến dịch theo từ khóa tìm kiếm
  const filteredCampaigns = campaigns.filter((campaign) => {
    const query = searchTerm.toLowerCase()
    return (
      campaign.name.toLowerCase().includes(query) ||
      campaign.type.toLowerCase().includes(query) ||
      campaign.owner?.toLowerCase().includes(query) ||
      campaign.description?.toLowerCase().includes(query)
    )
  })

  // Tính tổng số chiến dịch theo trạng thái
  const totalRunning = campaigns.filter(c => c.status === CampaignStatus.RUNNING).length
  const totalScheduled = campaigns.filter(c => c.status === CampaignStatus.SCHEDULED).length
  const totalCompleted = campaigns.filter(c => c.status === CampaignStatus.COMPLETED).length

  // Tính tổng ngân sách
  const totalBudget = campaigns.reduce((total, c) => total + (c.budget || 0), 0)

  // Xử lý khi tạo chiến dịch mới
  const handleCreateCampaign = () => {
    setOpenDialog('create')
  }

  return (
    <>
      <Header fixed>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className="mb-6">
          <div className="mb-4">
            <h2 className="text-2xl font-bold tracking-tight">Chiến dịch</h2>
            <p className="text-muted-foreground">
              Quản lý và theo dõi các chiến dịch email marketing.
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row justify-between gap-4">
            {/* Search bar - Left side */}
            <div className="w-full sm:w-80">
              <div className="relative">
                <IconSearch className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Tìm kiếm chiến dịch..."
                  className="w-full bg-background pl-9 h-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            
            {/* Action buttons - Right side */}
            <div className="flex items-center gap-2">
              <ViewModeToggle viewMode={viewMode} setViewMode={setViewMode} />
              <Button
                variant="outline"
                size="sm"
                className="h-10 gap-1.5 px-3"
                onClick={() => setShowFilterPanel(!showFilterPanel)}
              >
                <IconFilter className="h-4 w-4" />
                <span className="hidden sm:inline">Bộ lọc</span>
              </Button>
              <Button
                size="sm"
                className="h-10 gap-1.5 px-4"
                onClick={handleCreateCampaign}
              >
                <IconPlus className="h-4 w-4" />
                <span className="hidden sm:inline">Tạo chiến dịch</span>
              </Button>
            </div>
          </div>
        </div>

        {/* Dashboard tổng quan */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Tổng ngân sách</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(totalBudget)}</div>
              <p className="text-xs text-muted-foreground mt-1">
                {campaigns.length} chiến dịch
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Đang chạy</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalRunning}</div>
              <p className="text-xs text-muted-foreground mt-1">
                chiến dịch đang hoạt động
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Đã lên lịch</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalScheduled}</div>
              <p className="text-xs text-muted-foreground mt-1">
                chiến dịch sắp diễn ra
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Đã hoàn thành</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalCompleted}</div>
              <p className="text-xs text-muted-foreground mt-1">
                chiến dịch đã kết thúc
              </p>
            </CardContent>
          </Card>
        </div>

        {showFilterPanel && (
          <Card className="mb-6">
            <CardHeader className="pb-3">
              <CardTitle>Bộ lọc</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Các bộ lọc sẽ được thêm vào đây */}
              </div>
            </CardContent>
          </Card>
        )}

        <div className="space-y-4">
          {viewMode === 'table' && (
            <CampaignsTable data={filteredCampaigns} columns={columns} />
          )}

          {viewMode === 'kanban' && (
            <div className="text-center py-20 text-muted-foreground">
              <IconChartBar className="h-16 w-16 mx-auto mb-4 opacity-20" />
              <h3 className="text-lg font-medium mb-2">Chế độ xem Kanban đang được phát triển</h3>
              <p className="max-w-md mx-auto">
                Chế độ xem Kanban đang được phát triển và sẽ sớm được cập nhật.
              </p>
            </div>
          )}

          {viewMode === 'calendar' && (
            <div className="text-center py-20 text-muted-foreground">
              <IconCalendarEvent className="h-16 w-16 mx-auto mb-4 opacity-20" />
              <h3 className="text-lg font-medium mb-2">Chế độ xem Lịch đang được phát triển</h3>
              <p className="max-w-md mx-auto">
                Chế độ xem Lịch đang được phát triển và sẽ sớm được cập nhật.
              </p>
            </div>
          )}

          {viewMode === 'analytics' && (
            <div className="text-center py-20 text-muted-foreground">
              <IconChartBar className="h-16 w-16 mx-auto mb-4 opacity-20" />
              <h3 className="text-lg font-medium mb-2">Chế độ xem Phân tích đang được phát triển</h3>
              <p className="max-w-md mx-auto">
                Chế độ xem Phân tích đang được phát triển và sẽ sớm được cập nhật.
              </p>
            </div>
          )}
        </div>
      </Main>

      <CampaignsDialogs />
    </>
  )
}

// Component chính
export default function Campaigns() {
  const [error, setError] = useState<Error | null>(null)

  // Xử lý lỗi khi parse dữ liệu
  useEffect(() => {
    try {
      // Validate dữ liệu
      campaignListSchema.parse(campaigns)
    } catch (err) {
      setError(err as Error)
    }
  }, [])

  // Hiển thị lỗi nếu có
  if (error) {
    return (
      <div className="p-4">
        <h1 className="text-xl font-bold text-red-600">Lỗi khi tải dữ liệu</h1>
        <p className="mt-2">{error.message}</p>
      </div>
    )
  }

  return (
    <CampaignsProvider initialCampaigns={campaigns}>
      <CampaignsContent />
    </CampaignsProvider>
  )
}
