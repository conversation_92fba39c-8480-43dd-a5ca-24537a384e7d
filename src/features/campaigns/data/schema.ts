import { z } from 'zod'

// Định nghĩa các enum cho Campaign
export const CampaignStatus = {
  DRAFT: 'draft',           // Bản nháp
  SCHEDULED: 'scheduled',   // Đ<PERSON> lên lịch
  RUNNING: 'running',       // <PERSON>ang chạy
  PAUSED: 'paused',         // Tạm dừng
  COMPLETED: 'completed',   // Hoàn thành
  CANCELLED: 'cancelled'    // Đã hủy
} as const

export const CampaignType = {
  EMAIL: 'email',               // Email marketing
  SOCIAL: 'social',             // Social media
  SMS: 'sms',                   // SMS
  OMNICHANNEL: 'omnichannel',   // <PERSON><PERSON> kênh (Messenger, Zalo, etc.)
  EVENT: 'event',               // Sự kiện
  WEBINAR: 'webinar',           // Hội thảo trực tuyến
  DIRECT_MAIL: 'direct_mail',   // Gửi thư trực tiếp
  OTHER: 'other'                // Khá<PERSON>
} as const

export const CampaignPriority = {
  LOW: 'low',       // Thấp
  MEDIUM: 'medium', // Trung bình
  HIGH: 'high'      // Cao
} as const

export const CampaignChannel = {
  EMAIL: 'email',           // Email
  FACEBOOK: 'facebook',     // Facebook
  MESSENGER: 'messenger',   // Messenger
  ZALO: 'zalo',             // Zalo
  SMS: 'sms',               // SMS
  WHATSAPP: 'whatsapp',     // WhatsApp
  TELEGRAM: 'telegram',     // Telegram
  WEBSITE: 'website',       // Website
  MOBILE_APP: 'mobile_app', // Mobile App
  INSTAGRAM: 'instagram',   // Instagram
  TWITTER: 'twitter',       // Twitter
  LINKEDIN: 'linkedin',     // LinkedIn
  OTHER: 'other'            // Khác
} as const

// Schema cho Campaign
export const campaignSchema = z.object({
  id: z.string(),
  name: z.string().min(1, "Tên chiến dịch không được để trống"),
  type: z.string(), // Sử dụng các giá trị từ CampaignType
  status: z.string(), // Sử dụng các giá trị từ CampaignStatus
  priority: z.string().optional(), // Sử dụng các giá trị từ CampaignPriority
  channels: z.array(z.string()).optional(), // Sử dụng các giá trị từ CampaignChannel
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  budget: z.number().nonnegative().optional(),
  actualCost: z.number().nonnegative().optional(),
  targetAudience: z.string().optional(),
  description: z.string().optional(),
  goals: z.string().optional(),
  expectedROI: z.number().optional(),
  owner: z.string().optional(),
  createdBy: z.string().optional(),
  createdAt: z.string(),
  updatedAt: z.string().optional(),
  tags: z.array(z.string()).optional(),
  
  // Thông tin chi tiết về nội dung chiến dịch
  content: z.object({
    subject: z.string().optional(), // Tiêu đề (cho email, tin nhắn)
    body: z.string().optional(), // Nội dung chính
    template: z.string().optional(), // Mẫu được sử dụng
    attachments: z.array(z.object({
      id: z.string(),
      name: z.string(),
      type: z.string(),
      url: z.string(),
      size: z.number().optional(),
    })).optional(),
    callToAction: z.string().optional(), // Nút CTA
    landingPage: z.string().optional(), // Trang đích
  }).optional(),
  
  // Thông tin về đối tượng mục tiêu
  audience: z.object({
    segments: z.array(z.string()).optional(), // Các phân khúc khách hàng
    totalContacts: z.number().optional(), // Tổng số liên hệ
    filters: z.record(z.any()).optional(), // Các bộ lọc được áp dụng
  }).optional(),
  
  // Thông tin về hiệu suất chiến dịch
  performance: z.object({
    sent: z.number().optional(), // Số lượng đã gửi
    delivered: z.number().optional(), // Số lượng đã nhận
    opened: z.number().optional(), // Số lượng đã mở (email)
    clicked: z.number().optional(), // Số lượng đã click
    responded: z.number().optional(), // Số lượng đã phản hồi
    converted: z.number().optional(), // Số lượng đã chuyển đổi
    bounced: z.number().optional(), // Số lượng bị trả lại
    unsubscribed: z.number().optional(), // Số lượng hủy đăng ký
    openRate: z.number().optional(), // Tỷ lệ mở
    clickRate: z.number().optional(), // Tỷ lệ click
    conversionRate: z.number().optional(), // Tỷ lệ chuyển đổi
    roi: z.number().optional(), // ROI thực tế
  }).optional(),
  
  // Các hoạt động liên quan đến chiến dịch
  activities: z.array(z.object({
    id: z.string(),
    type: z.enum(['note', 'task', 'email', 'call', 'meeting', 'system']),
    title: z.string(),
    description: z.string().optional(),
    date: z.string(),
    user: z.string().optional(),
    status: z.string().optional(),
    dueDate: z.string().optional(),
  })).optional(),
  
  // Các nhiệm vụ liên quan đến chiến dịch
  tasks: z.array(z.object({
    id: z.string(),
    title: z.string(),
    description: z.string().optional(),
    assignedTo: z.string().optional(),
    dueDate: z.string().optional(),
    status: z.string().optional(),
    priority: z.string().optional(),
  })).optional(),
  
  // Các cơ hội được tạo ra từ chiến dịch
  opportunities: z.array(z.string()).optional(), // IDs của các cơ hội
  
  // Các leads được tạo ra từ chiến dịch
  leads: z.array(z.string()).optional(), // IDs của các leads
})

export const campaignListSchema = z.array(campaignSchema)

// Types
export type CampaignStatusType = typeof CampaignStatus[keyof typeof CampaignStatus]
export type CampaignTypeType = typeof CampaignType[keyof typeof CampaignType]
export type CampaignPriorityType = typeof CampaignPriority[keyof typeof CampaignPriority]
export type CampaignChannelType = typeof CampaignChannel[keyof typeof CampaignChannel]
export type Campaign = z.infer<typeof campaignSchema>
