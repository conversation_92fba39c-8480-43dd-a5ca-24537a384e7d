import { Row } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  IconDots,
  IconEdit,
  IconTrash,
  IconPlayerPlay,
  IconPlayerPause,
  IconCheck,
  IconX,
  IconEye,
  IconCopy,
  IconStar,
  IconStarFilled,
} from '@tabler/icons-react'
import { useCampaignsContext } from '../context/use-campaigns-context'
import { Campaign, CampaignStatus } from '../data/schema'

interface DataTableRowActionsProps<TData> {
  row: Row<TData>
}

export function DataTableRowActions<TData>({
  row,
}: DataTableRowActionsProps<TData>) {
  const campaign = row.original as Campaign
  
  const {
    setOpenDialog,
    setSelectedCampaignIds,
    startCampaign,
    pauseCampaign,
    resumeCampaign,
    completeCampaign,
    cancelCampaign,
    favoriteCampaignIds,
    toggleFavoriteCampaign,
  } = useCampaignsContext()

  const handleEdit = () => {
    setSelectedCampaignIds([campaign.id])
    setOpenDialog('edit')
  }

  const handleDelete = () => {
    setSelectedCampaignIds([campaign.id])
    setOpenDialog('delete')
  }

  const handleView = () => {
    setSelectedCampaignIds([campaign.id])
  }

  const handleDuplicate = () => {
    setSelectedCampaignIds([campaign.id])
    setOpenDialog('duplicate')
  }

  const handleStart = () => {
    startCampaign(campaign.id)
  }

  const handlePause = () => {
    pauseCampaign(campaign.id)
  }

  const handleResume = () => {
    resumeCampaign(campaign.id)
  }

  const handleComplete = () => {
    setSelectedCampaignIds([campaign.id])
    setOpenDialog('complete')
  }

  const handleCancel = () => {
    setSelectedCampaignIds([campaign.id])
    setOpenDialog('cancel')
  }

  const handleFavoriteToggle = () => {
    toggleFavoriteCampaign(campaign.id)
  }

  const isFavorite = favoriteCampaignIds.includes(campaign.id)

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Mở menu</span>
          <IconDots className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={handleView}>
          <IconEye className="mr-2 h-4 w-4" />
          Xem chi tiết
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleEdit}>
          <IconEdit className="mr-2 h-4 w-4" />
          Chỉnh sửa
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleDuplicate}>
          <IconCopy className="mr-2 h-4 w-4" />
          Nhân bản
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleFavoriteToggle}>
          {isFavorite ? (
            <>
              <IconStarFilled className="mr-2 h-4 w-4 text-yellow-400" />
              Bỏ yêu thích
            </>
          ) : (
            <>
              <IconStar className="mr-2 h-4 w-4" />
              Yêu thích
            </>
          )}
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        {/* Các hành động dựa trên trạng thái */}
        {campaign.status === CampaignStatus.DRAFT && (
          <DropdownMenuItem onClick={handleStart}>
            <IconPlayerPlay className="mr-2 h-4 w-4" />
            Bắt đầu
          </DropdownMenuItem>
        )}
        
        {campaign.status === CampaignStatus.SCHEDULED && (
          <DropdownMenuItem onClick={handleStart}>
            <IconPlayerPlay className="mr-2 h-4 w-4" />
            Bắt đầu ngay
          </DropdownMenuItem>
        )}
        
        {campaign.status === CampaignStatus.RUNNING && (
          <DropdownMenuItem onClick={handlePause}>
            <IconPlayerPause className="mr-2 h-4 w-4" />
            Tạm dừng
          </DropdownMenuItem>
        )}
        
        {campaign.status === CampaignStatus.PAUSED && (
          <DropdownMenuItem onClick={handleResume}>
            <IconPlayerPlay className="mr-2 h-4 w-4" />
            Tiếp tục
          </DropdownMenuItem>
        )}
        
        {(campaign.status === CampaignStatus.RUNNING || 
          campaign.status === CampaignStatus.PAUSED) && (
          <DropdownMenuItem onClick={handleComplete}>
            <IconCheck className="mr-2 h-4 w-4" />
            Hoàn thành
          </DropdownMenuItem>
        )}
        
        {(campaign.status === CampaignStatus.DRAFT || 
          campaign.status === CampaignStatus.SCHEDULED || 
          campaign.status === CampaignStatus.RUNNING || 
          campaign.status === CampaignStatus.PAUSED) && (
          <DropdownMenuItem onClick={handleCancel}>
            <IconX className="mr-2 h-4 w-4" />
            Hủy
          </DropdownMenuItem>
        )}
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem onClick={handleDelete} className="text-red-600">
          <IconTrash className="mr-2 h-4 w-4" />
          Xóa
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
