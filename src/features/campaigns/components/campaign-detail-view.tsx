import { useState } from 'react'
import { format } from 'date-fns'
import { vi } from 'date-fns/locale'
import {
  IconEdit,
  IconTrash,
  IconArrowBack,
  IconCalendar,
  IconUser,
  IconFileText,
  IconClock,
  IconMail,
  IconBrandFacebook,
  IconBrandInstagram,
  IconBrandTwitter,
  IconBrandLinkedin,
  IconChartBar,
  IconLink,
  IconPlayerPause,
  IconPlayerPlay,
  IconCheck,
  IconX,
  IconChevronDown
} from '@tabler/icons-react'
import { cn } from '@/lib/utils'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Campaign, CampaignStatus, CampaignType, CampaignChannel } from '../data/schema'
import { formatCurrency } from '@/utils/format'
// Các component tab sẽ được thêm sau

interface CampaignDetailViewProps {
  campaign: Campaign
  onEdit: () => void
  onDelete: () => void
  onBack: () => void
  onStart: () => void
  onPause: () => void
  onResume: () => void
  onComplete: () => void
  onCancel: () => void
}

// Hàm lấy chữ cái đầu của tên
function getInitials(name: string): string {
  return name
    .split(' ')
    .map(part => part.charAt(0))
    .join('')
    .toUpperCase()
    .substring(0, 2)
}

// Hàm lấy icon cho kênh chiến dịch
function getChannelIcon(channel: string) {
  switch (channel) {
    case CampaignChannel.EMAIL:
      return <IconMail className="h-4 w-4" />
    case CampaignChannel.FACEBOOK:
      return <IconBrandFacebook className="h-4 w-4" />
    case CampaignChannel.INSTAGRAM:
      return <IconBrandInstagram className="h-4 w-4" />
    case CampaignChannel.TWITTER:
      return <IconBrandTwitter className="h-4 w-4" />
    case CampaignChannel.LINKEDIN:
      return <IconBrandLinkedin className="h-4 w-4" />
    default:
      return <IconLink className="h-4 w-4" />
  }
}

// Hàm lấy màu cho trạng thái
function getStatusColor(status: string) {
  switch (status) {
    case CampaignStatus.DRAFT:
      return "bg-muted text-muted-foreground border-border"
    case CampaignStatus.SCHEDULED:
      return "bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800"
    case CampaignStatus.RUNNING:
      return "bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300 dark:border-green-800"
    case CampaignStatus.PAUSED:
      return "bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-950 dark:text-yellow-300 dark:border-yellow-800"
    case CampaignStatus.COMPLETED:
      return "bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-950 dark:text-purple-300 dark:border-purple-800"
    case CampaignStatus.CANCELLED:
      return "bg-destructive/10 text-destructive border-destructive/20"
    default:
      return "bg-muted text-muted-foreground border-border"
  }
}

// Hàm lấy tên hiển thị cho trạng thái
function getStatusLabel(status: string) {
  switch (status) {
    case CampaignStatus.DRAFT:
      return "Bản nháp"
    case CampaignStatus.SCHEDULED:
      return "Đã lên lịch"
    case CampaignStatus.RUNNING:
      return "Đang chạy"
    case CampaignStatus.PAUSED:
      return "Tạm dừng"
    case CampaignStatus.COMPLETED:
      return "Hoàn thành"
    case CampaignStatus.CANCELLED:
      return "Đã hủy"
    default:
      return status
  }
}

export function CampaignDetailView({
  campaign,
  onEdit,
  onDelete,
  onBack,
  onStart,
  onPause,
  onResume,
  onComplete,
  onCancel
}: CampaignDetailViewProps) {
  const [activeTab, setActiveTab] = useState('overview')

  // Tính toán tiến độ chiến dịch
  const calculateProgress = () => {
    if (campaign.status === CampaignStatus.COMPLETED) return 100
    if (campaign.status === CampaignStatus.CANCELLED) return 0
    if (!campaign.startDate || !campaign.endDate) return 0

    const start = new Date(campaign.startDate).getTime()
    const end = new Date(campaign.endDate).getTime()
    const now = new Date().getTime()

    if (now <= start) return 0
    if (now >= end) return 100

    const total = end - start
    const current = now - start
    return Math.round((current / total) * 100)
  }

  const progress = calculateProgress()

  // Kiểm tra các hành động có thể thực hiện
  const canStart = campaign.status === CampaignStatus.DRAFT || campaign.status === CampaignStatus.SCHEDULED
  const canPause = campaign.status === CampaignStatus.RUNNING
  const canResume = campaign.status === CampaignStatus.PAUSED
  const canComplete = campaign.status === CampaignStatus.RUNNING || campaign.status === CampaignStatus.PAUSED
  const canCancel = campaign.status !== CampaignStatus.COMPLETED && campaign.status !== CampaignStatus.CANCELLED

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" onClick={onBack}>
            <IconArrowBack className="h-4 w-4" />
          </Button>
          <h1 className="text-xl font-bold">Chi tiết chiến dịch</h1>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={onEdit}>
            <IconEdit className="h-4 w-4 mr-2" />
            Chỉnh sửa
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                Thao tác
                <IconChevronDown className="h-4 w-4 ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Thao tác chiến dịch</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {canStart && (
                <DropdownMenuItem onClick={onStart} className="text-green-700 dark:text-green-300">
                  <IconPlayerPlay className="h-4 w-4 mr-2" />
                  Bắt đầu chiến dịch
                </DropdownMenuItem>
              )}
              {canPause && (
                <DropdownMenuItem onClick={onPause} className="text-yellow-700 dark:text-yellow-300">
                  <IconPlayerPause className="h-4 w-4 mr-2" />
                  Tạm dừng chiến dịch
                </DropdownMenuItem>
              )}
              {canResume && (
                <DropdownMenuItem onClick={onResume} className="text-green-700 dark:text-green-300">
                  <IconPlayerPlay className="h-4 w-4 mr-2" />
                  Tiếp tục chiến dịch
                </DropdownMenuItem>
              )}
              {canComplete && (
                <DropdownMenuItem onClick={onComplete} className="text-purple-700 dark:text-purple-300">
                  <IconCheck className="h-4 w-4 mr-2" />
                  Hoàn thành chiến dịch
                </DropdownMenuItem>
              )}
              {canCancel && (
                <DropdownMenuItem onClick={onCancel} className="text-destructive">
                  <IconX className="h-4 w-4 mr-2" />
                  Hủy chiến dịch
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={onDelete} className="text-destructive">
                <IconTrash className="h-4 w-4 mr-2" />
                Xóa chiến dịch
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Thông tin cơ bản */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-4">
              <Avatar className="h-16 w-16">
                <AvatarFallback className="bg-primary/10 text-primary text-xl">
                  {getInitials(campaign.name)}
                </AvatarFallback>
              </Avatar>
              <div>
                <CardTitle className="text-xl">{campaign.name}</CardTitle>
                <CardDescription className="mt-1 flex items-center gap-2">
                  <Badge variant="outline" className={cn(getStatusColor(campaign.status))}>
                    {getStatusLabel(campaign.status)}
                  </Badge>
                  {campaign.type && (
                    <Badge variant="secondary">
                      {campaign.type === CampaignType.EMAIL ? 'Email' : 
                       campaign.type === CampaignType.SOCIAL ? 'Mạng xã hội' : 
                       campaign.type === CampaignType.EVENT ? 'Sự kiện' : 
                       campaign.type === CampaignType.WEBINAR ? 'Hội thảo trực tuyến' : 
                       campaign.type}
                    </Badge>
                  )}
                </CardDescription>
              </div>
            </div>

            <div className="flex flex-col items-end gap-2">
              {campaign.budget && (
                <div className="text-xl font-bold text-primary">
                  {formatCurrency(campaign.budget)}
                </div>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent>
          {/* Tiến độ chiến dịch */}
          {campaign.status !== CampaignStatus.DRAFT && (
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Tiến độ chiến dịch</span>
                <span className="text-sm font-medium">{progress}%</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Thời gian</p>
              <div className="flex items-center">
                <IconCalendar className="h-4 w-4 mr-2 text-muted-foreground" />
                <span className="font-medium">
                  {campaign.startDate && campaign.endDate ? (
                    <>
                      {format(new Date(campaign.startDate), 'dd/MM/yyyy', { locale: vi })} - {format(new Date(campaign.endDate), 'dd/MM/yyyy', { locale: vi })}
                    </>
                  ) : (
                    'Chưa xác định'
                  )}
                </span>
              </div>
            </div>

            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Người phụ trách</p>
              <div className="flex items-center">
                <IconUser className="h-4 w-4 mr-2 text-muted-foreground" />
                <span className="font-medium">{campaign.owner || 'Chưa phân công'}</span>
              </div>
            </div>

            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Kênh</p>
              <div className="flex items-center gap-2">
                {campaign.channels && campaign.channels.length > 0 ? (
                  campaign.channels.map((channel, index) => (
                    <TooltipProvider key={index}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className="flex items-center justify-center h-6 w-6 rounded-full bg-muted">
                            {getChannelIcon(channel)}
                          </div>
                        </TooltipTrigger>
                        <TooltipContent>
                          {channel === CampaignChannel.EMAIL ? 'Email' : 
                           channel === CampaignChannel.FACEBOOK ? 'Facebook' : 
                           channel === CampaignChannel.INSTAGRAM ? 'Instagram' : 
                           channel === CampaignChannel.TWITTER ? 'Twitter' : 
                           channel === CampaignChannel.LINKEDIN ? 'LinkedIn' : 
                           channel}
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  ))
                ) : (
                  <span className="text-muted-foreground">Chưa xác định</span>
                )}
              </div>
            </div>
          </div>

          {campaign.tags && campaign.tags.length > 0 && (
            <div className="mt-6">
              <p className="text-sm text-muted-foreground mb-2">Tags</p>
              <div className="flex flex-wrap gap-2">
                {campaign.tags.map(tag => (
                  <Badge key={tag} variant="secondary">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {campaign.description && (
            <div className="mt-6">
              <p className="text-sm text-muted-foreground mb-2">Mô tả</p>
              <p className="text-sm">{campaign.description}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">
            <IconFileText className="h-4 w-4 mr-2" />
            Tổng quan
          </TabsTrigger>
          <TabsTrigger value="analytics">
            <IconChartBar className="h-4 w-4 mr-2" />
            Phân tích
          </TabsTrigger>
          <TabsTrigger value="audience">
            <IconUser className="h-4 w-4 mr-2" />
            Đối tượng
          </TabsTrigger>
          <TabsTrigger value="activities">
            <IconClock className="h-4 w-4 mr-2" />
            Hoạt động
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4 mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Thông tin mục tiêu */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Mục tiêu chiến dịch</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Mục tiêu</p>
                    <div className="font-medium">{campaign.goals || 'Chưa xác định'}</div>
                  </div>

                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Đối tượng mục tiêu</p>
                    <div className="font-medium">{campaign.targetAudience || 'Chưa xác định'}</div>
                  </div>

                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">ROI dự kiến</p>
                    <div className="font-medium">{campaign.expectedROI ? `${campaign.expectedROI}%` : 'Chưa xác định'}</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Thông tin ngân sách */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Ngân sách & Chi phí</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Ngân sách</p>
                    <div className="font-medium">{campaign.budget ? formatCurrency(campaign.budget) : 'Chưa xác định'}</div>
                  </div>

                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Chi phí thực tế</p>
                    <div className="font-medium">{campaign.actualCost ? formatCurrency(campaign.actualCost) : 'Chưa phát sinh'}</div>
                  </div>

                  {campaign.budget && campaign.actualCost && (
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">Còn lại</p>
                      <div className="font-medium">{formatCurrency(campaign.budget - campaign.actualCost)}</div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4 mt-6">
          <div className="flex items-center justify-center h-64 rounded-lg border border-dashed">
            <div className="text-center">
              <IconChartBar className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium">Phân tích chiến dịch</h3>
              <p className="text-sm text-muted-foreground mt-1">
                Tính năng đang được phát triển
              </p>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="audience" className="space-y-4 mt-6">
          <div className="flex items-center justify-center h-64 rounded-lg border border-dashed">
            <div className="text-center">
              <IconUser className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium">Đối tượng mục tiêu</h3>
              <p className="text-sm text-muted-foreground mt-1">
                Tính năng đang được phát triển
              </p>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="activities" className="space-y-4 mt-6">
          <div className="flex items-center justify-center h-64 rounded-lg border border-dashed">
            <div className="text-center">
              <IconClock className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium">Lịch sử hoạt động</h3>
              <p className="text-sm text-muted-foreground mt-1">
                Tính năng đang được phát triển
              </p>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
