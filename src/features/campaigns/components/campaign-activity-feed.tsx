import { useState } from 'react'
import { 
  Calendar, 
  Clock, 
  Filter as FilterIcon, 
  Mail, 
  MessageSquare, 
  Phone, 
  User,
  Play,
  Pause,
  Check,
  X,
  Edit,
  Plus
} from 'lucide-react'
import { format } from 'date-fns'
import { vi } from 'date-fns/locale'
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Campaign } from '../data/schema'

interface CampaignActivityFeedProps {
  campaign: Campaign
}

export function CampaignActivityFeed({ campaign }: CampaignActivityFeedProps) {
  // Tạo dữ liệu hoạt động mẫu nếu không có
  const activities = campaign.activities || [
    {
      id: 'act-1',
      type: 'system',
      title: '<PERSON>ế<PERSON> dịch được tạo',
      date: campaign.createdAt || new Date().toISOString(),
    }
  ]

  // Nhóm hoạt động theo ngày
  const groupActivitiesByDate = () => {
    const groups: Record<string, typeof activities> = {}
    
    activities.forEach(activity => {
      const date = new Date(activity.date).toISOString().split('T')[0]
      if (!groups[date]) {
        groups[date] = []
      }
      groups[date].push(activity)
    })
    
    // Sắp xếp các nhóm theo ngày giảm dần (mới nhất lên đầu)
    return Object.entries(groups)
      .sort(([dateA], [dateB]) => new Date(dateB).getTime() - new Date(dateA).getTime())
      .map(([date, activities]) => ({
        date,
        activities: activities.sort((a, b) => 
          new Date(b.date).getTime() - new Date(a.date).getTime()
        )
      }))
  }

  const activityGroups = groupActivitiesByDate()

  // Hàm lấy icon cho loại hoạt động
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'email':
        return <Mail className="h-3 w-3 text-primary" />
      case 'call':
        return <Phone className="h-3 w-3 text-primary" />
      case 'note':
        return <MessageSquare className="h-3 w-3 text-primary" />
      case 'start':
        return <Play className="h-3 w-3 text-primary" />
      case 'pause':
        return <Pause className="h-3 w-3 text-primary" />
      case 'complete':
        return <Check className="h-3 w-3 text-primary" />
      case 'cancel':
        return <X className="h-3 w-3 text-primary" />
      case 'edit':
        return <Edit className="h-3 w-3 text-primary" />
      default:
        return <Calendar className="h-3 w-3 text-primary" />
    }
  }

  // Hàm định dạng ngày
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)
    
    if (date.toDateString() === today.toDateString()) {
      return 'Hôm nay'
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Hôm qua'
    } else {
      return format(date, 'dd/MM/yyyy', { locale: vi })
    }
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base font-medium">Lịch sử hoạt động</CardTitle>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Calendar className="h-3.5 w-3.5 mr-2" />
              Lọc theo ngày
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <FilterIcon className="h-3.5 w-3.5 mr-2" />
                  Lọc hoạt động
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>Tất cả hoạt động</DropdownMenuItem>
                <DropdownMenuItem>Hệ thống</DropdownMenuItem>
                <DropdownMenuItem>Email</DropdownMenuItem>
                <DropdownMenuItem>Người dùng</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <Button variant="outline" size="sm">
              <Plus className="h-3.5 w-3.5 mr-2" />
              Thêm ghi chú
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <ScrollArea className="h-[500px]">
          <div className="px-4 py-2 space-y-6">
            {activityGroups.map(group => (
              <div key={group.date} className="relative pl-6 pb-2">
                <div className="absolute top-0 left-0 w-px h-full bg-border"></div>
                <div className="absolute top-0 left-0 w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center -translate-x-1/2">
                  <Calendar className="h-3 w-3 text-primary" />
                </div>
                <div className="font-medium text-sm text-muted-foreground mb-4">
                  {formatDate(group.date)}
                </div>

                <div className="space-y-4">
                  {group.activities.map(activity => (
                    <ActivityItem
                      key={activity.id}
                      avatar={activity.type === 'system' ? 'System' : campaign.owner || 'User'}
                      name={activity.type === 'system' ? 'Hệ thống' : campaign.owner || 'Người dùng'}
                      title={activity.title}
                      description={activity.description}
                      type={activity.type}
                      time={format(new Date(activity.date), 'HH:mm', { locale: vi })}
                    />
                  ))}
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  )
}

interface ActivityItemProps {
  avatar: string
  name: string
  title: string
  description?: string
  type: string
  time: string
}

function ActivityItem({
  avatar,
  name,
  title,
  description,
  type,
  time
}: ActivityItemProps) {
  // Generate initials from name
  const getInitials = (name: string) => {
    if (!name) return "NA"
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
  }

  return (
    <div className="flex items-start gap-3 py-4 border-b border-border last:border-none">
      <Avatar className="h-8 w-8 mt-0.5">
        <AvatarFallback className={`text-xs ${type === 'system' ? 'bg-blue-100 text-blue-600' : 'bg-primary/10 text-primary'}`}>
          {getInitials(avatar)}
        </AvatarFallback>
      </Avatar>
      <div className="flex-1 min-w-0">
        <div className="text-sm">
          <span className="font-medium">{name}</span>
          <span className="text-muted-foreground ml-1">{title}</span>
          {description && (
            <div className="mt-1 text-sm bg-muted/50 rounded-md p-1.5 break-words">
              {description}
            </div>
          )}
        </div>
        <div className="text-xs text-muted-foreground mt-1 flex items-center">
          <Clock className="h-3 w-3 mr-1 inline-block" />
          {time}
        </div>
      </div>
    </div>
  )
}
