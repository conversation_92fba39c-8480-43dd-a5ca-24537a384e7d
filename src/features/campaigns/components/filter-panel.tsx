import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { IconFilter, IconX } from '@tabler/icons-react'
import { CampaignStatus, CampaignType, CampaignPriority } from '../data/schema'
import { useCampaignsContext } from '../context/use-campaigns-context'

export function FilterToggleButton() {
  const { showFilterPanel, setShowFilterPanel } = useCampaignsContext()

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={() => setShowFilterPanel(!showFilterPanel)}
      className="h-8"
    >
      <IconFilter className="mr-2 h-4 w-4" />
      <PERSON><PERSON> lọc
    </Button>
  )
}

export function FilterPanel() {
  return (
    <div className="w-[250px] flex-shrink-0 rounded-md border p-4">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium">Bộ lọc</h3>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <IconX className="h-4 w-4" />
          <span className="sr-only">Đóng</span>
        </Button>
      </div>
      <Separator className="my-4" />
      
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="status">Trạng thái</Label>
          <Select>
            <SelectTrigger id="status">
              <SelectValue placeholder="Tất cả trạng thái" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tất cả trạng thái</SelectItem>
              <SelectItem value={CampaignStatus.DRAFT}>Bản nháp</SelectItem>
              <SelectItem value={CampaignStatus.SCHEDULED}>Đã lên lịch</SelectItem>
              <SelectItem value={CampaignStatus.RUNNING}>Đang chạy</SelectItem>
              <SelectItem value={CampaignStatus.PAUSED}>Tạm dừng</SelectItem>
              <SelectItem value={CampaignStatus.COMPLETED}>Hoàn thành</SelectItem>
              <SelectItem value={CampaignStatus.CANCELLED}>Đã hủy</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="type">Loại chiến dịch</Label>
          <Select>
            <SelectTrigger id="type">
              <SelectValue placeholder="Tất cả loại" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tất cả loại</SelectItem>
              <SelectItem value={CampaignType.EMAIL}>Email</SelectItem>
              <SelectItem value={CampaignType.SOCIAL}>Mạng xã hội</SelectItem>
              <SelectItem value={CampaignType.SMS}>SMS</SelectItem>
              <SelectItem value={CampaignType.OMNICHANNEL}>Đa kênh</SelectItem>
              <SelectItem value={CampaignType.EVENT}>Sự kiện</SelectItem>
              <SelectItem value={CampaignType.WEBINAR}>Hội thảo trực tuyến</SelectItem>
              <SelectItem value={CampaignType.DIRECT_MAIL}>Thư trực tiếp</SelectItem>
              <SelectItem value={CampaignType.OTHER}>Khác</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="priority">Mức độ ưu tiên</Label>
          <Select>
            <SelectTrigger id="priority">
              <SelectValue placeholder="Tất cả mức độ" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tất cả mức độ</SelectItem>
              <SelectItem value={CampaignPriority.HIGH}>Cao</SelectItem>
              <SelectItem value={CampaignPriority.MEDIUM}>Trung bình</SelectItem>
              <SelectItem value={CampaignPriority.LOW}>Thấp</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="owner">Người phụ trách</Label>
          <Input id="owner" placeholder="Nhập tên người phụ trách" />
        </div>
        
        <div className="space-y-2">
          <Label>Thời gian</Label>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label htmlFor="startDate" className="sr-only">
                Từ ngày
              </Label>
              <Input
                id="startDate"
                type="date"
                placeholder="Từ ngày"
              />
            </div>
            <div>
              <Label htmlFor="endDate" className="sr-only">
                Đến ngày
              </Label>
              <Input
                id="endDate"
                type="date"
                placeholder="Đến ngày"
              />
            </div>
          </div>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="budget">Ngân sách</Label>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label htmlFor="minBudget" className="sr-only">
                Tối thiểu
              </Label>
              <Input
                id="minBudget"
                type="number"
                placeholder="Tối thiểu"
              />
            </div>
            <div>
              <Label htmlFor="maxBudget" className="sr-only">
                Tối đa
              </Label>
              <Input
                id="maxBudget"
                type="number"
                placeholder="Tối đa"
              />
            </div>
          </div>
        </div>
        
        <div className="pt-4">
          <Button className="w-full">Áp dụng bộ lọc</Button>
        </div>
        
        <div>
          <Button variant="outline" className="w-full">
            Đặt lại bộ lọc
          </Button>
        </div>
      </div>
    </div>
  )
}
