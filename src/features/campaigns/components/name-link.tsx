import { Campaign } from '../data/schema'
import { useCampaignsContext } from '../context/use-campaigns-context'
import { useNavigate } from '@tanstack/react-router'

interface NameLinkProps {
  campaign: Campaign
}

export function NameLink({ campaign }: NameLinkProps) {
  const { setSelectedCampaignIds } = useCampaignsContext()
  const navigate = useNavigate()

  const handleClick = () => {
    setSelectedCampaignIds([campaign.id])
    navigate({ to: `/campaigns/${campaign.id}` })
  }

  return (
    <div className="flex items-center">
      <button
        onClick={handleClick}
        className="font-medium hover:underline focus:outline-none"
      >
        {campaign.name}
      </button>
    </div>
  )
}
