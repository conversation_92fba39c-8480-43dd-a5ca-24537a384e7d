import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  IconPlus,
  IconUpload,
  IconDownload,
  IconChevronDown,
} from '@tabler/icons-react'
import { useCampaignsContext } from '../context/use-campaigns-context'

export function CampaignsPrimaryButtons() {
  const { setOpenDialog } = useCampaignsContext()

  return (
    <div className="flex items-center gap-2">
      <Button onClick={() => setOpenDialog('create')}>
        <IconPlus className="mr-2 h-4 w-4" />
        Thêm chiến dịch
      </Button>
      
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline">
            <IconChevronDown className="mr-2 h-4 w-4" />
            Thao tác
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={() => setOpenDialog('import')}>
            <IconUpload className="mr-2 h-4 w-4" />
            Nhập chiến dịch
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setOpenDialog('export')}>
            <IconDownload className="mr-2 h-4 w-4" />
            Xuất chiến dịch
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}
