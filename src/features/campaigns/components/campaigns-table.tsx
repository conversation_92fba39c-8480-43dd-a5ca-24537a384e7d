import { ColumnDef } from '@tanstack/react-table'
import { DataTable } from '@/components/ui/data-table'
import { Campaign } from '../data/schema'

interface CampaignsTableProps {
  data: Campaign[]
  columns: ColumnDef<Campaign>[]
}

export function CampaignsTable({ data, columns }: CampaignsTableProps) {
  return (
    <DataTable
      columns={columns}
      data={data}
      searchKey="name"
      searchPlaceholder="Tìm kiếm chiến dịch..."
    />
  )
}
