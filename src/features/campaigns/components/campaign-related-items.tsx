import { useState } from 'react'
import { 
  DollarSign, 
  User, 
  Plus, 
  ArrowRightCircle,
  Mail
} from 'lucide-react'
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Campaign } from '../data/schema'

interface CampaignRelatedItemsProps {
  campaign: Campaign
}

export function CampaignRelatedItems({ campaign }: CampaignRelatedItemsProps) {
  const [activeTab, setActiveTab] = useState('leads')

  return (
    <div className="space-y-4">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="leads">Leads</TabsTrigger>
          <TabsTrigger value="opportunities"><PERSON><PERSON> hội</TabsTrigger>
          <TabsTrigger value="templates">Mẫu Email</TabsTrigger>
        </TabsList>

        <TabsContent value="leads" className="mt-4 space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-3">
              <div>
                <CardTitle className="text-base font-medium">Leads</CardTitle>
                <CardDescription>Các leads được tạo từ chiến dịch này</CardDescription>
              </div>
              <Button size="sm">
                <Plus className="h-3.5 w-3.5 mr-2" />
                Thêm leads
              </Button>
            </CardHeader>
            <CardContent className="p-0">
              {campaign.leads && campaign.leads.length > 0 ? (
                <div className="divide-y">
                  {campaign.leads.map((leadId, index) => (
                    <div key={leadId} className="p-4 flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <User className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <div className="text-sm font-medium">Lead #{index + 1}</div>
                          <div className="text-xs text-muted-foreground">
                            ID: {leadId}
                          </div>
                        </div>
                      </div>
                      <Button variant="ghost" size="sm">
                        Xem chi tiết
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="p-6 text-center border-t">
                  <User className="h-10 w-10 text-muted-foreground/50 mx-auto mb-3" />
                  <h3 className="text-base font-medium">Không có leads</h3>
                  <p className="text-sm text-muted-foreground mt-1 mb-4">
                    Chiến dịch này chưa tạo ra leads nào
                  </p>
                  <Button size="sm">
                    <ArrowRightCircle className="h-3.5 w-3.5 mr-2" />
                    Thêm leads mới
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="opportunities" className="mt-4 space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-3">
              <div>
                <CardTitle className="text-base font-medium">Cơ hội</CardTitle>
                <CardDescription>Các cơ hội được tạo từ chiến dịch này</CardDescription>
              </div>
              <Button size="sm">
                <Plus className="h-3.5 w-3.5 mr-2" />
                Thêm cơ hội
              </Button>
            </CardHeader>
            <CardContent className="p-0">
              {campaign.opportunities && campaign.opportunities.length > 0 ? (
                <div className="divide-y">
                  {campaign.opportunities.map((oppId, index) => (
                    <div key={oppId} className="p-4 flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <div className="text-sm font-medium">Cơ hội #{index + 1}</div>
                          <div className="text-xs text-muted-foreground">
                            ID: {oppId}
                          </div>
                        </div>
                      </div>
                      <Button variant="ghost" size="sm">
                        Xem chi tiết
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="p-6 text-center border-t">
                  <DollarSign className="h-10 w-10 text-muted-foreground/50 mx-auto mb-3" />
                  <h3 className="text-base font-medium">Không có cơ hội</h3>
                  <p className="text-sm text-muted-foreground mt-1 mb-4">
                    Chiến dịch này chưa tạo ra cơ hội nào
                  </p>
                  <Button size="sm">
                    <ArrowRightCircle className="h-3.5 w-3.5 mr-2" />
                    Thêm cơ hội mới
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates" className="mt-4 space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-3">
              <div>
                <CardTitle className="text-base font-medium">Mẫu Email</CardTitle>
                <CardDescription>Các mẫu email được sử dụng trong chiến dịch này</CardDescription>
              </div>
              <Button size="sm">
                <Plus className="h-3.5 w-3.5 mr-2" />
                Thêm mẫu email
              </Button>
            </CardHeader>
            <CardContent className="p-0">
              {campaign.content && campaign.content.template ? (
                <div className="p-4 flex items-center justify-between border-t">
                  <div className="flex items-center gap-3">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <div className="text-sm font-medium">{campaign.content.template}</div>
                      <div className="text-xs text-muted-foreground">
                        {campaign.content.subject}
                      </div>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm">
                    Xem chi tiết
                  </Button>
                </div>
              ) : (
                <div className="p-6 text-center border-t">
                  <Mail className="h-10 w-10 text-muted-foreground/50 mx-auto mb-3" />
                  <h3 className="text-base font-medium">Không có mẫu email</h3>
                  <p className="text-sm text-muted-foreground mt-1 mb-4">
                    Chiến dịch này chưa sử dụng mẫu email nào
                  </p>
                  <Button size="sm">
                    <ArrowRightCircle className="h-3.5 w-3.5 mr-2" />
                    Thêm mẫu email
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
