import { useState } from 'react'
import { 
  <PERSON><PERSON>hart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  Legend
} from 'recharts'
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Campaign, CampaignType } from '../data/schema'

interface CampaignAnalyticsProps {
  campaign: Campaign
}

export function CampaignAnalytics({ campaign }: CampaignAnalyticsProps) {
  const [timeRange, setTimeRange] = useState('all')

  // Tạo dữ liệu mẫu cho biểu đồ nếu không có
  const generateSampleData = () => {
    if (campaign.type === CampaignType.EMAIL) {
      return {
        emailStats: {
          sent: 1000,
          delivered: 950,
          opened: 380,
          clicked: 95,
          bounced: 50,
          unsubscribed: 5,
        },
        dailyStats: [
          { name: 'Ngày 1', sent: 200, opened: 80, clicked: 20 },
          { name: 'Ngày 2', sent: 300, opened: 120, clicked: 30 },
          { name: 'Ngày 3', sent: 250, opened: 100, clicked: 25 },
          { name: 'Ngày 4', sent: 150, opened: 60, clicked: 15 },
          { name: 'Ngày 5', sent: 100, opened: 20, clicked: 5 },
        ],
        deviceStats: [
          { name: 'Desktop', value: 45 },
          { name: 'Mobile', value: 40 },
          { name: 'Tablet', value: 15 },
        ],
        locationStats: [
          { name: 'Hà Nội', value: 35 },
          { name: 'TP.HCM', value: 40 },
          { name: 'Đà Nẵng', value: 10 },
          { name: 'Khác', value: 15 },
        ],
      }
    } else {
      return {
        performanceStats: {
          impressions: 5000,
          clicks: 250,
          conversions: 25,
          ctr: 5,
          conversionRate: 10,
        },
        dailyStats: [
          { name: 'Ngày 1', impressions: 1000, clicks: 50, conversions: 5 },
          { name: 'Ngày 2', impressions: 1200, clicks: 60, conversions: 6 },
          { name: 'Ngày 3', impressions: 900, clicks: 45, conversions: 4 },
          { name: 'Ngày 4', impressions: 1100, clicks: 55, conversions: 5 },
          { name: 'Ngày 5', impressions: 800, clicks: 40, conversions: 5 },
        ],
        channelStats: [
          { name: 'Facebook', value: 40 },
          { name: 'Instagram', value: 30 },
          { name: 'Google', value: 20 },
          { name: 'Khác', value: 10 },
        ],
      }
    }
  }

  const data = generateSampleData()
  
  // Màu cho biểu đồ - sử dụng chart colors từ CSS variables
  const COLORS = [
    'hsl(var(--chart-1))',
    'hsl(var(--chart-2))',
    'hsl(var(--chart-3))',
    'hsl(var(--chart-4))',
    'hsl(var(--chart-5))',
    'hsl(var(--primary))'
  ]

  // Định dạng số
  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('vi-VN').format(value)
  }

  // Định dạng phần trăm
  const formatPercent = (value: number) => {
    return `${value}%`
  }

  // Hiển thị phân tích cho chiến dịch email
  const renderEmailAnalytics = () => {
    const { emailStats, dailyStats, deviceStats, locationStats } = data
    
    if (!emailStats || !deviceStats || !locationStats) return null;
    
    // Tính toán tỷ lệ
    const openRate = Math.round(((emailStats?.opened || 0) / (emailStats?.delivered || 1)) * 100)
    const clickRate = Math.round(((emailStats?.clicked || 0) / (emailStats?.opened || 1)) * 100)
    const bounceRate = Math.round(((emailStats?.bounced || 0) / (emailStats?.sent || 1)) * 100)
    
    // Dữ liệu cho biểu đồ tròn
    const pieData = [
      { name: 'Đã mở', value: emailStats?.opened || 0 },
      { name: 'Chưa mở', value: (emailStats?.delivered || 0) - (emailStats?.opened || 0) },
      { name: 'Không gửi được', value: emailStats?.bounced || 0 },
    ]

    return (
      <>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Tỷ lệ mở</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{openRate}%</div>
              <p className="text-xs text-muted-foreground mt-1">
                {formatNumber(emailStats?.opened || 0)} / {formatNumber(emailStats?.delivered || 0)} emails
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Tỷ lệ nhấp</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{clickRate}%</div>
              <p className="text-xs text-muted-foreground mt-1">
                {formatNumber(emailStats?.clicked || 0)} / {formatNumber(emailStats?.opened || 0)} emails
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Tỷ lệ trả về</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{bounceRate}%</div>
              <p className="text-xs text-muted-foreground mt-1">
                {formatNumber(emailStats?.bounced || 0)} / {formatNumber(emailStats?.sent || 0)} emails
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Hủy đăng ký</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{emailStats?.unsubscribed || 0}</div>
              <p className="text-xs text-muted-foreground mt-1">
                người nhận
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Hiệu suất theo thời gian</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={dailyStats}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="sent" fill="#8884d8" name="Đã gửi" />
                    <Bar dataKey="opened" fill="#82ca9d" name="Đã mở" />
                    <Bar dataKey="clicked" fill="#ffc658" name="Đã nhấp" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-base">Tổng quan email</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={pieData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {pieData.map((_entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Thiết bị</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={deviceStats}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {deviceStats.map((_entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-base">Vị trí</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={locationStats}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {locationStats.map((_entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>
      </>
    )
  }

  // Hiển thị phân tích cho các loại chiến dịch khác
  const renderOtherAnalytics = () => {
    const { performanceStats, dailyStats, channelStats } = data
    
    if (!performanceStats || !channelStats) return null;
    
    return (
      <>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Lượt hiển thị</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatNumber(performanceStats?.impressions || 0)}</div>
              <p className="text-xs text-muted-foreground mt-1">
                tổng lượt hiển thị
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Lượt nhấp</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatNumber(performanceStats?.clicks || 0)}</div>
              <p className="text-xs text-muted-foreground mt-1">
                tổng lượt nhấp
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">CTR</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatPercent(performanceStats?.ctr || 0)}</div>
              <p className="text-xs text-muted-foreground mt-1">
                tỷ lệ nhấp/hiển thị
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Chuyển đổi</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatNumber(performanceStats?.conversions || 0)}</div>
              <p className="text-xs text-muted-foreground mt-1">
                tỷ lệ: {formatPercent(performanceStats?.conversionRate || 0)}
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Hiệu suất theo thời gian</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={dailyStats}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="impressions" fill="#8884d8" name="Lượt hiển thị" />
                    <Bar dataKey="clicks" fill="#82ca9d" name="Lượt nhấp" />
                    <Bar dataKey="conversions" fill="#ffc658" name="Chuyển đổi" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-base">Phân bổ theo kênh</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={channelStats}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {channelStats.map((_entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>
      </>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Tabs value={timeRange} onValueChange={setTimeRange} className="w-auto">
          <TabsList>
            <TabsTrigger value="day">Hôm nay</TabsTrigger>
            <TabsTrigger value="week">Tuần này</TabsTrigger>
            <TabsTrigger value="month">Tháng này</TabsTrigger>
            <TabsTrigger value="all">Tất cả</TabsTrigger>
          </TabsList>
        </Tabs>

        <Button variant="outline" size="sm">
          Xuất báo cáo
        </Button>
      </div>

      {campaign.type === CampaignType.EMAIL ? renderEmailAnalytics() : renderOtherAnalytics()}
    </div>
  )
}
