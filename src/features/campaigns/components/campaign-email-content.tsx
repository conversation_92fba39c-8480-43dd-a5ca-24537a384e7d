import { useState } from 'react'
import { 
  Mail, 
  FileText, 
  ExternalLink, 
  Co<PERSON>, 
  Check,
  Edit,
  Eye
} from 'lucide-react'
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle
} from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog'

interface EmailAttachment {
  id: string
  name: string
  size: number
}

interface EmailContent {
  subject?: string
  body?: string
  template?: string
  callToAction?: string
  landingPage?: string
  attachments?: EmailAttachment[]
}

interface CampaignEmailContentProps {
  content: EmailContent
}

interface CopyState {
  subject: boolean
  landingPage: boolean
  html: boolean
  text: boolean
}

export function CampaignEmailContent({ content }: CampaignEmailContentProps) {
  const [activeTab, setActiveTab] = useState('preview')
  const [copiedStates, setCopiedStates] = useState<CopyState>({
    subject: false,
    landingPage: false,
    html: false,
    text: false
  })
  const [previewOpen, setPreviewOpen] = useState(false)

  // Handle copying content with specific key
  const handleCopy = (text: string | undefined, key: keyof CopyState) => {
    if (text) {
      navigator.clipboard.writeText(text)
      setCopiedStates((prev: CopyState) => ({ ...prev, [key]: true }))
      setTimeout(() => {
        setCopiedStates((prev: CopyState) => ({ ...prev, [key]: false }))
      }, 2000)
    }
  }

  // Sanitize HTML content
  const sanitizeHtml = (html: string) => {
    // Basic sanitization - you might want to use a library like DOMPurify in production
    return html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
  }

  // Email HTML template
  const emailHtml = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>${content.subject || 'Email Campaign'}</title>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { text-align: center; padding: 20px 0; }
    .content { padding: 20px; background-color: #f9f9f9; border-radius: 5px; }
    .footer { text-align: center; padding: 20px 0; font-size: 12px; color: #777; }
    .button { display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>${content.subject || 'Email Campaign'}</h1>
    </div>
    <div class="content">
      ${content.body ? sanitizeHtml(content.body) : ''}
      
      ${content.callToAction && content.landingPage ? `<p style="text-align: center; margin-top: 30px;">
        <a href="${content.landingPage}" class="button">${content.callToAction}</a>
      </p>` : ''}
    </div>
    <div class="footer">
      <p>© 2023 Công ty ABC. Tất cả các quyền được bảo lưu.</p>
      <p>Bạn nhận được email này vì bạn đã đăng ký nhận thông tin từ chúng tôi.</p>
      <p><a href="#">Hủy đăng ký</a> | <a href="#">Xem trên trình duyệt</a></p>
    </div>
  </div>
</body>
</html>
  `

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-base">Chi tiết email</CardTitle>
              <p className="text-sm text-muted-foreground">
                Thông tin về nội dung email được sử dụng trong chiến dịch
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setPreviewOpen(true)}
                aria-label="Xem trước email"
              >
                <Eye className="h-4 w-4 mr-2" />
                Xem trước
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                aria-label="Chỉnh sửa email"
              >
                <Edit className="h-4 w-4 mr-2" />
                Chỉnh sửa
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium mb-1">Tiêu đề</h3>
              <div className="flex items-center justify-between p-3 bg-muted rounded-md">
                <p className="text-sm">{content.subject || 'Chưa có tiêu đề'}</p>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="h-8 w-8 p-0 rounded-full"
                  onClick={() => handleCopy(content.subject, 'subject')}
                  aria-label="Sao chép tiêu đề"
                >
                  {copiedStates.subject ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium mb-1">Mẫu</h3>
              <div className="flex items-center gap-2">
                <Badge variant="outline">{content.template || 'Chưa chọn mẫu'}</Badge>
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium mb-1">Nút hành động</h3>
              <div className="flex items-center gap-2">
                {content.callToAction ? (
                  <Badge variant="secondary">{content.callToAction}</Badge>
                ) : (
                  <span className="text-sm text-muted-foreground">Không có</span>
                )}
              </div>
            </div>

            {content.landingPage && (
              <div>
                <h3 className="text-sm font-medium mb-1">Trang đích</h3>
                <div className="flex items-center justify-between p-3 bg-muted rounded-md">
                  <p className="text-sm truncate">{content.landingPage}</p>
                  <div className="flex items-center gap-2">
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="h-8 w-8 p-0 rounded-full"
                      onClick={() => window.open(content.landingPage, '_blank')}
                      aria-label="Mở trang đích"
                    >
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="h-8 w-8 p-0 rounded-full"
                      onClick={() => handleCopy(content.landingPage, 'landingPage')}
                      aria-label="Sao chép đường dẫn trang đích"
                    >
                      {copiedStates.landingPage ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">Nội dung email</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <div className="px-6">
              <TabsList className="w-full grid grid-cols-3">
                <TabsTrigger value="preview">
                  <Eye className="h-4 w-4 mr-2" />
                  Xem trước
                </TabsTrigger>
                <TabsTrigger value="html">
                  <FileText className="h-4 w-4 mr-2" />
                  HTML
                </TabsTrigger>
                <TabsTrigger value="text">
                  <Mail className="h-4 w-4 mr-2" />
                  Văn bản
                </TabsTrigger>
              </TabsList>
            </div>

            <Separator className="my-2" />

            <TabsContent value="preview" className="p-6">
              <div className="border rounded-md p-4 bg-white">
                <div className="text-xl font-bold mb-4">{content.subject || 'Chưa có tiêu đề'}</div>
                <div 
                  className="prose max-w-none" 
                  dangerouslySetInnerHTML={{ 
                    __html: content.body ? sanitizeHtml(content.body.replace(/\n/g, '<br/>')) : 'Chưa có nội dung'
                  }} 
                />
                
                {content.callToAction && (
                  <div className="mt-6 text-center">
                    <Button className="bg-blue-500 hover:bg-blue-600">
                      {content.callToAction}
                    </Button>
                  </div>
                )}

                {content.attachments && content.attachments.length > 0 && (
                  <div className="mt-6 border-t pt-4">
                    <h3 className="text-sm font-medium mb-2">Tệp đính kèm:</h3>
                    <div className="space-y-2">
                      {content.attachments.map((attachment) => (
                        <div key={attachment.id} className="flex items-center p-2 border rounded-md">
                          <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                          <span className="text-sm">{attachment.name}</span>
                          <span className="text-xs text-muted-foreground ml-2">
                            ({Math.round(attachment.size / 1024)} KB)
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="html" className="p-6">
              <div className="relative">
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="absolute right-2 top-2"
                  onClick={() => handleCopy(emailHtml, 'html')}
                  aria-label="Sao chép mã HTML"
                >
                  {copiedStates.html ? <Check className="h-4 w-4 mr-2" /> : <Copy className="h-4 w-4 mr-2" />}
                  {copiedStates.html ? 'Đã sao chép' : 'Sao chép'}
                </Button>
                <ScrollArea className="h-[400px] w-full rounded-md border p-4 bg-muted">
                  <pre className="text-xs">{emailHtml}</pre>
                </ScrollArea>
              </div>
            </TabsContent>

            <TabsContent value="text" className="p-6">
              <div className="relative">
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="absolute right-2 top-2"
                  onClick={() => handleCopy(content.body, 'text')}
                  aria-label="Sao chép nội dung văn bản"
                >
                  {copiedStates.text ? <Check className="h-4 w-4 mr-2" /> : <Copy className="h-4 w-4 mr-2" />}
                  {copiedStates.text ? 'Đã sao chép' : 'Sao chép'}
                </Button>
                <ScrollArea className="h-[400px] w-full rounded-md border p-4 bg-muted">
                  <div className="text-sm whitespace-pre-wrap">{content.body || 'Chưa có nội dung'}</div>
                </ScrollArea>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      <Dialog open={previewOpen} onOpenChange={setPreviewOpen}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-auto">
          <DialogHeader>
            <DialogTitle>Xem trước email</DialogTitle>
            <DialogDescription>
              Xem trước email như người nhận sẽ thấy
            </DialogDescription>
          </DialogHeader>
          
          <div className="mt-4 border rounded-md p-6 bg-white">
            <div dangerouslySetInnerHTML={{ __html: sanitizeHtml(emailHtml) }} />
          </div>
          
          <DialogFooter className="mt-4">
            <Button 
              variant="outline" 
              onClick={() => setPreviewOpen(false)}
              aria-label="Đóng xem trước"
            >
              Đóng
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
