import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { useCampaignsContext } from '../context/use-campaigns-context'

export function CampaignsDialogs() {
  const {
    openDialog,
    setOpenDialog,
    selectedCampaignIds,
    deleteCampaign,
    campaigns,
    completeCampaign,
    cancelCampaign,
  } = useCampaignsContext()

  const handleClose = () => {
    setOpenDialog('')
  }

  const handleDelete = () => {
    selectedCampaignIds.forEach((id) => {
      deleteCampaign(id)
    })
    handleClose()
  }

  const handleComplete = () => {
    selectedCampaignIds.forEach((id) => {
      completeCampaign(id)
    })
    handleClose()
  }

  const handleCancel = () => {
    selectedCampaignIds.forEach((id) => {
      cancelCampaign(id)
    })
    handleClose()
  }

  // <PERSON><PERSON><PERSON> thông tin chiến dịch đang được chọn
  const selectedCampaign = selectedCampaignIds.length === 1
    ? campaigns.find((campaign) => campaign.id === selectedCampaignIds[0])
    : null

  return (
    <>
      {/* Dialog xóa chiến dịch */}
      <Dialog open={openDialog === 'delete'} onOpenChange={handleClose}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Xác nhận xóa</DialogTitle>
            <DialogDescription>
              {selectedCampaignIds.length > 1
                ? `Bạn có chắc chắn muốn xóa ${selectedCampaignIds.length} chiến dịch đã chọn không?`
                : 'Bạn có chắc chắn muốn xóa chiến dịch này không?'}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={handleClose}>
              Hủy
            </Button>
            <Button variant="destructive" onClick={handleDelete}>
              Xóa
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog hoàn thành chiến dịch */}
      <Dialog open={openDialog === 'complete'} onOpenChange={handleClose}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Xác nhận hoàn thành</DialogTitle>
            <DialogDescription>
              {selectedCampaignIds.length > 1
                ? `Bạn có chắc chắn muốn đánh dấu ${selectedCampaignIds.length} chiến dịch đã chọn là hoàn thành không?`
                : `Bạn có chắc chắn muốn đánh dấu chiến dịch "${selectedCampaign?.name}" là hoàn thành không?`}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={handleClose}>
              Hủy
            </Button>
            <Button variant="default" onClick={handleComplete}>
              Hoàn thành
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog hủy chiến dịch */}
      <Dialog open={openDialog === 'cancel'} onOpenChange={handleClose}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Xác nhận hủy</DialogTitle>
            <DialogDescription>
              {selectedCampaignIds.length > 1
                ? `Bạn có chắc chắn muốn hủy ${selectedCampaignIds.length} chiến dịch đã chọn không?`
                : `Bạn có chắc chắn muốn hủy chiến dịch "${selectedCampaign?.name}" không?`}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={handleClose}>
              Không
            </Button>
            <Button variant="destructive" onClick={handleCancel}>
              Hủy chiến dịch
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Các dialog khác sẽ được thêm sau */}
      {/* Dialog tạo chiến dịch mới */}
      {/* Dialog chỉnh sửa chiến dịch */}
      {/* Dialog nhân bản chiến dịch */}
      {/* Dialog nhập chiến dịch */}
      {/* Dialog xuất chiến dịch */}
    </>
  )
}
