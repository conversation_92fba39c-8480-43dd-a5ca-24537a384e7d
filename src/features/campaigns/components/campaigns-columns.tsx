import { ColumnDef } from '@tanstack/react-table'
import { Checkbox } from '@/components/ui/checkbox'
import { Campaign } from '../data/schema'
import { DataTableColumnHeader } from '@/components/ui/data-table'
import { DataTableRowActions } from './data-table-row-actions'
import { StatusBadge } from '@/components/ui/status-badge'
import { NameLink } from './name-link'
import { Badge } from '@/components/ui/badge'
import { formatDate, formatCurrency } from '@/lib/utils'

export const columns: ColumnDef<Campaign>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label='Chọn tất cả'
        className='translate-y-[2px]'
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label='Chọn hàng'
        className='translate-y-[2px]'
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Tên chiến dịch' />
    ),
    cell: ({ row }) => <NameLink campaign={row.original} />,
    enableSorting: true,
    enableHiding: false,
  },
  {
    accessorKey: 'type',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Loại' />
    ),
    cell: ({ row }) => {
      const type = row.getValue('type') as string
      
      return (
        <div className='flex items-center'>
          <Badge variant='outline' className='capitalize'>
            {type === 'email' ? 'Email' : 
             type === 'social' ? 'Mạng xã hội' : 
             type === 'sms' ? 'SMS' : 
             type === 'omnichannel' ? 'Đa kênh' : 
             type === 'event' ? 'Sự kiện' : 
             type === 'webinar' ? 'Hội thảo trực tuyến' : 
             type === 'direct_mail' ? 'Thư trực tiếp' : 
             'Khác'}
          </Badge>
        </div>
      )
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'status',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Trạng thái' />
    ),
    cell: ({ row }) => {
      const status = row.getValue('status') as string
      
      return (
        <StatusBadge
          status={
            status === 'draft' ? 'Bản nháp' :
            status === 'scheduled' ? 'Đã lên lịch' :
            status === 'running' ? 'Đang chạy' :
            status === 'paused' ? 'Tạm dừng' :
            status === 'completed' ? 'Hoàn thành' :
            'Đã hủy'
          }
          variant={
            status === 'draft' ? 'default' :
            status === 'scheduled' ? 'inProgress' :
            status === 'running' ? 'default' :
            status === 'paused' ? 'warning' :
            status === 'completed' ? 'success' :
            'destructive'
          }
        />
      )
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'startDate',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Ngày bắt đầu' />
    ),
    cell: ({ row }) => {
      const startDate = row.getValue('startDate') as string
      
      if (!startDate) return <span className='text-muted-foreground'>Chưa xác định</span>
      
      return <div>{formatDate(startDate)}</div>
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'endDate',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Ngày kết thúc' />
    ),
    cell: ({ row }) => {
      const endDate = row.getValue('endDate') as string
      
      if (!endDate) return <span className='text-muted-foreground'>Chưa xác định</span>
      
      return <div>{formatDate(endDate)}</div>
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'budget',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Ngân sách' />
    ),
    cell: ({ row }) => {
      const budget = row.getValue('budget') as number
      
      if (!budget) return <span className='text-muted-foreground'>Chưa xác định</span>
      
      return <div>{formatCurrency(budget)}</div>
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'owner',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Người phụ trách' />
    ),
    cell: ({ row }) => {
      const owner = row.getValue('owner') as string
      
      if (!owner) return <span className='text-muted-foreground'>Chưa phân công</span>
      
      return <div>{owner}</div>
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    id: 'actions',
    cell: ({ row }) => <DataTableRowActions row={row} />,
  },
]
