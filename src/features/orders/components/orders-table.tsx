import { ColumnDef, RowData } from '@tanstack/react-table'
import { DataTable } from '@/components/ui/data-table'
import { Order } from '../data/schema'

declare module '@tanstack/react-table' {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  interface ColumnMeta<TData extends RowData, TValue> {
    className: string
  }
}

interface OrdersTableProps {
  columns: ColumnDef<Order>[]
  data: Order[]
}

export function OrdersTable({ columns, data }: OrdersTableProps) {
  return (
    <DataTable
      columns={columns}
      data={data}
      searchKey="orderNumber"
      searchPlaceholder="Tìm kiếm đơn hàng..."
    />
  )
}