import { useState } from 'react'
import { useNavigate } from '@tanstack/react-router'
import {
  IconArrowLeft,
  IconEdit,
  IconFileInvoice,
  IconPrinter,
  IconUser,
  IconCalendar,
  IconCreditCard,
  IconTruck,
  IconNotes,
  IconPackage,
  IconClock,
  IconLink,
  IconTag,
  IconBuilding,
  IconChevronDown,
  IconPlus,
  IconDots
} from '@tabler/icons-react'
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from '@/components/ui/tabs'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { StatusBadge } from '@/components/ui/status-badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Separator } from '@/components/ui/separator'
import { formatCurrency } from '@/utils/format'
import { Order, OrderStatus, PaymentStatus } from '../data/schema'
import { OrderTimeline } from './order-timeline'
import { OrderPricing } from './order-pricing'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { showSubmittedData } from '@/utils/show-submitted-data'

interface OrderDetailProps {
  order: Order
}

export function OrderDetail({ order }: OrderDetailProps) {
  const navigate = useNavigate()
  const [activeTab, setActiveTab] = useState('overview')

  const handleBack = () => {
    navigate({ to: '/orders/' })
  }

  const handleEdit = () => {
    showSubmittedData({ message: 'Chỉnh sửa đơn hàng', order })
  }

  const handlePrint = () => {
    showSubmittedData({ message: 'In đơn hàng', order })
  }

  const handleInvoice = () => {
    showSubmittedData({ message: 'Xuất hóa đơn', order })
  }

  // Xác định variant cho trạng thái đơn hàng
  const getOrderStatusVariant = () => {
    const status = order.status.toLowerCase()
    if (status === 'completed') return 'done'
    if (status === 'processing') return 'inProgress'
    if (status === 'cancelled') return 'cancelled'
    if (status === 'new') return 'todo'
    return 'default'
  }

  // Xác định variant cho trạng thái thanh toán
  const getPaymentStatusVariant = () => {
    const status = order.paymentStatus.toLowerCase()
    if (status === 'paid') return 'done'
    if (status === 'pending') return 'todo'
    if (status === 'refunded') return 'cancelled'
    return 'default'
  }

  return (
    <>
      <Header fixed>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <Button variant="outline" size="icon" onClick={handleBack}>
              <IconArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-xl font-bold">Chi tiết đơn hàng</h1>
          </div>

          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={handlePrint}>
              <IconPrinter className="h-4 w-4 mr-2" />
              In đơn hàng
            </Button>
            <Button variant="outline" onClick={handleInvoice}>
              <IconFileInvoice className="h-4 w-4 mr-2" />
              Xuất hóa đơn
            </Button>
            <Button variant="outline" onClick={handleEdit}>
              <IconEdit className="h-4 w-4 mr-2" />
              Chỉnh sửa
            </Button>
          </div>
        </div>

        {/* Order Info */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Thông tin đơn hàng</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Mã đơn hàng:</span>
                  <span className="font-medium">{order.orderNumber}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Ngày đặt:</span>
                  <span>{order.orderDate}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Cập nhật cuối:</span>
                  <span>{order.lastModified}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Trạng thái:</span>
                  <StatusBadge status={order.status} variant={getOrderStatusVariant()} />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Thông tin khách hàng</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-3 mb-3">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={order.customerAvatar} alt={order.customerName} />
                  <AvatarFallback>{order.customerName.charAt(0)}</AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-medium">{order.customerName}</div>
                  <div className="text-sm text-muted-foreground">Khách hàng</div>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Địa chỉ giao hàng:</span>
                </div>
                <div className="text-sm">
                  {order.shippingAddress}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Thông tin thanh toán</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Tổng tiền:</span>
                  <span className="font-medium">{formatCurrency(order.total)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Phương thức:</span>
                  <span>{order.paymentMethod}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Trạng thái:</span>
                  <StatusBadge status={order.paymentStatus} variant={getPaymentStatusVariant()} />
                </div>
                <div className="pt-2">
                  <div className="w-full bg-muted rounded-full h-2.5">
                    <div
                      className="bg-primary h-2.5 rounded-full"
                      style={{
                        width: order.paymentStatus === 'Paid' ? '100%' :
                               order.paymentStatus === 'Partially Paid' ? '50%' : '0%'
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Tabs */}
        <Tabs defaultValue="overview" onValueChange={setActiveTab} className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <IconPackage className="h-4 w-4" />
              <span>Sản phẩm</span>
            </TabsTrigger>
            <TabsTrigger value="activity" className="flex items-center gap-2">
              <IconCalendar className="h-4 w-4" />
              <span>Hoạt động</span>
            </TabsTrigger>
            <TabsTrigger value="links" className="flex items-center gap-2">
              <IconCreditCard className="h-4 w-4" />
              <span>Liên kết</span>
            </TabsTrigger>
            <TabsTrigger value="notes" className="flex items-center gap-2">
              <IconNotes className="h-4 w-4" />
              <span>Ghi chú</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <OrderPricing order={order} readOnly={true} />
          </TabsContent>

          <TabsContent value="activity" className="space-y-4">
            <OrderTimeline order={order} />
          </TabsContent>

          <TabsContent value="links" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Opportunity</CardTitle>
                <CardDescription>
                  Cơ hội liên quan đến đơn hàng
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-6 text-muted-foreground">
                  Không có cơ hội nào được liên kết với đơn hàng này.
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Nhiệm vụ</CardTitle>
                <CardDescription>
                  Nhiệm vụ liên quan đến đơn hàng
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-6 text-muted-foreground">
                  Không có nhiệm vụ nào được liên kết với đơn hàng này.
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Chiến dịch</CardTitle>
                <CardDescription>
                  Chiến dịch liên quan đến đơn hàng
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-6 text-muted-foreground">
                  Không có chiến dịch nào được liên kết với đơn hàng này.
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="notes" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Ghi chú</CardTitle>
                <CardDescription>
                  Ghi chú về đơn hàng
                </CardDescription>
              </CardHeader>
              <CardContent>
                {order.notes ? (
                  <div className="p-4 border rounded-md">
                    {order.notes}
                  </div>
                ) : (
                  <div className="text-center py-6 text-muted-foreground">
                    Không có ghi chú nào cho đơn hàng này.
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </Main>
    </>
  )
}
