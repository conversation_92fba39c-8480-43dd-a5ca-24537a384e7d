import { DropdownMenuTrigger } from '@radix-ui/react-dropdown-menu'
import { Table } from '@tanstack/react-table'
import { MixerHorizontalIcon } from '@radix-ui/react-icons'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu'

interface DataTableViewOptionsProps<TData> {
  table: Table<TData>
}

export function DataTableViewOptions<TData>({
  table,
}: DataTableViewOptionsProps<TData>) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant='outline'
          size='sm'
          className='ml-auto flex h-8 lg:gap-1'
        >
          <MixerHorizontalIcon className='h-3.5 w-3.5 lg:mr-1' />
          <span className='hidden lg:block'>Hi<PERSON><PERSON> thị</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end' className='w-[150px]'>
        <DropdownMenuLabel>Hiển thị cột</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {table
          .getAllColumns()
          .filter(
            (column) =>
              typeof column.accessorFn !== 'undefined' && column.getCanHide()
          )
          .map((column) => {
            return (
              <DropdownMenuCheckboxItem
                key={column.id}
                className='capitalize'
                checked={column.getIsVisible()}
                onCheckedChange={(value) => column.toggleVisibility(!!value)}
              >
                {column.id === 'orderNumber'
                  ? 'Mã đơn hàng'
                  : column.id === 'customerName'
                  ? 'Khách hàng'
                  : column.id === 'status'
                  ? 'Trạng thái'
                  : column.id === 'orderDate'
                  ? 'Ngày đặt'
                  : column.id === 'total'
                  ? 'Tổng tiền'
                  : column.id === 'paymentStatus'
                  ? 'TT Thanh toán'
                  : column.id === 'paymentMethod'
                  ? 'PT Thanh toán'
                  : column.id}
              </DropdownMenuCheckboxItem>
            )
          })}
      </DropdownMenuContent>
    </DropdownMenu>
  )
} 