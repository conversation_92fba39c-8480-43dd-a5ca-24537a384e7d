import { useState } from 'react'
import { 
  DollarSign, 
  Percent, 
  Plus, 
  Minus, 
  Edit, 
  Save, 
  X, 
  Calculator,
  Tag
} from 'lucide-react'
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle,
  CardFooter
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Order, OrderItem } from '../data/schema'
import { formatCurrency } from '@/utils/format'

interface OrderPricingProps {
  order: Order
  onUpdatePricing?: (updatedOrder: Order) => void
  readOnly?: boolean
}

export function OrderPricing({ order, onUpdatePricing, readOnly = false }: OrderPricingProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [items, setItems] = useState<OrderItem[]>(order.items)
  const [discountTotal, setDiscountTotal] = useState<number>(order.discountTotal || 0)
  const [taxTotal, setTaxTotal] = useState<number>(order.taxTotal || 0)
  const [shippingTotal, setShippingTotal] = useState<number>(order.shippingTotal || 0)
  const [orderDiscount, setOrderDiscount] = useState<number>(0)
  const [orderDiscountType, setOrderDiscountType] = useState<'percentage' | 'fixed'>('percentage')

  // Tính toán tổng tiền
  const calculateSubtotal = () => {
    return items.reduce((sum, item) => sum + item.total, 0)
  }

  const calculateTotal = () => {
    return calculateSubtotal() - discountTotal + taxTotal + shippingTotal
  }

  // Xử lý cập nhật số lượng sản phẩm
  const handleQuantityChange = (itemId: string, newQuantity: number) => {
    if (newQuantity < 1) return

    setItems(prevItems => 
      prevItems.map(item => {
        if (item.id === itemId) {
          const newTotal = item.price * newQuantity
          return { ...item, quantity: newQuantity, total: newTotal }
        }
        return item
      })
    )
  }

  // Xử lý cập nhật giá sản phẩm
  const handlePriceChange = (itemId: string, newPrice: number) => {
    if (newPrice < 0) return

    setItems(prevItems => 
      prevItems.map(item => {
        if (item.id === itemId) {
          const newTotal = newPrice * item.quantity
          return { ...item, price: newPrice, total: newTotal }
        }
        return item
      })
    )
  }

  // Xử lý cập nhật chiết khấu sản phẩm
  const handleDiscountChange = (itemId: string, newDiscount: number, type: 'percentage' | 'fixed') => {
    if (newDiscount < 0) return

    setItems(prevItems => 
      prevItems.map(item => {
        if (item.id === itemId) {
          let discountAmount = 0
          if (type === 'percentage') {
            discountAmount = (item.price * item.quantity) * (newDiscount / 100)
          } else {
            discountAmount = newDiscount
          }
          
          const newTotal = (item.price * item.quantity) - discountAmount
          return { 
            ...item, 
            discount: newDiscount,
            discountType: type,
            total: newTotal 
          }
        }
        return item
      })
    )
  }

  // Xử lý áp dụng chiết khấu cho toàn bộ đơn hàng
  const applyOrderDiscount = () => {
    let discountAmount = 0
    const subtotal = calculateSubtotal()
    
    if (orderDiscountType === 'percentage') {
      discountAmount = subtotal * (orderDiscount / 100)
    } else {
      discountAmount = orderDiscount
    }
    
    setDiscountTotal(discountAmount)
  }

  // Xử lý lưu thay đổi
  const handleSave = () => {
    const updatedOrder = {
      ...order,
      items,
      subtotal: calculateSubtotal(),
      discountTotal,
      taxTotal,
      shippingTotal,
      total: calculateTotal()
    }
    
    if (onUpdatePricing) {
      onUpdatePricing(updatedOrder)
    }
    
    setIsEditing(false)
  }

  // Xử lý hủy thay đổi
  const handleCancel = () => {
    setItems(order.items)
    setDiscountTotal(order.discountTotal || 0)
    setTaxTotal(order.taxTotal || 0)
    setShippingTotal(order.shippingTotal || 0)
    setIsEditing(false)
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-base font-medium">Chi tiết giá</CardTitle>
            <CardDescription>
              Thông tin về giá, chiết khấu và thuế
            </CardDescription>
          </div>
          {!readOnly && (
            <div>
              {isEditing ? (
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm" onClick={handleCancel}>
                    <X className="h-4 w-4 mr-2" />
                    Hủy
                  </Button>
                  <Button size="sm" onClick={handleSave}>
                    <Save className="h-4 w-4 mr-2" />
                    Lưu
                  </Button>
                </div>
              ) : (
                <Button variant="outline" size="sm" onClick={() => setIsEditing(true)}>
                  <Edit className="h-4 w-4 mr-2" />
                  Chỉnh sửa
                </Button>
              )}
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[300px]">Sản phẩm</TableHead>
                <TableHead className="text-right">Đơn giá</TableHead>
                <TableHead className="text-right">Số lượng</TableHead>
                <TableHead className="text-right">Chiết khấu</TableHead>
                <TableHead className="text-right">Thành tiền</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {items.map((item) => (
                <TableRow key={item.id}>
                  <TableCell className="font-medium">
                    <div>
                      {item.name}
                      {item.sku && (
                        <div className="text-xs text-muted-foreground">
                          SKU: {item.sku}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    {isEditing ? (
                      <div className="flex items-center justify-end">
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                        <Input
                          type="number"
                          value={item.price}
                          onChange={(e) => handlePriceChange(item.id, parseFloat(e.target.value))}
                          className="w-20 text-right"
                        />
                      </div>
                    ) : (
                      formatCurrency(item.price)
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    {isEditing ? (
                      <div className="flex items-center justify-end">
                        <Button
                          variant="outline"
                          size="icon"
                          className="h-6 w-6"
                          onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                          disabled={item.quantity <= 1}
                        >
                          <Minus className="h-3 w-3" />
                        </Button>
                        <Input
                          type="number"
                          value={item.quantity}
                          onChange={(e) => handleQuantityChange(item.id, parseInt(e.target.value))}
                          className="w-12 mx-1 text-center"
                          min={1}
                        />
                        <Button
                          variant="outline"
                          size="icon"
                          className="h-6 w-6"
                          onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                      </div>
                    ) : (
                      item.quantity
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    {isEditing ? (
                      <div className="flex items-center justify-end gap-1">
                        <Input
                          type="number"
                          value={item.discount || 0}
                          onChange={(e) => handleDiscountChange(
                            item.id, 
                            parseFloat(e.target.value), 
                            item.discountType || 'percentage'
                          )}
                          className="w-16 text-right"
                          min={0}
                        />
                        <Select
                          value={item.discountType || 'percentage'}
                          onValueChange={(value) => handleDiscountChange(
                            item.id, 
                            item.discount || 0, 
                            value as 'percentage' | 'fixed'
                          )}
                        >
                          <SelectTrigger className="w-20">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="percentage">%</SelectItem>
                            <SelectItem value="fixed">$</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    ) : (
                      item.discount ? (
                        <Badge variant="outline">
                          {item.discount}{item.discountType === 'percentage' ? '%' : '$'}
                        </Badge>
                      ) : (
                        '-'
                      )
                    )}
                  </TableCell>
                  <TableCell className="text-right font-medium">
                    {formatCurrency(item.total)}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {isEditing && (
            <div className="bg-muted/50 p-4 rounded-md">
              <h3 className="text-sm font-medium mb-3">Chiết khấu đơn hàng</h3>
              <div className="flex items-end gap-2">
                <div className="space-y-2">
                  <Label htmlFor="discount-value">Giá trị</Label>
                  <Input
                    id="discount-value"
                    type="number"
                    value={orderDiscount}
                    onChange={(e) => setOrderDiscount(parseFloat(e.target.value))}
                    className="w-32"
                    min={0}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="discount-type">Loại</Label>
                  <Select
                    value={orderDiscountType}
                    onValueChange={(value) => setOrderDiscountType(value as 'percentage' | 'fixed')}
                  >
                    <SelectTrigger id="discount-type" className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="percentage">Phần trăm (%)</SelectItem>
                      <SelectItem value="fixed">Cố định ($)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button onClick={applyOrderDiscount} className="mb-0.5">
                  Áp dụng
                </Button>
              </div>
            </div>
          )}

          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Tạm tính:</span>
              <span>{formatCurrency(calculateSubtotal())}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Chiết khấu:</span>
              {isEditing ? (
                <div className="flex items-center gap-2">
                  <Input
                    type="number"
                    value={discountTotal}
                    onChange={(e) => setDiscountTotal(parseFloat(e.target.value))}
                    className="w-24 text-right"
                    min={0}
                  />
                </div>
              ) : (
                <span>-{formatCurrency(discountTotal)}</span>
              )}
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Thuế:</span>
              {isEditing ? (
                <div className="flex items-center gap-2">
                  <Input
                    type="number"
                    value={taxTotal}
                    onChange={(e) => setTaxTotal(parseFloat(e.target.value))}
                    className="w-24 text-right"
                    min={0}
                  />
                </div>
              ) : (
                <span>{formatCurrency(taxTotal)}</span>
              )}
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Phí vận chuyển:</span>
              {isEditing ? (
                <div className="flex items-center gap-2">
                  <Input
                    type="number"
                    value={shippingTotal}
                    onChange={(e) => setShippingTotal(parseFloat(e.target.value))}
                    className="w-24 text-right"
                    min={0}
                  />
                </div>
              ) : (
                <span>{formatCurrency(shippingTotal)}</span>
              )}
            </div>
            <Separator />
            <div className="flex justify-between font-medium">
              <span>Tổng cộng:</span>
              <span className="text-lg">{formatCurrency(calculateTotal())}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
