import { Row } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  IconDotsVertical,
  IconEdit,
  IconEye,
  IconTrash,
  IconFileInvoice,
  IconTruckDelivery,
} from '@tabler/icons-react'
import { showSubmittedData } from '@/utils/show-submitted-data'
import { Order } from '../data/schema'

interface DataTableRowActionsProps {
  row: Row<Order>
}

export function DataTableRowActions({ row }: DataTableRowActionsProps) {
  const order = row.original

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant='ghost'
          className='flex h-8 w-8 p-0 data-[state=open]:bg-muted'
        >
          <IconDotsVertical className='h-4 w-4' />
          <span className='sr-only'>Open menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end' className='w-[160px]'>
        <DropdownMenuItem onClick={() => showSubmittedData(order)}>
          <IconEye className='mr-2 h-3.5 w-3.5 text-muted-foreground' />
          <span>View Details</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => showSubmittedData(order)}>
          <IconEdit className='mr-2 h-3.5 w-3.5 text-muted-foreground' />
          <span>Edit</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => showSubmittedData(order)}>
          <IconFileInvoice className='mr-2 h-3.5 w-3.5 text-muted-foreground' />
          <span>Invoice</span>
        </DropdownMenuItem>
        {order.status !== 'Completed' && order.status !== 'Cancelled' && (
          <DropdownMenuItem onClick={() => showSubmittedData(order)}>
            <IconTruckDelivery className='mr-2 h-3.5 w-3.5 text-muted-foreground' />
            <span>Track Order</span>
          </DropdownMenuItem>
        )}
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={() => showSubmittedData(order)}
          className='text-red-600'
        >
          <IconTrash className='mr-2 h-3.5 w-3.5 text-muted-foreground' />
          <span>Delete</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
} 