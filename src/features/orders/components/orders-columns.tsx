import { ColumnDef } from '@tanstack/react-table'
import { cn } from '@/lib/utils'
import { Checkbox } from '@/components/ui/checkbox'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Order } from '../data/schema'
import { DataTableColumnHeader } from '@/components/ui/data-table'
import { DataTableRowActions } from './data-table-row-actions'
import { StatusBadge } from '@/components/ui/status-badge'
import { OrderNameLink } from './order-name-link'
import { formatCurrency } from '@/utils/format'

export const columns: ColumnDef<Order>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label='Select all'
        className='translate-y-[2px]'
      />
    ),
    meta: {
      className: cn(
        'sticky md:table-cell left-0 z-10 rounded-tl pr-2',
        'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted'
      ),
    },
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label='Select row'
        className='translate-y-[2px]'
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'orderNumber',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Order No' />
    ),
    cell: ({ row }) => {
      const orderNumber = row.getValue('orderNumber') as string
      const id = row.original.id
      
      return (
        <OrderNameLink id={id} orderNumber={orderNumber} />
      )
    },
    meta: {
      className: cn(
        'drop-shadow-[0_1px_2px_rgb(0_0_0_/_0.1)] dark:drop-shadow-[0_1px_2px_rgb(255_255_255_/_0.1)] lg:drop-shadow-none',
        'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
        'sticky left-6 md:table-cell'
      ),
    },
    enableHiding: false,
  },
  {
    accessorKey: 'customerName',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Customer' />
    ),
    cell: ({ row }) => {
      const customerName = row.getValue('customerName') as string
      const customerAvatar = row.original.customerAvatar
      
      return (
        <div className="flex items-center gap-2">
          <Avatar className="h-8 w-8">
            <AvatarImage src={customerAvatar} alt={customerName} />
            <AvatarFallback>{customerName.charAt(0)}</AvatarFallback>
          </Avatar>
          <span>{customerName}</span>
        </div>
      )
    },
    enableHiding: false,
  },
  {
    accessorKey: 'status',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Status' />
    ),
    cell: ({ row }) => {
      const { status } = row.original
      
      const getVariant = () => {
        if (status.toLowerCase() === 'completed') return 'done'
        if (status.toLowerCase() === 'processing') return 'inProgress'
        if (status.toLowerCase() === 'cancelled') return 'cancelled'
        if (status.toLowerCase() === 'new') return 'todo'
        return 'default'
      }
      
      return (
        <div className='w-[100px]'>
          <StatusBadge status={status} variant={getVariant()} />
        </div>
      )
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
    enableHiding: false,
    enableSorting: false,
  },
  {
    accessorKey: 'orderDate',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Order Date' />
    ),
    cell: ({ row }) => {
      return <div>{row.getValue('orderDate')}</div>
    },
    enableSorting: true,
  },
  {
    accessorKey: 'total',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Total' />
    ),
    cell: ({ row }) => {
      const total = row.getValue('total') as number
      return <div>{formatCurrency(total)}</div>
    },
    enableSorting: true,
  },
  {
    accessorKey: 'paymentStatus',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Payment Status' />
    ),
    cell: ({ row }) => {
      const paymentStatus = row.getValue('paymentStatus') as string
      
      const getVariant = () => {
        if (paymentStatus.toLowerCase() === 'paid') return 'done'
        if (paymentStatus.toLowerCase() === 'pending') return 'todo'
        if (paymentStatus.toLowerCase() === 'refunded') return 'cancelled'
        return 'default'
      }
      
      return (
        <div className='w-[100px]'>
          <StatusBadge status={paymentStatus} variant={getVariant()} />
        </div>
      )
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
    enableSorting: false,
  },
  {
    accessorKey: 'paymentMethod',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Payment Method' />
    ),
    cell: ({ row }) => {
      const paymentMethod = row.getValue('paymentMethod') as string
      return <div className='capitalize'>{paymentMethod || '-'}</div>
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
    enableSorting: false,
  },
  {
    id: 'actions',
    cell: ({ row }) => <DataTableRowActions row={row} />,
  },
] 