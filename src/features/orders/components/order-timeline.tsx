import { useState } from 'react'
import { format } from 'date-fns'
import { vi } from 'date-fns/locale'
import { 
  Calendar, 
  Clock, 
  Filter as FilterIcon, 
  ShoppingCart, 
  CreditCard, 
  Truck, 
  CheckCircle, 
  XCircle, 
  MessageSquare, 
  User,
  Plus
} from 'lucide-react'
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Order, TimelineEvent, OrderStatus, PaymentStatus } from '../data/schema'

interface OrderTimelineProps {
  order: Order
}

export function OrderTimeline({ order }: OrderTimelineProps) {
  // Nhóm các sự kiện theo ngày
  const groupEventsByDate = () => {
    if (!order.timeline || order.timeline.length === 0) {
      return []
    }
    
    const groups: Record<string, TimelineEvent[]> = {}
    
    order.timeline.forEach(event => {
      const date = new Date(event.timestamp).toISOString().split('T')[0]
      if (!groups[date]) {
        groups[date] = []
      }
      groups[date].push(event)
    })
    
    // Sắp xếp các nhóm theo ngày giảm dần (mới nhất lên đầu)
    return Object.entries(groups)
      .sort(([dateA], [dateB]) => new Date(dateB).getTime() - new Date(dateA).getTime())
      .map(([date, events]) => ({
        date,
        events: events.sort((a, b) => 
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        )
      }))
  }

  const eventGroups = groupEventsByDate()

  // Hàm lấy icon cho loại sự kiện
  const getEventIcon = (type: string, status?: string) => {
    switch (type) {
      case 'order_created':
        return <ShoppingCart className="h-3 w-3 text-primary" />
      case 'payment_received':
      case 'payment_pending':
      case 'payment_refunded':
        return <CreditCard className="h-3 w-3 text-primary" />
      case 'status_changed':
        if (status === OrderStatus.SHIPPED) {
          return <Truck className="h-3 w-3 text-primary" />
        } else if (status === OrderStatus.COMPLETED) {
          return <CheckCircle className="h-3 w-3 text-primary" />
        } else if (status === OrderStatus.CANCELLED) {
          return <XCircle className="h-3 w-3 text-primary" />
        }
        return <ShoppingCart className="h-3 w-3 text-primary" />
      case 'note_added':
        return <MessageSquare className="h-3 w-3 text-primary" />
      default:
        return <Calendar className="h-3 w-3 text-primary" />
    }
  }

  // Hàm định dạng ngày
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)
    
    if (date.toDateString() === today.toDateString()) {
      return 'Hôm nay'
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Hôm qua'
    } else {
      return format(date, 'dd/MM/yyyy', { locale: vi })
    }
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base font-medium">Lịch sử đơn hàng</CardTitle>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Calendar className="h-3.5 w-3.5 mr-2" />
              Lọc theo ngày
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <FilterIcon className="h-3.5 w-3.5 mr-2" />
                  Lọc sự kiện
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>Tất cả sự kiện</DropdownMenuItem>
                <DropdownMenuItem>Thay đổi trạng thái</DropdownMenuItem>
                <DropdownMenuItem>Thanh toán</DropdownMenuItem>
                <DropdownMenuItem>Ghi chú</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <Button variant="outline" size="sm">
              <Plus className="h-3.5 w-3.5 mr-2" />
              Thêm ghi chú
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <ScrollArea className="h-[500px]">
          <div className="px-4 py-2 space-y-6">
            {eventGroups.length > 0 ? (
              eventGroups.map(group => (
                <div key={group.date} className="relative pl-6 pb-2">
                  <div className="absolute top-0 left-0 w-px h-full bg-border"></div>
                  <div className="absolute top-0 left-0 w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center -translate-x-1/2">
                    <Calendar className="h-3 w-3 text-primary" />
                  </div>
                  <div className="font-medium text-sm text-muted-foreground mb-4">
                    {formatDate(group.date)}
                  </div>

                  <div className="space-y-4">
                    {group.events.map(event => (
                      <TimelineItem
                        key={event.id}
                        avatar={event.userName || 'System'}
                        name={event.userName || 'System'}
                        title={event.title}
                        description={event.description}
                        icon={getEventIcon(event.type, event.status)}
                        time={format(new Date(event.timestamp), 'HH:mm', { locale: vi })}
                      />
                    ))}
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <ShoppingCart className="h-12 w-12 mx-auto mb-4 opacity-20" />
                <p>Chưa có sự kiện nào</p>
              </div>
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  )
}

interface TimelineItemProps {
  avatar: string
  name: string
  title: string
  description?: string
  icon: React.ReactNode
  time: string
}

function TimelineItem({
  avatar,
  name,
  title,
  description,
  icon,
  time
}: TimelineItemProps) {
  // Generate initials from name
  const getInitials = (name: string) => {
    if (!name) return "NA"
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
  }

  const isSystem = name.toLowerCase() === 'system'

  return (
    <div className="flex items-start gap-3 py-4 border-b border-border last:border-none">
      <Avatar className="h-8 w-8 mt-0.5">
        <AvatarFallback className={`text-xs ${isSystem ? 'bg-blue-100 text-blue-600' : 'bg-primary/10 text-primary'}`}>
          {getInitials(avatar)}
        </AvatarFallback>
      </Avatar>
      <div className="flex-1 min-w-0">
        <div className="text-sm">
          <span className="font-medium">{name}</span>
          <span className="text-muted-foreground ml-1">{title}</span>
          {description && (
            <div className="mt-1 text-sm bg-muted/50 rounded-md p-1.5 break-words">
              {description}
            </div>
          )}
        </div>
        <div className="text-xs text-muted-foreground mt-1 flex items-center">
          <Clock className="h-3 w-3 mr-1 inline-block" />
          {time}
        </div>
      </div>
    </div>
  )
}
