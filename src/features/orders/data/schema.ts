import { z } from 'zod'

// Enum for order status
export const OrderStatus = {
  NEW: 'New',
  PROCESSING: 'Processing',
  SHIPPED: 'Shipped',
  DELIVERED: 'Delivered',
  COMPLETED: 'Completed',
  CANCELLED: 'Cancelled',
  ON_HOLD: 'On Hold'
} as const

// Enum for payment status
export const PaymentStatus = {
  PENDING: 'Pending',
  PAID: 'Paid',
  PARTIALLY_PAID: 'Partially Paid',
  REFUNDED: 'Refunded',
  FAILED: 'Failed'
} as const

// Enum for payment methods
export const PaymentMethod = {
  CREDIT_CARD: 'Credit Card',
  BANK_TRANSFER: 'Bank Transfer',
  PAYPAL: 'PayPal',
  CASH: 'Cash',
  OTHER: 'Other'
} as const

// Schema for order items
export const orderItemSchema = z.object({
  id: z.string(),
  name: z.string(),
  sku: z.string().optional(),
  quantity: z.number(),
  price: z.number(),
  discount: z.number().optional(),
  discountType: z.enum(['percentage', 'fixed']).optional(),
  tax: z.number().optional(),
  total: z.number(),
  productId: z.string().optional(),
  options: z.array(
    z.object({
      name: z.string(),
      value: z.string()
    })
  ).optional()
})

// Schema for timeline events
export const timelineEventSchema = z.object({
  id: z.string(),
  type: z.string(),
  title: z.string(),
  description: z.string().optional(),
  timestamp: z.string(),
  status: z.string().optional(),
  userId: z.string().optional(),
  userName: z.string().optional()
})

// Schema for order
export const orderSchema = z.object({
  id: z.string(),
  orderNumber: z.string(),
  customerName: z.string(),
  customerId: z.string().optional(),
  customerAvatar: z.string().optional(),
  customerEmail: z.string().optional(),
  customerPhone: z.string().optional(),
  status: z.string(),
  orderDate: z.string(),
  subtotal: z.number().optional(),
  discountTotal: z.number().optional(),
  taxTotal: z.number().optional(),
  shippingTotal: z.number().optional(),
  total: z.number(),
  paymentStatus: z.string(),
  paymentMethod: z.string(),
  items: z.array(orderItemSchema),
  billingAddress: z.string().optional(),
  shippingAddress: z.string(),
  notes: z.string().optional(),
  internalNotes: z.string().optional(),
  tags: z.array(z.string()).optional(),
  timeline: z.array(timelineEventSchema).optional(),
  createdBy: z.string().optional(),
  createdAt: z.string().optional(),
  lastModified: z.string(),
  opportunityId: z.string().optional(),
  campaignId: z.string().optional(),
  attachments: z.array(
    z.object({
      id: z.string(),
      name: z.string(),
      url: z.string(),
      type: z.string(),
      size: z.number().optional()
    })
  ).optional()
})

export const orderListSchema = z.array(orderSchema)

export type OrderStatusType = typeof OrderStatus[keyof typeof OrderStatus]
export type PaymentStatusType = typeof PaymentStatus[keyof typeof PaymentStatus]
export type PaymentMethodType = typeof PaymentMethod[keyof typeof PaymentMethod]
export type Order = z.infer<typeof orderSchema>
export type OrderItem = z.infer<typeof orderItemSchema>
export type TimelineEvent = z.infer<typeof timelineEventSchema>