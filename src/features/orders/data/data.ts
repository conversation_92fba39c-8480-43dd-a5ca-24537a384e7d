import { Order, OrderStatus, PaymentStatus, PaymentMethod } from './schema'

export const orders: Order[] = [
  {
    id: '1',
    orderNumber: 'ORD-2023-001',
    customerName: '<PERSON>',
    customerId: 'CUST-001',
    customerAvatar: '/avatars/avatar-1.png',
    customerEmail: '<EMAIL>',
    customerPhone: '+****************',
    status: OrderStatus.COMPLETED,
    orderDate: '2023-10-15',
    subtotal: 1250.99,
    discountTotal: 0,
    taxTotal: 0,
    shippingTotal: 0,
    total: 1250.99,
    paymentStatus: PaymentStatus.PAID,
    paymentMethod: PaymentMethod.CREDIT_CARD,
    items: [
      {
        id: '1-1',
        name: 'Premium Plan Subscription',
        sku: 'PREMIUM-PLAN',
        quantity: 1,
        price: 999.99,
        discount: 0,
        tax: 0,
        total: 999.99,
        productId: 'PROD-001',
      },
      {
        id: '1-2',
        name: 'Support Add-on',
        sku: 'SUPPORT-ADD',
        quantity: 1,
        price: 251.00,
        discount: 0,
        tax: 0,
        total: 251.00,
        productId: 'PROD-002',
      },
    ],
    billingAddress: '123 Main St, Suite 101, San Francisco, CA 94105',
    shippingAddress: '123 Main St, Suite 101, San Francisco, CA 94105',
    notes: 'Customer requested expedited processing',
    internalNotes: 'VIP customer, provide priority handling',
    tags: ['premium', 'subscription', 'vip'],
    timeline: [
      {
        id: 'event-1-1',
        type: 'order_created',
        title: 'Đơn hàng được tạo',
        timestamp: '2023-10-15T10:30:00Z',
        userId: 'USER-001',
        userName: 'Sales Agent'
      },
      {
        id: 'event-1-2',
        type: 'payment_received',
        title: 'Thanh toán đã nhận',
        description: 'Thanh toán đầy đủ qua Credit Card',
        timestamp: '2023-10-15T10:35:00Z',
        status: PaymentStatus.PAID,
        userId: 'SYSTEM',
        userName: 'System'
      },
      {
        id: 'event-1-3',
        type: 'status_changed',
        title: 'Trạng thái đơn hàng đã thay đổi',
        description: 'Trạng thái đã thay đổi từ New sang Processing',
        timestamp: '2023-10-15T10:40:00Z',
        status: OrderStatus.PROCESSING,
        userId: 'USER-001',
        userName: 'Sales Agent'
      },
      {
        id: 'event-1-4',
        type: 'status_changed',
        title: 'Trạng thái đơn hàng đã thay đổi',
        description: 'Trạng thái đã thay đổi từ Processing sang Completed',
        timestamp: '2023-10-16T14:20:00Z',
        status: OrderStatus.COMPLETED,
        userId: 'USER-002',
        userName: 'Order Manager'
      }
    ],
    createdBy: 'USER-001',
    createdAt: '2023-10-15T10:30:00Z',
    lastModified: '2023-10-16T14:20:00Z',
    opportunityId: 'OPP-001',
    attachments: [
      {
        id: 'att-1-1',
        name: 'Invoice-ORD-2023-001.pdf',
        url: '/attachments/Invoice-ORD-2023-001.pdf',
        type: 'application/pdf',
        size: 125000
      }
    ]
  },
  {
    id: '2',
    orderNumber: 'ORD-2023-002',
    customerName: 'Jane Smith',
    customerId: 'CUST-002',
    customerAvatar: '/avatars/avatar-2.png',
    customerEmail: '<EMAIL>',
    customerPhone: '+****************',
    status: OrderStatus.PROCESSING,
    orderDate: '2023-10-18',
    subtotal: 850.50,
    discountTotal: 0,
    taxTotal: 0,
    shippingTotal: 0,
    total: 850.50,
    paymentStatus: PaymentStatus.PAID,
    paymentMethod: PaymentMethod.PAYPAL,
    items: [
      {
        id: '2-1',
        name: 'Standard Plan Subscription',
        sku: 'STANDARD-PLAN',
        quantity: 1,
        price: 599.99,
        discount: 0,
        tax: 0,
        total: 599.99,
        productId: 'PROD-003',
      },
      {
        id: '2-2',
        name: 'Data Migration',
        sku: 'DATA-MIGR',
        quantity: 1,
        price: 250.51,
        discount: 0,
        tax: 0,
        total: 250.51,
        productId: 'PROD-004',
      },
    ],
    billingAddress: '456 Market St, Apt 301, New York, NY 10001',
    shippingAddress: '456 Market St, Apt 301, New York, NY 10001',
    tags: ['standard', 'migration'],
    timeline: [
      {
        id: 'event-2-1',
        type: 'order_created',
        title: 'Đơn hàng được tạo',
        timestamp: '2023-10-18T09:15:00Z',
        userId: 'USER-003',
        userName: 'Sales Agent'
      },
      {
        id: 'event-2-2',
        type: 'payment_received',
        title: 'Thanh toán đã nhận',
        description: 'Thanh toán đầy đủ qua PayPal',
        timestamp: '2023-10-18T09:20:00Z',
        status: PaymentStatus.PAID,
        userId: 'SYSTEM',
        userName: 'System'
      },
      {
        id: 'event-2-3',
        type: 'status_changed',
        title: 'Trạng thái đơn hàng đã thay đổi',
        description: 'Trạng thái đã thay đổi từ New sang Processing',
        timestamp: '2023-10-18T09:25:00Z',
        status: OrderStatus.PROCESSING,
        userId: 'USER-003',
        userName: 'Sales Agent'
      }
    ],
    createdBy: 'USER-003',
    createdAt: '2023-10-18T09:15:00Z',
    lastModified: '2023-10-19T11:30:00Z',
  },
  {
    id: '3',
    orderNumber: 'ORD-2023-003',
    customerName: 'Robert Johnson',
    customerId: 'CUST-003',
    customerAvatar: '/avatars/avatar-3.png',
    customerEmail: '<EMAIL>',
    customerPhone: '+****************',
    status: OrderStatus.CANCELLED,
    orderDate: '2023-10-20',
    subtotal: 499.99,
    discountTotal: 0,
    taxTotal: 0,
    shippingTotal: 0,
    total: 499.99,
    paymentStatus: PaymentStatus.REFUNDED,
    paymentMethod: PaymentMethod.CREDIT_CARD,
    items: [
      {
        id: '3-1',
        name: 'Basic Plan Subscription',
        sku: 'BASIC-PLAN',
        quantity: 1,
        price: 499.99,
        discount: 0,
        tax: 0,
        total: 499.99,
        productId: 'PROD-005',
      },
    ],
    billingAddress: '789 Oak St, Chicago, IL 60601',
    shippingAddress: '789 Oak St, Chicago, IL 60601',
    notes: 'Customer cancelled due to change in requirements',
    internalNotes: 'Customer may return in Q1 2024, follow up needed',
    tags: ['basic', 'cancelled'],
    timeline: [
      {
        id: 'event-3-1',
        type: 'order_created',
        title: 'Đơn hàng được tạo',
        timestamp: '2023-10-20T14:30:00Z',
        userId: 'USER-004',
        userName: 'Sales Agent'
      },
      {
        id: 'event-3-2',
        type: 'payment_received',
        title: 'Thanh toán đã nhận',
        description: 'Thanh toán đầy đủ qua Credit Card',
        timestamp: '2023-10-20T14:35:00Z',
        status: PaymentStatus.PAID,
        userId: 'SYSTEM',
        userName: 'System'
      },
      {
        id: 'event-3-3',
        type: 'status_changed',
        title: 'Trạng thái đơn hàng đã thay đổi',
        description: 'Trạng thái đã thay đổi từ New sang Processing',
        timestamp: '2023-10-20T14:40:00Z',
        status: OrderStatus.PROCESSING,
        userId: 'USER-004',
        userName: 'Sales Agent'
      },
      {
        id: 'event-3-4',
        type: 'note_added',
        title: 'Ghi chú đã được thêm',
        description: 'Khách hàng yêu cầu hủy đơn hàng do thay đổi yêu cầu',
        timestamp: '2023-10-21T09:15:00Z',
        userId: 'USER-004',
        userName: 'Sales Agent'
      },
      {
        id: 'event-3-5',
        type: 'status_changed',
        title: 'Trạng thái đơn hàng đã thay đổi',
        description: 'Trạng thái đã thay đổi từ Processing sang Cancelled',
        timestamp: '2023-10-21T09:20:00Z',
        status: OrderStatus.CANCELLED,
        userId: 'USER-005',
        userName: 'Order Manager'
      },
      {
        id: 'event-3-6',
        type: 'payment_refunded',
        title: 'Thanh toán đã hoàn trả',
        description: 'Hoàn trả đầy đủ qua Credit Card',
        timestamp: '2023-10-21T10:30:00Z',
        status: PaymentStatus.REFUNDED,
        userId: 'USER-006',
        userName: 'Finance Manager'
      }
    ],
    createdBy: 'USER-004',
    createdAt: '2023-10-20T14:30:00Z',
    lastModified: '2023-10-21T10:30:00Z',
    opportunityId: 'OPP-003',
  },
  {
    id: '4',
    orderNumber: 'ORD-2023-004',
    customerName: 'Sarah Williams',
    customerId: 'CUST-004',
    customerAvatar: '/avatars/avatar-4.png',
    customerEmail: '<EMAIL>',
    customerPhone: '+****************',
    status: OrderStatus.NEW,
    orderDate: '2023-10-22',
    subtotal: 1999.98,
    discountTotal: 0,
    taxTotal: 0,
    shippingTotal: 0,
    total: 1999.98,
    paymentStatus: PaymentStatus.PENDING,
    paymentMethod: PaymentMethod.BANK_TRANSFER,
    items: [
      {
        id: '4-1',
        name: 'Enterprise Plan Subscription',
        sku: 'ENTERPRISE-PLAN',
        quantity: 1,
        price: 1499.99,
        discount: 0,
        tax: 0,
        total: 1499.99,
        productId: 'PROD-006',
      },
      {
        id: '4-2',
        name: 'Custom Integration',
        sku: 'CUSTOM-INT',
        quantity: 1,
        price: 499.99,
        discount: 0,
        tax: 0,
        total: 499.99,
        productId: 'PROD-007',
      },
    ],
    billingAddress: '321 Pine St, Suite 200, Seattle, WA 98101',
    shippingAddress: '321 Pine St, Suite 200, Seattle, WA 98101',
    internalNotes: 'Enterprise client, high priority',
    tags: ['enterprise', 'integration', 'high-priority'],
    timeline: [
      {
        id: 'event-4-1',
        type: 'order_created',
        title: 'Đơn hàng được tạo',
        timestamp: '2023-10-22T11:45:00Z',
        userId: 'USER-007',
        userName: 'Enterprise Sales'
      },
      {
        id: 'event-4-2',
        type: 'payment_pending',
        title: 'Thanh toán đang chờ xử lý',
        description: 'Đang chờ thanh toán qua Bank Transfer',
        timestamp: '2023-10-22T11:50:00Z',
        status: PaymentStatus.PENDING,
        userId: 'SYSTEM',
        userName: 'System'
      }
    ],
    createdBy: 'USER-007',
    createdAt: '2023-10-22T11:45:00Z',
    lastModified: '2023-10-22T11:50:00Z',
    opportunityId: 'OPP-004',
    campaignId: 'CAMP-002',
  },
  {
    id: '5',
    orderNumber: 'ORD-2023-005',
    customerName: 'Michael Brown',
    customerId: 'CUST-005',
    customerAvatar: '/avatars/avatar-5.png',
    customerEmail: '<EMAIL>',
    customerPhone: '+****************',
    status: OrderStatus.COMPLETED,
    orderDate: '2023-10-14',
    subtotal: 750.00,
    discountTotal: 0,
    taxTotal: 0,
    shippingTotal: 0,
    total: 750.00,
    paymentStatus: PaymentStatus.PAID,
    paymentMethod: PaymentMethod.CREDIT_CARD,
    items: [
      {
        id: '5-1',
        name: 'Standard Plan Subscription',
        sku: 'STANDARD-PLAN',
        quantity: 1,
        price: 599.99,
        discount: 0,
        tax: 0,
        total: 599.99,
        productId: 'PROD-003',
      },
      {
        id: '5-2',
        name: 'Additional User License',
        sku: 'USER-LICENSE',
        quantity: 3,
        price: 50.00,
        discount: 0,
        tax: 0,
        total: 150.01,
        productId: 'PROD-009',
      },
    ],
    billingAddress: '555 Elm St, Austin, TX 78701',
    shippingAddress: '555 Elm St, Austin, TX 78701',
    tags: ['standard', 'user-license'],
    timeline: [
      {
        id: 'event-5-1',
        type: 'order_created',
        title: 'Đơn hàng được tạo',
        timestamp: '2023-10-14T09:30:00Z',
        userId: 'USER-010',
        userName: 'Sales Agent'
      },
      {
        id: 'event-5-2',
        type: 'payment_received',
        title: 'Thanh toán đã nhận',
        description: 'Thanh toán đầy đủ qua Credit Card',
        timestamp: '2023-10-14T09:35:00Z',
        status: PaymentStatus.PAID,
        userId: 'SYSTEM',
        userName: 'System'
      },
      {
        id: 'event-5-3',
        type: 'status_changed',
        title: 'Trạng thái đơn hàng đã thay đổi',
        description: 'Trạng thái đã thay đổi từ New sang Processing',
        timestamp: '2023-10-14T09:40:00Z',
        status: OrderStatus.PROCESSING,
        userId: 'USER-010',
        userName: 'Sales Agent'
      },
      {
        id: 'event-5-4',
        type: 'status_changed',
        title: 'Trạng thái đơn hàng đã thay đổi',
        description: 'Trạng thái đã thay đổi từ Processing sang Completed',
        timestamp: '2023-10-15T14:20:00Z',
        status: OrderStatus.COMPLETED,
        userId: 'USER-011',
        userName: 'Order Manager'
      }
    ],
    createdBy: 'USER-010',
    createdAt: '2023-10-14T09:30:00Z',
    lastModified: '2023-10-15T14:20:00Z',
  },
  {
    id: '6',
    orderNumber: 'ORD-2023-006',
    customerName: 'Emily Davis',
    customerAvatar: '/avatars/avatar-6.png',
    status: 'Processing',
    orderDate: '2023-10-23',
    total: 1299.99,
    paymentStatus: 'Paid',
    paymentMethod: 'PayPal',
    items: [
      {
        id: '6-1',
        name: 'Premium Plan Subscription',
        quantity: 1,
        price: 999.99,
        total: 999.99,
      },
      {
        id: '6-2',
        name: 'Priority Support',
        quantity: 1,
        price: 300.00,
        total: 300.00,
      },
    ],
    shippingAddress: '777 Maple Ave, Denver, CO 80202',
    lastModified: '2023-10-24',
  },
  {
    id: '7',
    orderNumber: 'ORD-2023-007',
    customerName: 'David Wilson',
    customerAvatar: '/avatars/avatar-7.png',
    status: 'Completed',
    orderDate: '2023-10-10',
    total: 499.99,
    paymentStatus: 'Paid',
    paymentMethod: 'Credit Card',
    items: [
      {
        id: '7-1',
        name: 'Basic Plan Subscription',
        quantity: 1,
        price: 499.99,
        total: 499.99,
      },
    ],
    shippingAddress: '888 Cherry St, Portland, OR 97201',
    lastModified: '2023-10-11',
  },
  {
    id: '8',
    orderNumber: 'ORD-2023-008',
    customerName: 'Jennifer Martinez',
    customerAvatar: '/avatars/avatar-8.png',
    status: 'New',
    orderDate: '2023-10-24',
    total: 2499.99,
    paymentStatus: 'Pending',
    paymentMethod: 'Bank Transfer',
    items: [
      {
        id: '8-1',
        name: 'Enterprise Plan Subscription',
        quantity: 1,
        price: 1499.99,
        total: 1499.99,
      },
      {
        id: '8-2',
        name: 'Custom Development',
        quantity: 1,
        price: 1000.00,
        total: 1000.00,
      },
    ],
    shippingAddress: '999 Walnut St, San Diego, CA 92101',
    notes: 'Custom development requirements attached',
    lastModified: '2023-10-24',
  },
]