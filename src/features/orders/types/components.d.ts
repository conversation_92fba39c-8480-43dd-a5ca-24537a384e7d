import { Column, Row, Table } from "@tanstack/react-table";
import { Order } from "../data/schema";

// Declaration for DataTableColumnHeader
declare module "../components/data-table-column-header" {
  export interface DataTableColumnHeaderProps<TData, TValue>
    extends React.HTMLAttributes<HTMLDivElement> {
    column: Column<TData, TValue>;
    title: string;
  }

  export function DataTableColumnHeader<TData, TValue>(
    props: DataTableColumnHeaderProps<TData, TValue>
  ): JSX.Element;
}

// Declaration for DataTableRowActions
declare module "../components/data-table-row-actions" {
  export interface DataTableRowActionsProps {
    row: Row<Order>;
  }

  export function DataTableRowActions(
    props: DataTableRowActionsProps
  ): JSX.Element;
}

// Declaration for DataTableFacetedFilter
declare module "../components/data-table-faceted-filter" {
  export interface DataTableFacetedFilterProps<TData, TValue> {
    column?: Column<TData, TValue>;
    title?: string;
    options: {
      label: string;
      value: string;
      icon?: React.ComponentType<{ className?: string }>;
    }[];
  }

  export function DataTableFacetedFilter<TData, TValue>(
    props: DataTableFacetedFilterProps<TData, TValue>
  ): JSX.Element;
}

// Declaration for DataTableViewOptions
declare module "../components/data-table-view-options" {
  export interface DataTableViewOptionsProps<TData> {
    table: Table<TData>;
  }

  export function DataTableViewOptions<TData>(
    props: DataTableViewOptionsProps<TData>
  ): JSX.Element;
}

// Declaration for utils/format
declare module "@/utils/format" {
  export function formatCurrency(
    value: number,
    locale?: string,
    currency?: string
  ): string;
  
  export function formatDate(
    dateString: string,
    locale?: string
  ): string;
  
  export function formatDateTime(
    dateString: string,
    locale?: string
  ): string;
  
  export function formatNumber(
    value: number,
    locale?: string
  ): string;
  
  export function formatPercent(
    value: number,
    locale?: string
  ): string;
} 