import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { Button } from '@/components/ui/button'
import { IconPlus } from '@tabler/icons-react'
import { columns, OrdersTable } from './components'
import { orderListSchema } from './data/schema'
import { orders } from './data/data'
import { showSubmittedData } from '@/utils/show-submitted-data'

export default function Orders() {
  // Parse order list
  const orderList = orderListSchema.parse(orders)

  return (
    <>
      <Header fixed>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='mb-2 flex flex-wrap items-center justify-between space-y-2'>
          <div>
            <h2 className='text-2xl font-bold tracking-tight'><PERSON><PERSON><PERSON> hàng</h2>
            <p className='text-muted-foreground'>
              Quản lý và theo dõi đơn hàng của bạn tại đây.
            </p>
          </div>
          <div>
            <Button 
              onClick={() => showSubmittedData({ message: 'Tạo đơn hàng mới' })}
              size="sm"
            >
              <IconPlus className='mr-2 h-4 w-4' />
              Tạo đơn hàng
            </Button>
          </div>
        </div>
        <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12'>
          <OrdersTable data={orderList} columns={columns} />
        </div>
      </Main>
    </>
  )
} 