import { IconMailPlus, IconUserPlus } from '@tabler/icons-react'
import { PrimaryButtonsGroup } from '@/components/ui/primary-buttons-group'
import { useUsers } from '../context/users-context'

export function UsersPrimaryButtons() {
  const { setOpen } = useUsers()

  const buttons = [
    {
      label: 'Invite User',
      icon: IconMailPlus,
      onClick: () => setOpen('invite'),
      variant: 'outline' as const,
    },
    {
      label: 'Add User',
      icon: IconUserPlus,
      onClick: () => setOpen('add'),
    },
  ]

  return <PrimaryButtonsGroup buttons={buttons} />
}
