import { ColumnDef, RowData } from '@tanstack/react-table'
import { DataTable } from '@/components/ui/data-table'
import { User } from '../data/schema'
import { userTypes } from '../data/data'

declare module '@tanstack/react-table' {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  interface ColumnMeta<TData extends RowData, TValue> {
    className: string
  }
}

interface UsersTableProps {
  columns: ColumnDef<User>[]
  data: User[]
}

export function UsersTable({ columns, data }: UsersTableProps) {
  const filters = [
    {
      column: 'status',
      title: 'Trạng thái',
      options: [
        { label: 'Active', value: 'active' },
        { label: 'Invited', value: 'invited' },
        { label: 'Inactive', value: 'inactive' },
      ],
    },
    {
      column: 'role',
      title: 'Vai trò',
      options: userTypes.map((type) => ({
        label: type.label,
        value: type.value,
        icon: type.icon,
      })),
    },
  ]

  return (
    <DataTable
      columns={columns}
      data={data}
      searchKey="username"
      searchPlaceholder="<PERSON><PERSON><PERSON> kiếm người dùng..."
      filters={filters}
    />
  )
}
