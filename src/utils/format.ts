/**
 * Format a number as currency
 * @param value - The number to format
 * @param locale - The locale to use (default: vi-VN)
 * @param currency - The currency to use (default: VND)
 * @returns Formatted currency string
 */
export function formatCurrency(
  value: number,
  locale = 'vi-VN',
  currency = 'VND'
): string {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
  }).format(value);
}

/**
 * Format a date string
 * @param dateString - The date string to format
 * @param locale - The locale to use (default: vi-VN)
 * @returns Formatted date string
 */
export function formatDate(
  dateString: string,
  locale = 'vi-VN'
): string {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat(locale, {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  }).format(date);
}

/**
 * Format a date and time string
 * @param dateString - The date string to format
 * @param locale - The locale to use (default: vi-VN)
 * @returns Formatted date and time string
 */
export function formatDateTime(
  dateString: string,
  locale = 'vi-VN'
): string {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat(locale, {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
}

/**
 * Format a number
 * @param value - The number to format
 * @param locale - The locale to use (default: vi-VN)
 * @returns Formatted number string
 */
export function formatNumber(
  value: number,
  locale = 'vi-VN'
): string {
  return new Intl.NumberFormat(locale).format(value);
}

/**
 * Format a percentage
 * @param value - The number to format as percentage
 * @param locale - The locale to use (default: vi-VN)
 * @returns Formatted percentage string
 */
export function formatPercent(
  value: number,
  locale = 'vi-VN'
): string {
  return new Intl.NumberFormat(locale, {
    style: 'percent',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value / 100);
} 