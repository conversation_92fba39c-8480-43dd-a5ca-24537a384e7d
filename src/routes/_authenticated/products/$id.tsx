import { createFileRoute } from '@tanstack/react-router'
import { ProductDetailPage } from '@/features/products/components/product-detail-page'
import { products } from '@/features/products/data/products'
import { Product } from '@/features/products/data/schema'

export const Route = createFileRoute('/_authenticated/products/$id')({
  component: ProductDetailWrapper,
})

function ProductDetailWrapper() {
  const { id } = Route.useParams()
  const product = products.find(product => product.id === id) as Product

  if (!product) {
    return <div className="p-4">Sản phẩm không tồn tại</div>
  }

  return <ProductDetailPage product={product} />
}
