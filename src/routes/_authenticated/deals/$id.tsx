import { createFileRoute } from '@tanstack/react-router'
import DealDetail from '@/features/deals/components/deal-detail'
import { deals } from '@/features/deals/data/deals'
import { dealListSchema } from '@/features/deals/data/schema'
import DealsProvider from '@/features/deals/context/deals-context'

export const Route = createFileRoute('/_authenticated/deals/$id')({
  component: DealDetailWrapper,
  loader: ({ params }) => {
    const deal = deals.find(d => d.id === params.id)
    if (!deal) {
      throw new Response('Deal not found', { status: 404 })
    }
    return { deal }
  },
  errorComponent: ({ error }) => {
    if (error instanceof Response && error.status === 404) {
      return (
        <div className="flex h-screen flex-col items-center justify-center">
          <h1 className="text-2xl font-bold">404</h1>
          <p className="text-muted-foreground">Deal không tồn tại</p>
        </div>
      )
    }
    return (
      <div className="flex h-screen flex-col items-center justify-center">
        <h1 className="text-2xl font-bold">Đ<PERSON> x<PERSON>y ra lỗi</h1>
        <p className="text-muted-foreground">Không thể tải thông tin deal</p>
      </div>
    )
  },
})

function DealDetailWrapper() {
  const { deal } = Route.useLoaderData()
  const dealList = dealListSchema.parse(deals)
  
  return (
    <div className="h-full overflow-auto">
      <DealsProvider initialDeals={dealList}>
        <DealDetail deal={deal} />
      </DealsProvider>
    </div>
  )
}