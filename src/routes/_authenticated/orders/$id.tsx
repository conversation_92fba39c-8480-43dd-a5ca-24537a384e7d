import { createFileRoute } from '@tanstack/react-router'
import { OrderDetail } from '@/features/orders/components/order-detail'
import { orders } from '@/features/orders/data/data'
import { Order } from '@/features/orders/data/schema'

export const Route = createFileRoute('/_authenticated/orders/$id')({
  component: OrderDetailWrapper,
})

function OrderDetailWrapper() {
  const { id } = Route.useParams()
  const order = orders.find(order => order.id === id) as Order

  if (!order) {
    return <div className="p-4">Đ<PERSON>n hàng không tồn tại</div>
  }

  return <OrderDetail order={order} />
}
