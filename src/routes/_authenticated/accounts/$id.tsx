import { createFileRoute } from '@tanstack/react-router'
import AccountDetail from '@/features/accounts/components/account-detail'
import AccountsProvider from '@/features/accounts/context/accounts-context'
import { accounts } from '@/features/accounts/data/accounts'

export const Route = createFileRoute('/_authenticated/accounts/$id')({
  component: AccountDetailWrapper,
})

function AccountDetailWrapper() {
  const { id } = Route.useParams()
  const account = accounts.find(a => a.id === id)
  if (!account) return <div className="p-4"><PERSON>hông tìm thấy tài khoản với ID: {id}</div>
  return (
    <AccountsProvider initialAccounts={[account]}>
      <AccountDetail account={account} />
    </AccountsProvider>
  )
}
