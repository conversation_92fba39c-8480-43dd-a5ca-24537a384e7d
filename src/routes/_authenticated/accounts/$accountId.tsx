import { createFileRoute } from '@tanstack/react-router'
import { AccountDetail } from '@/features/accounts/components'
import AccountsProvider from '@/features/accounts/context/accounts-context'
import { accounts } from '@/features/accounts/data/accounts'

export const Route = createFileRoute('/_authenticated/accounts/$accountId')({
  component: AccountDetailPage
})

function AccountDetailPage() {
  const { accountId } = Route.useParams()
  const account = accounts.find(a => a.id === accountId)
  
  if (!account) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2">Không tìm thấy tài khoản</h1>
          <p className="text-muted-foreground">Không tìm thấy tài khoản với ID: {accountId}</p>
        </div>
      </div>
    )
  }

  return (
    <AccountsProvider initialAccounts={[account]}>
      <AccountDetail account={account} />
    </AccountsProvider>
  )
}
