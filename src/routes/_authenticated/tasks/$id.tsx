import { createFileRoute } from '@tanstack/react-router'
import TaskDetailPage from '@/features/tasks/components/task-detail-page'
import { tasks } from '@/features/tasks/data/tasks'
import type { Task } from '@/features/tasks/data/schema'

export const Route = createFileRoute('/_authenticated/tasks/$id')({
  component: TaskDetailWrapper,
})

function TaskDetailWrapper() {
  const { id } = Route.useParams()
  const task = tasks.find((t) => t.id === id) as Task
  if (!task) {
    return <div className="p-4">Task not found</div>
  }
  return <TaskDetailPage task={task} />
}
