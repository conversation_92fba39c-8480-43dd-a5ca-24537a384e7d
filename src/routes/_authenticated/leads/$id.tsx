import { createFileRoute } from '@tanstack/react-router'
import LeadDetailSimple from '@/features/leads/components/lead-detail'
import { leads } from '@/features/leads/data/leads'
import { Lead } from '@/features/leads/data/schema'
import LeadsProvider from '@/features/leads/context/leads-context' // Thêm import này

export const Route = createFileRoute('/_authenticated/leads/$id')({
  component: LeadDetailWrapper,
})

function LeadDetailWrapper() {
  const { id } = Route.useParams()
  const lead = leads.find(lead => lead.id === id) as Lead

  if (!lead) {
    return <div className="p-4">Lead không tồn tại</div>
  }

  // Bọc LeadDetailSimple trong LeadsProvider và truyền initialLeads
  return (
    <LeadsProvider initialLeads={leads}>
      <LeadDetailSimple lead={lead} />
    </LeadsProvider>
  )
}