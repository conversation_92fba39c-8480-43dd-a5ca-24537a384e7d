# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependency directories
node_modules/
.pnp/
.pnp.js
.pnp.cjs

# Vite
.vite/

# Build output
dist/
dist-ssr/
build/
out/

# Environment variables
*.local
.env.development.local
.env.test.local
.env.production.local
# .env # Uncomment if you don't want to commit .env file, but ensure .env.example is present

# Unused Lockfiles (project uses yarn.lock)
package-lock.json
pnpm-lock.yaml

# Test results
coverage/
TEST-*.xml
junit.xml
.nyc_output/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
# .DS_Store # Already present
Thumbs.db

# Temporary files
*.tmp
*~
