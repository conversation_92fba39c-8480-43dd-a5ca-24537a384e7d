# Salegon-CRM

**Salegon-CRM** là giải pháp quản lý quan hệ khách hàng (CRM) to<PERSON><PERSON>, đ<PERSON><PERSON><PERSON> xây dựng trên nền tảng **React + TypeScript** với giao diện hiện đại từ **shadcn UI**. <PERSON>ệ thống tập trung vào quy trình bán hàng hiệu quả, giao <PERSON>ện dễ sử dụng, <PERSON>h<PERSON> năng mở rộ<PERSON> linh ho<PERSON>t, phù hợp cho doanh nghiệp vừa và nhỏ.

![Salegon-CRM Screenshot](public/images/shadcn-admin.png)

---

## Tính năng nổi bật

- Quản lý khách hàng ti<PERSON> n<PERSON> (Leads), c<PERSON> hội bán h<PERSON> (Deals), <PERSON><PERSON><PERSON><PERSON> h<PERSON> (Accounts), s<PERSON><PERSON> ph<PERSON>m, đ<PERSON><PERSON> hàng, chiến dịch marketing, mẫu email, trò chuyện đa kênh, báo cáo.
- Giao diện hiện đại, responsive, hỗ trợ dark/light mode.
- <PERSON><PERSON><PERSON><PERSON> lý pipeline b<PERSON> hà<PERSON>, <PERSON><PERSON><PERSON> board, Sales Path.
- Tích hợp giao tiếp đa kênh (Messenger, Zalo).
- <PERSON><PERSON> quyền theo vai trò (Sales, Manager, <PERSON>min).
- Tìm kiếm toàn cục, sidebar điều hướng, profile, thông báo.
- Dữ liệu mẫu phong phú, dễ dàng mở rộng.

---

## Kiến trúc & Module chính

| Module         | Chức năng chính                                                                 | Giao diện/Đặc điểm           | Liên kết dữ liệu                |
|----------------|-------------------------------------------------------------------------------|------------------------------|---------------------------------|
| **Leads**      | Quản lý khách hàng tiềm năng, trạng thái, điểm số, lịch sử tương tác          | Table, Kanban, Sales Path    | Deals, Campaigns, Tasks, Chats  |
| **Deals**      | Quản lý pipeline bán hàng 5 giai đoạn, giá trị, xác suất, khách hàng          | Pipeline, Kanban             | Accounts, Products, Tasks, Campaigns |
| **Accounts**   | Quản lý khách hàng đã xác định, phân loại, lịch sử giao dịch                  | Table                        | Leads, Deals, Orders, Chats     |
| **Products**   | Quản lý danh mục sản phẩm/dịch vụ, tồn kho, biến thể                          | Table                        | Orders, Deals                   |
| **Orders**     | Quản lý đơn hàng, trạng thái, sản phẩm, giá                                   | Table                        | Accounts, Products, Deals       |
| **Tasks**      | Quản lý công việc nội bộ, ưu tiên, trạng thái                                 | Table, Kanban, Gantt         | Leads, Deals, Accounts, Campaigns |
| **Campaigns**  | Quản lý chiến dịch marketing, kênh, ngân sách, mục tiêu                       | Table, Detail                | Leads, Deals, Email Templates   |
| **Email Templates** | Quản lý mẫu email marketing, cá nhân hóa                                 | Table                        | Campaigns                       |
| **Chats**      | Giao tiếp đa kênh với khách hàng (Messenger, Zalo)                            | Omnichannel                  | Leads, Accounts, Tasks, Deals   |
| **Reports**    | Phân tích dữ liệu, hiệu suất, doanh thu, tỷ lệ chuyển đổi                     | Dashboard                    | Tất cả module                   |

### Luồng dữ liệu chính

- **Bán hàng:** Lead → Deal → Order → Account
- **Marketing:** Campaign → Email Template → Lead → Deal
- **Giao tiếp:** Chat → Lead → Deal → Order

### Phân quyền

- **Sales:** Quản lý leads/deals/orders của mình
- **Manager:** Xem tất cả, không xóa
- **Admin:** Toàn quyền

---

## Công nghệ sử dụng

- **Frontend:** React, TypeScript, shadcn UI (TailwindCSS + RadixUI)
- **Routing:** TanStack Router
- **State/Context:** React Context, custom hooks, stores
- **Build Tool:** Vite
- **Icons:** Tabler Icons
- **Lint/Format:** ESLint, Prettier
- **Testing:** Playwright (có thể mở rộng)
- **Quản lý gói:** pnpm (ưu tiên), yarn, npm đều hỗ trợ

---

## Hướng dẫn cài đặt & chạy dự án

1. **Clone dự án**
   ```bash
   git clone <repo-url>
   cd Salegon-CRM
   ```

2. **Cài đặt dependencies**
   ```bash
   pnpm install
   # hoặc yarn install, hoặc npm install
   ```

3. **Chạy ứng dụng**
   ```bash
   pnpm run dev
   # hoặc yarn dev, hoặc npm run dev
   ```

4. **Truy cập:**  
   Mở trình duyệt tại [http://localhost:5173](http://localhost:5173) (mặc định của Vite).

---

## Tùy chỉnh & Mở rộng

- Dễ dàng thêm module mới (ví dụ: Payment, SMS, KPI Reports).
- Có thể tích hợp API backend thực tế (hiện tại dùng mock data).
- Hỗ trợ đa ngôn ngữ, dễ dàng tuỳ biến giao diện.
- Có thể triển khai lên Netlify, Vercel, hoặc server riêng.

---

## Đóng góp & Giấy phép

- **Tác giả:** Crafted with 🤍 by Salegon Team
- **License:** MIT

---

## Định hướng phát triển

- Ưu tiên phát triển: Leads, Deals, Orders, Products, Campaigns, Chats, Accounts, Reports.
- Giản lược: Không tự động hóa phức tạp, pipeline cố định, chỉ hỗ trợ Email/Messenger/Zalo.
- Tiềm năng mở rộng: Tích hợp cổng thanh toán, thêm kênh liên lạc (SMS, Facebook), báo cáo KPI phức tạp.

---

**Salegon-CRM** – Nền tảng CRM hiện đại, linh hoạt, dễ mở rộng cho doanh nghiệp Việt!

---

Bạn có thể copy nội dung này để thay thế README.md hiện tại. Nếu muốn bản tiếng Anh, hãy yêu cầu!
